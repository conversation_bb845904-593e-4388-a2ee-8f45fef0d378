<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>单位管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script type="text/javascript" th:src="@{/scripts/dic/workUnit/list.js}"></script>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <a style="margin-right:5px;"  href="javascript:void(0);" id="addinfo">
                <button class="layui-btn layui-btn-sm">
                    <i class="layui-icon layui-icon-add"></i>增加
                </button>
            </a>
           <!-- <a style="margin-right:5px;" href="javascript:void(0);" id="editinfo">
                <button class="layui-btn layui-btn-sm  layui-btn-normal">
                    <i class="layui-icon layui-icon-edit"></i>编辑
                </button>
            </a>-->
         <!--   <a href="javascript:void(0);" id="deleteinfo">
                <button class="layui-btn layui-btn-danger layui-btn-sm">
                    <i class="layui-icon layui-icon-delete"></i>删除
                </button>
            </a>-->
            <table class="layui-table layui-form treeTable" lay-filter="tree-table" id="tree-table"></table>

        </div>
    </div>

</div>

</body>
</html>
