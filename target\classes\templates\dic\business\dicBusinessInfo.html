<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="utf-8">
<title>添加部门</title>
<link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}"
	media="all">
<link rel="stylesheet"
	th:href="@{/plugins/formSelects/formSelects-v4.css}" />
<script th:src="@{/admin/layui/layui.js}" charset="utf-8"></script>
<script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
<script type="text/javascript" th:src="@{/scripts/dic/business/businessAdd.js}"></script>
<style>
.layui-form-item .layui-input-inline{
	width : 260px;
}
</style>
</head>
<body>
	<div class="layui-fluid">
		<div class="layui-card">
			<div class="layui-card-body">
				<fieldset class="layui-elem-field layui-field-title"
					style="margin-top: 20px;">
					<legend>业务信息</legend>
				</fieldset>

				<form class="layui-form" action="">
					<input type="hidden" name="id" th:value="${business?.id}"/>
					<input type="hidden" name="orderCode" th:value="${business?.orderCode}"/>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">业务名称：</label>
							<div class="layui-input-inline">
								<input type="tel" name="businessName" lay-verify="required"
									autocomplete="off" class="layui-input" th:value="${business?.businessName}">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">上级id：</label>
							<div class="layui-input-inline">
								<input type="hidden" value="" name="parentId" id="parentId" th:value="${business?.parentId}"/>
								<input type="hidden" value="" name="parentName" id="parentName"/>
								<select name="city" xm-select="example6_4" xm-select-search="" xm-select-radio="" xm-select-height="36px" >
									<option th:value="${business?.parentId}"></option>
								</select>
							</div>
						</div>
					</div>

					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">描述：</label>
							<div class="layui-input-inline">
								<input type="text" name="describe" autocomplete="off"
									class="layui-input" lay-verify="required" th:value="${business?.describe}">
							</div>
						</div>
						
						<div class="layui-inline">
		                      <label class="layui-form-label">业务种类</label>
		                      <div class="layui-input-inline">
		                              <select id="businessType" lay-verify="required" name="businessType">
		                                  <option value="">请选择参数类别</option>
							  				<option value="100" th:selected="${business?.businessType==100}">交警业务</option>
                              				<option value="1001" th:selected="${business?.businessType==1001}">交警驾驶证业务</option>                                                                    
                              				<option value="1001001" th:selected="${business?.businessType==1001001}">交警驾驶证丢失业务</option>      
                              				<option value="1001002" th:selected="${business?.businessType==1001002}">交警驾驶证损坏业务</option>      
                              				<option value="1001003" th:selected="${business?.businessType==1001003}">交警驾驶证到期业务</option>      
                              				<option value="1001004" th:selected="${business?.businessType==1001004}">交警驾驶证转入业务</option>      
                              				<option value="1001005" th:selected="${business?.businessType==1001005}">交警驾驶证自助制证业务</option>      
                              				<option value="1001006" th:selected="${business?.businessType==1001006}">交警驾驶证变更驾驶人联系方式</option>      
                              				<option value="1001007" th:selected="${business?.businessType==1001007}">交警驾驶证基本信息查询</option>      
                              				<option value="1002" th:selected="${business?.businessType==1002}">机动车业务</option>      
                              				<option value="1002001" th:selected="${business?.businessType==1002001}">机动车六合一免检业务</option>      
                              				<option value="1002002" th:selected="${business?.businessType==1002002}">机动车补领换证-号牌补领</option>      
                              				<option value="1002003" th:selected="${business?.businessType==1002003}">机动车补领换证-号牌换领</option>      
                              				<option value="1002004" th:selected="${business?.businessType==1002004}">机动车补领换证-行驶证补领</option>      
                              				<option value="1002005" th:selected="${business?.businessType==1002005}">机动车补领换证-行驶证换领</option>      
                              				<option value="1002006" th:selected="${business?.businessType==1002006}">机动车补领检验合格标志</option>      
                              				<!-- <option value="1002007" th:selected="${business?.businessType==1002007}">机动车非本人机动车备案</option> -->      
                              				<option value="1002008" th:selected="${business?.businessType==1002008}">机动车基本信息查询</option>      
                              				<option value="1003001" th:selected="${business?.businessType==1003001}">机动车违法处理业务</option>      
                              				<option value="1003002" th:selected="${business?.businessType==1003002}">机动车违法查询业务</option>      
		                              </select>
		                      </div>
                  		</div>
						
					</div>
					
					<!-- <div class="layui-form-item">
						<div class="layui-inline">
							  <label class="layui-form-label">业务须知</label>
							  <div class="layui-input-inline">
								  <textarea id="notice" name="notice" th:text="${business?.notice}" placeholder="业务须知文档，请添加标题及内容"  class="layui-textarea" disabled></textarea>
							  </div>
				  		</div>
				  		<div class="layui-inline">
				  				<div class="layui-form-item">
							  <label class="layui-form-label">标题</label>
							  <div class="layui-input-inline">
								<input type="text" name="title" autocomplete="off" id="title"
									class="layui-input" >
							  </div>
								<button type="button" class="layui-btn layui-btn-sm" id="titleAdd"><i class="layui-icon">&#xe654;</i>添加</button>
							  </div>
							  <div class="layui-form-item">
							  <label class="layui-form-label">内容</label>
							  <div class="layui-input-inline">
								<input type="text" name="content" autocomplete="off" id="content"
									class="layui-input" >
							  </div>
							  <button type="button" class="layui-btn layui-btn-sm" id="contentAdd"><i class="layui-icon">&#xe654;</i>添加</button>
							  </div>
				  		</div>
					</div> -->
					
					<div class="layui-form-item">
							<div class="layui-upload" th:if="${business == null}">
								  <label class="layui-form-label"><button type="button" class="layui-btn" lay-type="images" id="pictureBtn">上传图标</button></label>
								  <div class="layui-upload-list">
								    <img class="layui-upload-img" style="width:60px;" id="picture" src="../images/none.jpg">
								    <input type="hidden" name="icoImgStr" id="picture-hid">
								    <p id="demoText"></p>
								  </div>
							</div>
							
							<div class="layui-upload" th:if="${business != null}">
								  <label class="layui-form-label"><button type="button" class="layui-btn" lay-type="images" id="pictureBtn">上传图标</button></label>
								  <div class="layui-upload-list">
								    <img th:if="${business.icoImgStr != null }" class="layui-upload-img" style="width:60px;" id="picture" th:src="${'data:image/png;base64,' + business.icoImgStr}">
								    <img th:if="${business.icoImgStr == null }" class="layui-upload-img" style="width:60px;" id="picture" src="../images/none.jpg">
								    <input type="hidden" name="icoImgStr" id="picture-hid" th:value="${'data:image/png;base64,'+business.icoImgStr}">
								    <p id="demoText"></p>
								  </div>
							</div>
					</div>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">是否启用：</label>
							<div class="layui-input-inline" th:if="${business!=null&&business.isShow==1}">
								<input type="checkbox" name="isShow" lay-skin="switch" checked=""
									lay-text="是|否">
							</div>
							<div class="layui-input-inline" th:if="${business==null||(business!=null&&business.isShow==0)}">
								<input type="checkbox" name="isShow" lay-skin="switch"
									lay-text="是|否">
							</div>
						</div>
					</div>
					<div class="layui-form-item">
					<label class="layui-form-label">业务须知：</label>
					<div class ="layui-row-11" >
					    	<textarea id="notice" name="notice" th:text="${business?.notice}"></textarea>
					</div>
					</div>
					
					<div class="layui-form-item">
						<div class="layui-input-block">
							<button class="layui-btn" lay-filter="demo1" lay-submit="">保存</button>
							<button type="reset" class="layui-btn layui-btn-primary" th:if="${dept==null}">重置</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	
  <script th:inline="javascript">
  	var ctx = [[${basePath}]];
	var top_value = [[${top_value}]];
	businessAdd(ctx,top_value);
  </script>
</body>
</html>
