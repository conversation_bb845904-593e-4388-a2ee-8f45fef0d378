/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.2 (2020-08-17)
 */
!function(){"use strict";var n,t,e,r=tinymce.util.Tools.resolve("tinymce.PluginManager"),l=tinymce.util.Tools.resolve("tinymce.util.Tools"),c=function(n,t,e){var r="UL"===t?"InsertUnorderedList":"InsertOrderedList";n.execCommand(r,!1,!1===e?null:{"list-style-type":e})},i=function(n){return function(){return n}},u=i(!1),s=i(!0),o=function(){return a},a=(n=function(n){return n.isNone()},{fold:function(n,t){return n()},is:u,isSome:u,isNone:s,getOr:e=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:i(null),getOrUndefined:i(undefined),or:e,orThunk:t,map:o,each:function(){},bind:o,exists:u,forall:s,filter:o,equals:n,equals_:n,toArray:function(){return[]},toString:i("none()")}),f=function(e){var n=i(e),t=function(){return o},r=function(n){return n(e)},o={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:s,isNone:u,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return f(n(e))},each:function(n){n(e)},bind:r,exists:r,forall:r,filter:function(n){return n(e)?o:a},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(u,function(n){return t(e,n)})}};return o},d=function(n){return null===n||n===undefined?a:f(n)},g=function(n){return n&&/^(TH|TD)$/.test(n.nodeName)},m=function(r){return function(n){return n&&/^(OL|UL|DL)$/.test(n.nodeName)&&(e=n,(t=r).$.contains(t.getBody(),e));var t,e}},p=function(n,t,e){var r=function(n,t){for(var e=0;e<n.length;e++){if(t(n[e]))return e}return-1}(t.parents,g),o=-1!==r?t.parents.slice(0,r):t.parents,i=l.grep(o,m(n));return 0<i.length&&i[0].nodeName===e},y=function(o,n,t,e,r,i){o.ui.registry.addSplitButton(n,{tooltip:t,icon:"OL"===r?"ordered-list":"unordered-list",presets:"listpreview",columns:3,fetch:function(n){n(l.map(i,function(n){return{type:"choiceitem",value:"default"===n?"":n,icon:"list-"+("OL"===r?"num":"bull")+"-"+("disc"===n||"decimal"===n?"default":n),text:n.replace(/\-/g," ").replace(/\b\w/g,function(n){return n.toUpperCase()})}}))},onAction:function(){return o.execCommand(e)},onItemAction:function(n,t){c(o,r,t)},select:function(t){var n,e,r;return(e=(n=o).dom.getParent(n.selection.getNode(),"ol,ul"),r=n.dom.getStyle(e,"listStyleType"),d(r)).map(function(n){return t===n}).getOr(!1)},onSetup:function(t){var n=function(n){t.setActive(p(o,n,r))};return o.on("NodeChange",n),function(){return o.off("NodeChange",n)}}})},v=function(n,t,e,r,o,i){var u,l,c,s,a;1<i.length?y(n,t,e,r,o,i):(l=t,c=e,s=r,a=o,(u=n).ui.registry.addToggleButton(l,{active:!1,tooltip:c,icon:"OL"===a?"ordered-list":"unordered-list",onSetup:function(t){var n=function(n){t.setActive(p(u,n,a))};return u.on("NodeChange",n),function(){return u.off("NodeChange",n)}},onAction:function(){return u.execCommand(s)}}))};!function O(){r.add("advlist",function(n){var e,t,r,o,i,u;i=n,u="lists",-1!==l.inArray(i.getParam("plugins","","string").split(/[ ,]/),u)&&(v(t=n,"numlist","Numbered list","InsertOrderedList","OL",(r=t.getParam("advlist_number_styles","default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman"))?r.split(/[ ,]/):[]),v(t,"bullist","Bullet list","InsertUnorderedList","UL",(o=t.getParam("advlist_bullet_styles","default,circle,square"))?o.split(/[ ,]/):[]),(e=n).addCommand("ApplyUnorderedListStyle",function(n,t){c(e,"UL",t["list-style-type"])}),e.addCommand("ApplyOrderedListStyle",function(n,t){c(e,"OL",t["list-style-type"])}))})}()}();