<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>非法中介防控综合系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/homePage/css/home.css}">
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <style>
        body {
            background-color: #101A39;
            color: #fff;
            margin: 0;
            padding: 0;
            font-family: "Microsoft YaHei", sans-serif;
            min-width: 1050px; /* 设置最小宽度 */
            overflow-x: auto; /* 允许水平滚动 */
            background-image: radial-gradient(circle at 50% 50%, rgba(0, 40, 80, 0.3) 0%, rgba(0, 23, 52, 1) 80%);
            background-size: cover;
            background-position: center;
            background-attachment: fixed; /* 固定背景 */
        }

        .content-container {
            display: flex;
            width: 100%;
            min-width: 1050px; /* 设置最小宽度 */
            padding: 0 10px;
            margin: 20px 0;
            background: #101A39;
            box-sizing: border-box;
        }

        .left-column {
            width: 30%;
            min-width: 300px; /* 设置最小宽度 */
            background: rgba(0,34,77,0.9);
            padding: 0 6px;
            margin: 0 8px;
            flex-shrink: 0; /* 防止压缩 */
        }

        .right-column {
            width: 68%;
            /*min-width: 800px; !* 设置最小宽度 *!*/
            background: rgba(0,34,77,0.9);
            /*padding: 0 6px;*/
            /*margin: 0 8px;*/
            flex-shrink: 0; /* 防止压缩 */
        }

        .history-container {
            display: flex;
            width: 100%;
            min-width: 700px; /* 设置最小宽度 */
        }

        .history-left {
            width: 53%;
            min-width: 385px; /* 设置最小宽度 */
            padding: 0 5px;
            box-sizing: border-box;
            flex-shrink: 0; /* 防止压缩 */
        }

        .history-right {
            width: 47%;
            min-width: 315px; /* 设置最小宽度 */
            padding: 0 5px;
            box-sizing: border-box;
            flex-shrink: 0; /* 防止压缩 */
        }

        .table-container, .frequency-table {
            width: 100%;
            overflow-x: auto;
            overflow-y: auto;
            min-height: 238px;
            max-height: 238px;
        }

        .chart-container {
            height: 260px;
            width: 100%;
            min-width: 300px;
            position: relative;
        }

        /*.alert-cards {*/
        /*    display: flex;*/
        /*    flex-wrap: nowrap;*/
        /*    justify-content: space-between;*/
        /*    align-items: center;*/
        /*    min-width: 280px;*/
        /*    overflow-x: auto;*/
        /*    overflow-y: hidden;*/
        /*}*/

        /* 统一的滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 60, 120, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 160, 233, 0.5);
            border-radius: 3px;
            border: 1px solid rgba(0, 160, 233, 0.2);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 160, 233, 0.7);
        }

        ::-webkit-scrollbar-corner {
            background: rgba(0, 60, 120, 0.1);
        }

        /* 移除所有媒体查询 */

        .card-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: nowrap;
            gap: 20px;
            width: 100%;
            min-width: 280px;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .metric-card {
            flex: 0 0 150px;
            min-width: 150px;
            max-width: 200px;
            margin: 0;
        }

        .stat-cards {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-around;
            margin: 10px 0;
            min-width: 300px;
            overflow-x: auto;
        }

        .stat-card {
            flex: 0 0 120px;
            min-width: 120px;
            margin: 0 10px;
        }

        /*!* 保持表格布局稳定 *!*/
        /*.data-table {*/
        /*    width: 100%;*/
        /*    table-layout: fixed;*/
        /*    border-collapse: collapse;*/
        /*    min-width: 500px; !* 设置最小宽度 *!*/
        /*}*/

        /*.data-table th, .data-table td {*/
        /*    white-space: nowrap;*/
        /*    overflow: hidden;*/
        /*    text-overflow: ellipsis;*/
        /*    padding: 8px;*/
        /*}*/

        /* 全屏模式样式 */
        body.fullscreen-mode {
            padding-left: 0 !important;
        }
        .header {
            height: 70px;
            background-image: url("../../../static/homePage/images/bigTitle.png");
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin-bottom: 10px;
            border-bottom: 1px solid #00a0e9;
            box-shadow: 0 0 15px rgba(0, 160, 233, 0.3);
        }
        .header::before, .header::after {
            content: "";
            position: absolute;
            width: 100px;
            height: 30px;
        }
        .header::before {
            left: 10%;
            top: 10px;
            border-top: 3px solid #00ffff;
            border-left: 3px solid #00ffff;
        }
        .header::after {
            right: 10%;
            bottom: 10px;
            border-bottom: 3px solid #00ffff;
            border-right: 3px solid #00ffff;
        }
        .header h1 {
            font-size: 36px;
            color: #fff;
            text-shadow: 0 0 10px #00a0e9, 0 0 20px #00a0e9;
            margin: 0;
            position: relative;
        }
        .header h1::before, .header h1::after {
            content: "";
            position: absolute;
            width: 200px;
            height: 4px;
            background-color: #00ffff;
            box-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff;
        }
        .header h1::before {
            left: -220px;
            top: 50%;
            transform: translateY(-50%);
        }
        .header h1::after {
            right: -220px;
            top: 50%;
            transform: translateY(-50%);
        }
        .panel {
            border: 1px solid #004c7d;
            border-radius: 5px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 76, 153, 0.3);
        }
        .panel::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(0, 160, 233, 0) 0%, rgba(0, 160, 233, 0.7) 50%, rgba(0, 160, 233, 0) 100%);
        }
        .panel-border {
            position: absolute;
            height: 100%;
            width: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-size: 8px 1px, 8px 1px, 1px 8px, 1px 8px;
            background-position: 0 0, 0 100%, 0 0, 100% 0;
        }
        .panel-header {
            background-image: linear-gradient(to right, rgba(0, 70, 140, 0.7) 0%, rgba(0, 40, 80, 0.3) 100%);
            color: #00a0e9;
            padding: 8px 15px;
            font-size: 16px;
            position: relative;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            font-weight: bold;
        }
        .panel-header::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: #00a0e9;
            box-shadow: 0 0 5px #00a0e9, 0 0 10px #00a0e9;
        }
        .panel-content {
            padding: 15px;
            overflow-x: auto;
            overflow-y: hidden;
            min-width: 0;
        }
        .panel-card-container {
            padding: 15px;
            min-width: 0;
            width: 100%;
            box-sizing: border-box;
        }
        /*.title_icon {*/
        /*    font-size: 16px;*/
        /*    color: #00a0e9;*/
        /*    margin-bottom: 10px;*/
        /*    position: relative;*/
        /*    display: flex;*/
        /*    align-items: center;*/
        /*    justify-content: center;*/
        /*}*/
        /*.title_icon::before, .title_icon::after {*/
        /*    content: "";*/
        /*    height: 1px;*/
        /*    background: linear-gradient(90deg, rgba(0, 160, 233, 0) 0%, rgba(0, 160, 233, 1) 50%, rgba(0, 160, 233, 0) 100%);*/
        /*    flex: 1;*/
        /*    margin: 0 15px;*/
        /*}*/
        .section-title {
            color: #00a0e9;
            font-size: 16px;
            text-align: center;
            margin: 10px 0;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .section-title::before, .section-title::after {
            content: "";
            height: 1px;
            background: linear-gradient(90deg, rgba(0, 160, 233, 0) 0%, rgba(0, 160, 233, 1) 50%, rgba(0, 160, 233, 0) 100%);
            flex: 1;
            margin: 0 15px;
        }
        .table-container table {
            min-width: 350px;
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }
        .table-container thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: rgba(0, 70, 140, 0.5);
        }
        /*.table-container tbody td {*/
        /*    vertical-align: middle;*/
        /*}*/
        .table-container tr:hover {
            background-color: rgba(0, 160, 233, 0.2);
        }
        .data-table {
            width: 100%;
            border-spacing: 0;
            color: #fff;
            border-collapse: collapse;
            min-width: 500px;
        }
        .data-table th {
            background-color: rgba(0, 70, 140, 0.5);
            color: #00a0e9;
            font-weight: normal;
            border: 1px solid rgba(0, 76, 153, 0.3);
        }
        .data-table td {
            text-align: center;
            border: 1px solid rgba(0, 76, 153, 0.3);
        }
        .data-table tr:nth-child(odd) {
            background-color: rgba(0, 60, 120, 0.2);
        }
        .data-table tr:nth-child(even) {
            background-color: rgba(0, 60, 120, 0.1);
        }
        .frequency-table {
            max-height: 352px;
            overflow-y: auto;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 160, 233, 0.5) rgba(0, 60, 120, 0.1);
        }
        .frequency-table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: rgba(0, 70, 140, 0.5);
        }
        .frequency-table::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .frequency-table::-webkit-scrollbar-track {
            background: rgba(0, 60, 120, 0.1);
            border-radius: 3px;
        }
        .frequency-table::-webkit-scrollbar-thumb {
            background: rgba(0, 160, 233, 0.5);
            border-radius: 3px;
            border: 1px solid rgba(0, 160, 233, 0.2);
        }
        .frequency-table::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 160, 233, 0.7);
        }
        .frequency-table::-webkit-scrollbar-corner {
            background: rgba(0, 60, 120, 0.1);
        }
        .frequency-table tr:hover {
            background-color: rgba(0, 160, 233, 0.2);
        }
        .blue-tag, .green-tag, .yellow-tag, .white-tag, .black-tag {
            color: black;
            padding: 2px 4px;
            border-radius: 4px;
            display: inline-block;
            height: 15px;
            width: 75px;
            font-weight: 400;
        }
        .blue-tag {
            color: white;
            background-color: #3e6fdb;
            box-shadow: 0 0 5px #0067c0;
            height: 15px;
            width: 75px;
        }
        .green-tag {
            background-color: #83ea7d;
            box-shadow: 0 0 5px #83ea7d;
            height: 15px;
            width: 75px;
        }
        .yellow-tag {
            background-color: #dbab29;
            box-shadow: 0 0 5px #dbab29;
            height: 15px;
            width: 75px;
        }
        .white-tag {
            background-color: #ffffff;
            color: #000;
            box-shadow: 0 0 5px #ffffff;
            height: 15px;
            width: 75px;
        }
        .black-tag {
            background-color: #000000;
            box-shadow: 0 0 5px #666666;
            height: 15px;
            color: white;
            width: 75px;
        }
        .tab-buttons {
            display: flex;
            justify-content: flex-end;
            margin-right: 20px;
        }
        .tab-button, .tab-button1 {
            padding: 3px 10px;
            margin: 0 2px;
            background-color: rgba(0, 60, 120, 0.5);
            border: 1px solid #004c7d;
            color: white;
            border-radius: 3px;
            cursor: pointer;
        }
        .tab-button.active, .tab-button1.active {
            background-color: #00a0e9;
            box-shadow: 0 0 5px #00a0e9;
        }
        .stat-cards {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-around;
            margin: 10px 0;
            min-width: 300px;
            overflow-x: auto;
        }
        .stat-card {
            flex: 0 0 120px;
            min-width: 120px;
            margin: 0 10px;
        }
        .stat-card::after {
            content: "";
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(0, 160, 233, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
            opacity: 0.5;
        }
        .stat-icon {
            font-size: 36px;
            color: #00a0e9;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }
        .chart-container {
            height: 260px;
            width: 100%;
            position: relative;
            min-width: 300px;
        }
        .alert-cards {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-items: center;
            min-width: 280px;
            overflow-x: auto;
            overflow-y: hidden;
        }
        .alert-cards::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .alert-cards::-webkit-scrollbar-track {
            background: rgba(0, 60, 120, 0.1);
            border-radius: 3px;
        }
        .alert-cards::-webkit-scrollbar-thumb {
            background: rgba(0, 160, 233, 0.5);
            border-radius: 3px;
            border: 1px solid rgba(0, 160, 233, 0.2);
        }
        .alert-cards::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 160, 233, 0.7);
        }
        .alert-cards::-webkit-scrollbar-corner {
            background: rgba(0, 60, 120, 0.1);
        }
        .alert-card {
            width: 23.5%;
            min-width: 90px;
            margin: 0 5px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .alert-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 5px;
            font-size: 20px;
            position: relative;
        }
        .alert-icon.blue {
            background-color: rgba(0, 103, 192, 0.3);
            color: #00a0e9;
            border: 1px solid #00a0e9;
        }
        .alert-icon.gold {
            background-color: rgba(255, 187, 0, 0.3);
            color: #ffbb00;
            border: 1px solid #ffbb00;
        }
        .alert-icon.red {
            background-color: rgba(255, 0, 0, 0.3);
            color: #ff0000;
            border: 1px solid #ff0000;
            box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
        }
        .alert-icon.warning {
            background-color: rgba(255, 153, 0, 0.3);
            color: #ff9900;
            border: 1px solid #ff9900;
            box-shadow: 0 0 10px rgba(255, 153, 0, 0.5);
        }
        .alert-icon.inactive {
            background-color: rgba(150, 150, 150, 0.3);
            color: #aaaaaa;
            border: 1px solid #aaaaaa;
        }
        .alert-value {
            font-size: 18px;
            color: #00a0e9;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(0, 160, 233, 0.3);
        }
        .menu-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #00ffff;
            font-size: 16px;
            background-color: rgba(0, 36, 76, 0.8);
            border: 1px solid #0a4c82;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .menu-btn:hover {
            background-color: #0a4c82;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
        .menu-btn i {
            margin-right: 5px;
        }
        .no-btn-skin .layui-layer-btn .layui-layer {
            display: none !important;
        }
        .metric-label {
            font-size: 11px;
            color: #99ccff;
            margin-bottom: 5px;
        }
        .xm-select-demo{
            color: #00ffff;
            font-size: 16px;
            border-radius: 4px;
            /*padding: 6px 12px;*/
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            transition: all 0.3s ease;
            text-decoration: none;
            width: 160px; /* 添加固定宽度 */
        }

    </style>
    <style id="xm-select-custom-style">
        /* 直接针对xm-select组件的样式，使用更高优先级 */
        .xm-select {
            background-color: rgba(0, 36, 76, 0.8) !important;
            border: 1px solid #0a4c82 !important;
        }

        .xm-select .xm-label .xm-label-block {
            height: 21px !important;
            line-height: 21px !important;
        }

        xm-select {
            min-height: 30px !important;
            line-height: 30px !important;
        }

        .xm-select > * {
            background-color: rgba(0, 36, 76, 0.8) !important;
        }

        .xm-select-title {
            background-color: rgba(0, 36, 76, 0.8) !important;
            border-color: #0a4c82 !important;
        }

        .xm-select-title * {
            color: #00ffff !important;
        }

        .xm-select--suffix > i {
            color: #00ffff !important;
        }

        .xm-select-dropdown {
            background-color: rgba(0, 36, 76, 0.95) !important;
            border: 1px solid #0a4c82 !important;
            box-shadow: 0 0 10px rgba(0, 160, 233, 0.3) !important;
        }

        .xm-select-dropdown .xm-select-dropdown-content .xm-option {
            color: #00ffff !important;
        }

        .xm-select-dropdown .xm-select-dropdown-content .xm-option:hover {
            background-color: rgba(0, 160, 233, 0.2) !important;
        }

        .xm-select-dropdown .xm-select-dropdown-content .xm-option.selected {
            background-color: rgba(0, 160, 233, 0.3) !important;
        }

        .xm-select .xm-label .xm-label-block {
            background-color: rgba(0, 160, 233, 0.3) !important;
            border: 1px solid #00a0e9 !important;
            color: #00ffff !important;
        }

        .xm-select .xm-label .xm-label-block i {
            color: #00ffff !important;
        }

        .xm-form-selected .xm-select {
            border-color: #00a0e9 !important;
        }
    </style>
    <script type="text/javascript">
        if (top.location != location) {
            top.location.href = location.href;
        }
    </script>
</head>

<body>
<!-- 头部标题 -->
<div class="big_title">
    <a href="javascript:void(0)" class="menu-btn" id="toggleMenu">
        进入菜单
    </a>
    <h1>非法中介防控综合系统</h1>
</div>

<div class="content-container">
    <!-- 左侧面板 - 今日数据预览 (30%) -->
    <div class="left-column">
        <!-- 今日数据预览 -->
        <div class="section-title1">·今日数据预览·</div>

        <!-- 已处理数量展示 -->
        <div class="panel">
            <div class="panel-border"></div>
            <div class="title_icon">已处理数量展示</div>
            <div class="panel-card-container">
                <div class="card-container">
                    <div class="metric-card">
                        <div class="car_processed_value_icon circle-animation">
                            <div class="blacklist-value-container">
                                <div class="metric-label">车辆处理数</div>
                                <div id="carProcessedValue" class="bl-wl-value" data-value="23445">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="person_processed_value_icon circle-animation">
                            <div class="whitelist-value-container">
                                <div class="metric-label">人员处理数</div>
                                <div id="personProcessedValue" class="bl-wl-value" data-value="54322">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时预警列表 -->
        <div class="panel">
            <div class="panel-border"></div>
            <div class="title_icon">
                实时预警列表
                <div class="tab-buttons" style="display: inline-block; float: right; margin: 0;">
                    <button class="tab-button active" data-type="2">车辆预警</button>
                    <button class="tab-button" data-type="1">人像预警</button>
                </div>
            </div>
            <div class="panel-content">
                <div class="table-container">
                    <table class="data-table" id="alertTable">
                        <thead>
                        <tr>
                            <th id="alertTypeHeader">车牌号</th>
                            <th>预警类型</th>
                            <th>出现时间</th>
                            <th>处理</th>
                        </tr>
                        </thead>
                        <tbody>
                        <!-- 表格数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 预警情况 -->
        <div class="panel">
            <div class="panel-border"></div>
            <div class="title_icon">预警情况</div>
            <div class="panel-card-container">
                <div class="alert-cards">
                    <div class="alert-card">
                        <div class="suspected_alert_value_icon circle-animation">
                            <div class="alert-icon-container" id="btn-warn1">
                                <div class="alert-value-title">黑名单人员预警</div>
                                <div id="suspectedAlertValue" class="alert-value-number" data-value="3121">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="alert-card">
                        <div class="suspected_processed_value_icon circle-animation">
                            <div class="alert-icon-container" id="btn-warn2">
                                <div class="alert-value-title">疑似人员预警</div>
                                <div id="suspectedProcessedValue" class="alert-value-number2" data-value="1213">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="alert-card">
                        <div class="blacklist_alert_value_icon circle-animation">
                            <div class="alert-icon-container" id="btn-warn3">
                                <div class="alert-value-title">黑名单车辆预警</div>
                                <div id="blacklistAlertValue" class="alert-value-number" data-value="34">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="alert-card">
                        <div class="blacklist_processed_value_icon circle-animation">
                            <div class="alert-icon-container" id="btn-warn4">
                                <div class="alert-value-title">疑似车辆预警</div>
                                <div id="blacklistProcessedValue" class="alert-value-number2" data-value="22122">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧面板 - 历史数据统计 (70%) -->
    <div class="right-column">
        <!-- 历史数据统计 -->
        <div class="section-title2" style="display: flex; justify-content: space-between; align-items: center;position: relative;">
            <div style="width: 160px;"></div> <!-- 左侧占位元素，与右侧下拉框宽度相同 -->
            <span style="position: absolute; left: 50%; transform: translateX(-50%);">·历史数据统计·</span>
            <div class="xm-select-demo" id="typeSelect" style="position: static; margin-right: 15px;"></div>
        </div>

<!--        <div class="section-title2" style="position: relative; text-align: center; padding: 10px 0;">-->
<!--            <span>·历史数据统计·</span>-->
<!--            <div class="xm-select-demo" id="typeSelect" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%);"></div>-->
<!--        </div>-->
<!--        <div class="section-title2" >·历史数据统计·</div>-->
        <div class="history-container">
            <!-- 历史数据左半部分 -->
            <div class="history-left">
                <!-- 人脸预警统计 -->
                <div class="panel">
                    <div class="panel-border"></div>
                    <div class="title_icon">人脸频次统计</div>
                    <div class="panel-content">
                        <div class="frequency-table">
                            <table class="data-table" id="faceAlertTable">
                                <thead>
                                <tr>
                                    <th width="10%">序号</th>
                                    <th width="20%">头像</th>
                                    <th width="15%">出现次数</th>
                                    <th width="30%">最后出现时间</th>
                                    <th width="25%">位置</th>
                                </tr>
                                </thead>
                                <tbody id="btn-person">
                                <!-- 表格数据将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 车辆预警统计 -->
                <div class="panel">
                    <div class="panel-border"></div>
                    <div class="title_icon">车牌频次统计</div>
                    <div class="panel-content">
                        <div class="frequency-table">
                            <table class="data-table" id="carAlertTable">
                                <thead>
                                <tr>
                                    <th width="8%">序号</th>
                                    <th width="18%">车牌号</th>
                                    <th width="12%">颜色</th>
                                    <th width="14%">出现次数</th>
                                    <th width="25%">最后出现时间</th>
                                    <th width="23%">位置</th>
                                </tr>
                                </thead>
                                <tbody id="btn-car">
                                <!-- 表格数据将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 历史数据右半部分 -->
            <div class="history-right">
                <!-- 车辆统计信息 -->
                <div class="panel">
                    <div class="panel-border"></div>
                    <div class="title_icon">预警数据处理统计</div>
                    <div class="tab-buttons">
                        <button class="tab-button1 active" data-period="week">周</button>
                        <button class="tab-button1" data-period="month">月</button>
                        <button class="tab-button1" data-period="year">年</button>
                    </div>
                    <div class="panel-card-container">
                        <div class="stat-cards">
                            <div class="stat-card">
                                <div class="processed-total-icon circle-animation">
                                    <div class="stat-icon-container">
                                        <div class="stat-title">处理总数</div>
                                        <div class="stat-value">543436</div>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="processed-car-icon circle-animation">
                                    <div class="stat-icon-container">
                                        <div class="stat-title">车辆处理数</div>
                                        <div class="stat-value">2134</div>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="processed-person-icon circle-animation">
                                    <div class="stat-icon-container">
                                        <div class="stat-title">人员处理数</div>
                                        <div class="stat-value">34</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 黑白名单数量 -->
                <div class="panel">
                    <div class="panel-border"></div>
                    <div class="title_icon">黑、白名单数量展示</div>
                    <div class="panel-card-container">
                        <div class="card-container">
                            <div class="metric-card">
                                <div class="blacklist-icon circle-animation">
                                    <div class="blacklist-value-container">
                                        <div class="bl-wl-label">黑名单数量</div>
                                        <div id="blacklistValue" class="bl-wl-value" data-value="432">0</div>
                                    </div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="whitelist-icon circle-animation">
                                    <div class="whitelist-value-container">
                                        <div class="bl-wl-label">白名单数量</div>
                                        <div id="whitelistValue" class="bl-wl-value" data-value="32">0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 趋势分析图表 -->
                <div class="panel">
                    <div class="panel-border"></div>
                    <div class="title_icon">趋势分析图表</div>
                    <div class="panel-card-container">
                        <div id="trendChart" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script th:src="@{/plugins/echarts/echarts.min.js}"></script>
<script>
    var deptId = [[${deptId}]];
    console.log(deptId);

    var typeSelect = xmSelect.render({
        el: '#typeSelect',
        radio: false,
        tips: '请选择地点',
        clickClose: true,
        filterable: true,
        toolbar: {show: true},
        name: 'deptIds',
        layVerify: 'required',
        prop: {name: 'deptName', value: 'ID'},
        data: [],
        // 启用严格模式，确保不会有重复选中项
        strict: true,
        style: {
            paddingLeft: '0px',
            position: 'relative',
            width: '160px',
            height: '38px'
        },
        theme: {
            color: '#00a0e9',                 // 主题颜色
            maxColor: '#00ffff',              // 最大值颜色
            hover: 'rgba(0, 160, 233, 0.2)'   // 悬停颜色
        },
        style: {
            paddingLeft: '0px',
            position: 'relative',
            width: '160px',
            height: '30px',
            backgroundColor: 'rgba(0, 36, 76, 0.8)',
            borderColor: '#0a4c82',
            color: '#00ffff'
        },
        // tree: {
        //     show: true,
        //     strict: false, //是否父子结构，父子结构父节点不会被选中
        //     indent: 30,//间距
        //     expandedKeys: [-1],
        //     clickCheck: true,
        //     clickExpand: true,//点击展开
        // },
        on: function(data) {
            // 当选择发生变化时
            setTimeout(() => {
                loadProcessedData();
                // 初始加载人脸预警统计数据
                loadFaceAlertData();

                // 初始加载车辆预警统计数据
                loadCarAlertData();

                // 初始加载车辆统计数据（默认为周数据）
                loadProcessedStatData(1);

                // 初始加载黑白名单数量数据
                loadBlackAndWhiteCount();

                // 初始加载趋势分析图表数据
                loadTrendChart();

            }, 10);
        }

    });

    $(document).ready(function() {

        setTimeout(function() {
            // 获取所有xm-select相关元素并应用样式
            $('.xm-select').css({
                'background-color': 'rgba(0, 36, 76, 0.8)',
                'border': '1px solid #0a4c82'
            });

            $('.xm-select-title').css({
                'background-color': 'rgba(0, 36, 76, 0.8)',
                'border-color': '#0a4c82'
            });

            $('.xm-select-title *').css('color', '#00ffff');

            // 其他初始化代码...
        }, 500);

        window.alertObject = 2;

        // 进入页面时自动隐藏左侧菜单
        hideLeftMenu();

        // 绑定菜单按钮点击事件
        $('#toggleMenu').on('click', function() {
            enterMainSystem();
        });
        $('#btn-warn1').on('click', function() {
            warn1();
        });
        $('#btn-warn2').on('click', function() {
            warn2();
        });
        $('#btn-warn3').on('click', function() {
            warn3();
        });
        $('#btn-warn4').on('click', function() {
            warn4();
        });

        $('#btn-person').on('click', function() {
            person();
        });

        $('#btn-car').on('click', function() {
            car();
        });

        // 初始化装载数据
        loadProcessedData();

        // 设置定时刷新数据 (每1分钟刷新一次)
        var refreshTimer = setInterval(function() {
            reflashAlertData();
        }, 10000);

        // 将定时器ID保存到window对象中，以便其他页面可以访问
        window.refreshTimer = refreshTimer;

        // 初始加载车辆统计数据（默认为周数据）
        loadProcessedStatData(1);

        // 初始加载黑白名单数量数据
        loadBlackAndWhiteCount();

        // 初始加载趋势分析图表数据
        loadTrendChart();

        // 初始加载人脸预警统计数据
        loadFaceAlertData();

        // 初始加载车辆预警统计数据
        loadCarAlertData();

        // 标签点击事件
        $('.tab-buttons .tab-button1').on('click', function() {
            $(this).siblings().removeClass('active');
            $(this).addClass('active');

            var period = $(this).data('period');
            // layer.msg('切换至' + period + '数据');

            // 获取status参数
            var status = 1; // 默认为1(周)
            if(period === 'month') {
                status = 2;
            } else if(period === 'year') {
                status = 3;
            }

            // 根据时间段获取数据
            loadProcessedStatData(status);
        });

        // 添加预警类型切换按钮事件
        $('.tab-buttons .tab-button').on('click', function() {
            $(this).siblings().removeClass('active');
            $(this).addClass('active');
            window.alertObject = $(this).data('type');
            loadWarningList(window.alertObject);
        });


        $.ajax({
            url: ctx + '/deptController/homeDeptDetail',
            type: 'PUT',
            success: function (res) {

                // 1. 将ID统一转为字符串
                // var data = res.data.map(item => {
                //     return {
                //         deptName: item.deptName,
                //         ID: item.ID.toString() // 确保ID是字符串
                //     };
                // });

                // 3. 更新下拉框数据
                typeSelect.update({
                    data: res.data,
                });

                // 4. 设置初始值（确保deptId转为字符串）
                var initValue = deptId ? deptId.toString() : "";
                if (initValue) {
                    typeSelect.setValue([initValue]);
                }
            }
        });
    });

    // 暂停定时任务
    function pauseRefreshTimer() {
        if (window.refreshTimer) {
            clearInterval(window.refreshTimer);
            window.refreshTimer = null;  // 清除定时器ID
            console.log('定时任务已暂停');
        }
    }

    // 恢复定时任务
    function resumeRefreshTimer() {
        // 确保先清除可能存在的旧定时器
        if (window.refreshTimer) {
            clearInterval(window.refreshTimer);
        }
        // 创建新的定时器
        window.refreshTimer = setInterval(function() {
            console.log("执行定时任务");
            reflashAlertData();
        }, 10000);
        console.log('定时任务已恢复');
    }

    function reflashAlertData() {

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        $.ajax({
            url: ctx + 'homeController/getNewAlert',
            type: 'POST',
            data: JSON.stringify({
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            success: function (res) {
                if (res.code === 0) {
                    if(res.data !== null){
                        // 暂停定时任务
                        pauseRefreshTimer();

                        layer.open({
                            type: 2,
                            title: false,
                            closeBtn: 0,
                            shadeClose: true,
                            area: ['700px', '550px'],
                            content: ctx + '/homeController/toHomeFeedbackPage/' + res.data.code,
                            skin: 'no-btn-skin',
                            success: function (index, layero) {
                                const iframe = layero.find('iframe')[0];
                                const iframeWindow = iframe.contentWindow;

                                // 监听 iframe 页面的提交事件
                                iframeWindow.document.getElementById('subBtn').addEventListener('click', function() {
                                    // 提交完成后关闭弹窗并刷新数据
                                    layer.close(index);
                                    loadProcessedData();
                                    // 恢复定时任务
                                    resumeRefreshTimer();
                                });
                            },
                            end: function() {
                                // 弹窗关闭时恢复定时任务
                                resumeRefreshTimer();
                            }
                        });
                    }else {
                        console.log('未获取到实时数据')
                    }
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (xhr, status, error) {
                console.log('Ajax 请求发生错误:', error);
            }
        });
    }

    // 隐藏左侧菜单函数
    function hideLeftMenu() {
        $('body').addClass('fullscreen-mode');
        // 不再尝试修改父窗口
        // 移除这段代码可以让页面独立显示
        /*
        if (window.parent && window.parent.document) {
            // 尝试在父窗口中隐藏左侧菜单
            $(window.parent.document).find('.layui-layout-admin .layui-side').hide();
            $(window.parent.document).find('.layui-layout-admin .layui-body').css('left', '0');
            $(window.parent.document).find('.layui-layout-admin .layui-footer').css('left', '0');
        }
        */
    }

    // 进入主系统函数
    function enterMainSystem() {
        // 尝试多种可能的路径
        try {
            // 方法1：直接跳转到首页
            window.location.href = ctx + "/index";
        } catch(e) {
            console.log("方法1失败，尝试方法2", e);
            try {
                // 方法2：尝试跳转到默认首页
                window.location.href = ctx + "/index";
            } catch(e2) {
                console.log("方法2失败，尝试方法3", e2);
                try {
                    // 方法3：尝试跳转到系统根目录
                    window.location.href = ctx + "/";
                } catch(e3) {
                    console.log("所有方法都失败", e3);
                    // 显示错误提示
                    layer.msg('无法进入系统，请联系管理员');
                }
            }
        }
    }


    function warn1() {
        debugger;
        // 构建跳转URL，携带参数
        var url = ctx + "/index?openTabUrl=" + encodeURIComponent(ctx + "/blackPersonnelRecordController/blackPersonnelList") +
            "&openTabTitle=" + encodeURIComponent("黑名单人员列表");
        console.log("进入黑名单人员页面"+url);
        // 跳转到index页面
        window.location.href = url;
    }

    function warn2() {
        debugger;
        // 构建跳转URL，携带参数
        var url = ctx + "/index?openTabUrl=" + encodeURIComponent(ctx + "/suspectedPersonnelRecordController/suspectedPersonnelList") +
            "&openTabTitle=" + encodeURIComponent("疑似人员列表");
        console.log("进入疑似人员页面"+url);
        // 跳转到index页面
        window.location.href = url;
    }

    function warn3() {
        debugger;
        // 构建跳转URL，携带参数
        var url = ctx + "/index?openTabUrl=" + encodeURIComponent(ctx + "/blackCarController/toBlackCarPage") +
            "&openTabTitle=" + encodeURIComponent("黑名单车辆列表");
        console.log("进入黑名单车辆页面"+url);
        // 跳转到index页面
        window.location.href = url;
    }

    function warn4() {
        debugger;
        // 构建跳转URL，携带参数
        var url = ctx + "/index?openTabUrl=" + encodeURIComponent(ctx + "/suspectedCarController/toSuspectedCarPage") +
            "&openTabTitle=" + encodeURIComponent("疑似黄牛车辆表");
        console.log("进入疑似黄牛车辆页面"+url);
        // 跳转到index页面
        window.location.href = url;
    }

    function person() {
        debugger;
        // 构建跳转URL，携带参数
        var url = ctx + "/index?openTabUrl=" + encodeURIComponent(ctx + "/portraitCollectionImageController/portraitList") +
            "&openTabTitle=" + encodeURIComponent("人像频次列表");
        console.log("进入人像频次列表页面"+url);
        // 跳转到index页面
        window.location.href = url;
    }

    function car() {
        debugger;
        // 构建跳转URL，携带参数
        var url = ctx + "/index?openTabUrl=" + encodeURIComponent(ctx + "/carController/carlist") +
            "&openTabTitle=" + encodeURIComponent("车辆频次表");
        console.log("进入车辆频次表页面"+url);
        // 跳转到index页面
        window.location.href = url;
    }

    // 显示左侧菜单函数 - 保留但不使用
    function showLeftMenu() {
        // 直接调用进入系统函数
        enterMainSystem();
    }

    // 切换左侧菜单显示状态
    function toggleLeftMenu() {
        // 直接调用进入系统函数
        enterMainSystem();
    }

    // 加载"已处理数量展示"模块数据
    function loadProcessedData() {
        // 显示加载中状态
        $('#carProcessedValue, #personProcessedValue').text('加载中...');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        console.log("carModel",carModel)

        // 调用接口获取当日已处理数量
        $.ajax({
            url: ctx + '/homeController/processedForToday',
            type: 'POST',
            data: JSON.stringify({
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            success: function(res) {
                if (res.code === 0) {
                    var data = res.data;
                    // 更新车辆处理数
                    updateWithAnimation('#carProcessedValue', data.processedVehicles || 0);
                    // 更新人员处理数
                    updateWithAnimation('#personProcessedValue', data.processedPersons || 0);

                    console.log('已处理数量展示数据加载成功');
                } else {
                    layer.msg('获取已处理数量失败: ' + res.msg);
                    // 设置默认值
                    $('#carProcessedValue').text('0');
                    $('#personProcessedValue').text('0');
                }
            },
            error: function() {
                layer.msg('获取已处理数量接口请求失败');
                // 设置默认值
                $('#carProcessedValue').text('0');
                $('#personProcessedValue').text('0');
            },
            complete: function() {
                // 确保在数据加载完成后，无论成功失败都刷新其他相关数据
                loadWarningList(window.alertObject);
                loadWarningData();
            }
        });
    }

    // 加载"实时预警列表"模块数据
    function loadWarningList(alertObject) {
        // 显示加载中状态
        $('#alertTable tbody').html('<tr><td colspan="4" style="text-align:center;">加载中...</td></tr>');

        // 更新表头
        $('#alertTypeHeader').text(alertObject === 2 ? '车牌号' : '头像');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        // 调用接口获取当日预警列表
        $.ajax({
            url: ctx + '/homeController/warningListForToday',
            type: 'POST',
            data: JSON.stringify({
                alertObject: alertObject,
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            success: function(res) {
                if (res.code === 0) {
                    var list = res.data.list || [];

                    if (list.length === 0) {
                        $('#alertTable tbody').html('<tr><td colspan="4" style="text-align:center;">暂无预警数据</td></tr>');
                        return;
                    }

                    var html = '';
                    $.each(list, function(index, item) {
                        var alertTypeText = item.alertType === 2 ? '黑名单预警' : '疑似预警';
                        var time = formatDateTime(item.createTime);

                        // 根据预警对象类型显示不同的内容
                        var typeContent = '';
                        if (alertObject === 2) {
                            var tagClass = 'blue-tag';
                            if (item.carColor === "蓝色") {
                                tagClass = 'blue-tag';
                            } else if (item.carColor === "绿色") {
                                tagClass = 'green-tag';
                            } else if (item.carColor === "黄色") {
                                tagClass = 'yellow-tag';
                            } else if (item.carColor === "白色") {
                                tagClass = 'white-tag';
                            } else if (item.carColor === "黑色") {
                                tagClass = 'black-tag';
                            }
                            typeContent = '<span class="' + tagClass + '">' + (item.carNum || '未知车牌') + '</span>';
                        } else {
                            var avatarDisplay = '👤'; // 默认头像显示
                            if (item.personUrl) {
                                avatarDisplay = '<img src="' + item.personUrl + '" class="avatar" style="width:30px;height:30px;border-radius:50%;" onerror="this.src=\'../../images/default-avatar.png\';this.onerror=null;" alt="头像" />';
                            } else if (item.ossUrl) {
                                avatarDisplay = '<img src="' + item.ossUrl + '" class="avatar" style="width:30px;height:30px;border-radius:50%;" onerror="this.src=\'../../images/default-avatar.png\';this.onerror=null;" alt="头像" />';
                            }
                            typeContent = avatarDisplay;
                        }

                        html += '<tr>' +
                            '<td>' + typeContent + '</td>' +
                            '<td>' + alertTypeText + '</td>' +
                            '<td>' + time + '</td>' +
                            '<td>' +
                            (item.stateId === 2 ?
                                    '<span class="layui-badge layui-bg-green">已处理</span>' :
                                    '<button class="layui-btn layui-btn-xs layui-btn-normal process-btn" data-code="' + item.code + '">处理</button>'
                            ) +
                            '</td>' +
                            '</tr>';
                    });

                    $('#alertTable tbody').html(html);

                    // 解绑之前的事件处理器
                    $('.process-btn').off('click');

                    // 重新绑定处理按钮点击事件
                    $('.process-btn').on('click', function() {
                        var code = $(this).data('code');
                        layer.open({
                            type: 2,
                            title: false,
                            closeBtn: 0,
                            shadeClose: true,
                            area: ['700px', '550px'],
                            content: ctx + '/homeController/toHomeFeedbackPage/' + code,
                            skin: 'no-btn-skin',
                            success: function (index, layero) {
                                const iframe = layero.find('iframe')[0];
                                const iframeWindow = iframe.contentWindow;

                                iframeWindow.document.getElementById('subBtn').addEventListener('click', function() {
                                    layer.close(index);
                                    loadWarningList(alertObject);
                                });
                            }
                        });
                    });

                    console.log('实时预警列表数据加载成功');
                } else {
                    layer.msg('获取预警列表失败: ' + res.msg);
                    $('#alertTable tbody').html('<tr><td colspan="4" style="text-align:center;">加载失败</td></tr>');
                }
            },
            error: function() {
                layer.msg('获取预警列表接口请求失败');
                $('#alertTable tbody').html('<tr><td colspan="4" style="text-align:center;">加载失败</td></tr>');
            }
        });
    }

    // 加载"预警情况"模块数据
    function loadWarningData() {
        // 显示加载中状态
        $('#suspectedAlertValue, #suspectedProcessedValue, #blacklistAlertValue, #blacklistProcessedValue').text('加载中...');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        // 调用接口获取当日预警数量
        $.ajax({
            url: ctx + '/homeController/warningDataForToday',
            type: 'POST',
            data: JSON.stringify({
                alertObject: alertObject,
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            success: function(res) {
                if (res.code === 0) {
                    var data = res.data;

                    // 黑名单人员预警数
                    var suspectedAlertCount = (data.blacklistPersonAlertCount || 0) ;

                    // 疑似人员预警数量
                    var suspectedProcessedValue= (data.suspectedPersonAlertCount || 0);

                    // 黑名单车辆预警数量
                    var blacklistAlertValue = (data.blacklistVehicleAlertCount || 0);

                    // 疑似车辆预警数量
                    var blacklistProcessedValue = (data.suspectedVehicleAlertCount || 0);

                    // 更新黑名单人员预警数
                    updateWithAnimation('#suspectedAlertValue', suspectedAlertCount);

                    // 更新黑名单预警数
                    updateWithAnimation('#blacklistAlertValue', blacklistAlertValue);

                    // 更新疑似人员预警数量
                    updateWithAnimation('#suspectedProcessedValue', suspectedProcessedValue);

                    // 更新疑似车辆预警数量
                    updateWithAnimation('#blacklistProcessedValue', blacklistProcessedValue);

                    console.log('预警情况数据加载成功');
                } else {
                    layer.msg('获取预警情况失败: ' + res.msg);
                    // 设置默认值
                    $('#suspectedAlertValue, #suspectedProcessedValue, #blacklistAlertValue, #blacklistProcessedValue').text('0');
                }
            },
            error: function() {
                layer.msg('获取预警情况接口请求失败');
                // 设置默认值
                $('#suspectedAlertValue, #suspectedProcessedValue, #blacklistAlertValue, #blacklistProcessedValue').text('0');
            }
        });
    }

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';

        var date = new Date(dateTimeStr);

        var year = date.getFullYear();
        var month = padZero(date.getMonth() + 1);
        var day = padZero(date.getDate());
        var hours = padZero(date.getHours());
        var minutes = padZero(date.getMinutes());
        var seconds = padZero(date.getSeconds());

        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }

    function padZero(num) {
        return (num < 10 ? '0' : '') + num;
    }

    // 数字动画效果
    function animateNumbers() {
        $('.metric-value, .alert-value, .stat-value').each(function () {
            var $this = $(this);
            var text = $this.text();

            // 如果当前文本不是纯数字或"加载中..."，则跳过
            if (text === '加载中...' || isNaN(text.replace(/,/g, ''))) {
                return;
            }

            var finalValue = parseInt(text.replace(/,/g, ''));

            $({ countNum: 0 }).animate({ countNum: finalValue }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum).toLocaleString());
                },
                complete: function() {
                    $this.text(finalValue.toLocaleString());
                }
            });
        });
    }

    // 带动画效果的数据更新
    function updateWithAnimation(selector, newValue) {
        var $element = $(selector);
        if (!$element.length) return;

        // 如果当前显示的是"加载中..."，则直接设置值
        if ($element.text() === '加载中...') {
            $element.text(newValue.toLocaleString());
            return;
        }

        var oldText = $element.text().replace(/,/g, '');
        var oldValue = parseInt(oldText) || 0;

        $({ countNum: oldValue }).animate({ countNum: newValue }, {
            duration: 1000,
            easing: 'swing',
            step: function() {
                $element.text(Math.floor(this.countNum).toLocaleString());
            },
            complete: function() {
                $element.text(newValue.toLocaleString());
            }
        });
    }

    // 加载"趋势分析图表"模块数据
    function loadTrendChart() {
        // 显示加载状态
        $('#trendChart').html('<div style="text-align:center;padding:50px;">加载中...</div>');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        // 调用接口获取每月预警数量
        $.ajax({
            url: ctx + '/homeController/warningCountForMonth',
            type: 'POST',
            data: JSON.stringify({
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            success: function(res) {
                if (res.code === 0) {
                    var data = res.data || [];

                    // 初始化月份数组和数据数组
                    var hours = [];
                    var carWarningData = [];
                    var personWarningData = [];

                    // 假设处理数据为预警数据的70%
                    var processRatio = 0.7;

                    console.log("data",data)
                    // 处理数据
                    data.forEach(function(item) {
                        // 添加月份标签
                        hours.push(item.hour + '点');

                        // 计算总预警数（车辆预警 + 人员预警）
                        // var totalWarnings = (item.carWarningCount || 0) + (item.personWarningCount || 0);

                        carWarningData.push(item.carWarningCount || 0)
                        personWarningData.push(item.personWarningCount || 0)
                        // 添加预警数据
                        // warningData.push(totalWarnings);
                        //
                        // // 添加处理数据（模拟：处理量约为预警量的70%）
                        // processedData.push(Math.round(totalWarnings * processRatio));
                    });

                    console.log("carWarningData",carWarningData)
                    console.log("personWarningData",personWarningData)
                    // 确保DOM元素存在
                    if (!document.getElementById('trendChart')) {
                        console.error('趋势图DOM元素不存在');
                        return;
                    }

                    // 销毁可能存在的旧图表实例
                    var chartDom = document.getElementById('trendChart');
                    var existingChart = echarts.getInstanceByDom(chartDom);
                    if (existingChart) {
                        existingChart.dispose();
                    }

                    // 初始化趋势图
                    var trendChart = echarts.init(document.getElementById('trendChart'));
                    var trendOption = {
                        backgroundColor: 'transparent',
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow',
                                label: {
                                    show: true
                                }
                            }
                        },
                        legend: {
                            data: ['车预警数', '人预警数'],
                            textStyle: {
                                color: '#fff'
                            },
                            top: 0
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            top: '30px',
                            containLabel: true
                        },
                        xAxis: {
                            name:'时间',
                            type: 'category',
                            boundaryGap: false,
                            data: hours,
                            axisLine: {
                                lineStyle: {
                                    color: '#0a4c82'
                                }
                            },
                            axisLabel: {
                                color: '#fff'
                            }
                        },
                        yAxis: {
                            name:'预警数',
                            type: 'value',
                            axisLine: {
                                lineStyle: {
                                    color: '#0a4c82'
                                }
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(10, 76, 130, 0.3)'
                                }
                            },
                            axisLabel: {
                                color: '#fff'
                            }
                        },
                        series: [
                            {
                                name: '车预警数',
                                type: 'line',
                                smooth: true,
                                symbol: 'circle',
                                symbolSize: 8,
                                showSymbol: false,
                                lineStyle: {
                                    width: 3,
                                    color: '#00d9ff',
                                    shadowColor: 'rgba(0, 217, 255, 0.5)',
                                    shadowBlur: 10
                                },
                                areaStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                        offset: 0,
                                        color: 'rgba(0, 217, 255, 0.7)'
                                    }, {
                                        offset: 1,
                                        color: 'rgba(0, 217, 255, 0)'
                                    }])
                                },
                                itemStyle: {
                                    color: '#00d9ff',
                                    borderColor: '#00d9ff',
                                    borderWidth: 2
                                },
                                emphasis: {
                                    itemStyle: {
                                        color: '#fff',
                                        borderColor: '#00d9ff',
                                        borderWidth: 2
                                    }
                                },
                                data: carWarningData
                            },
                            {
                                name: '人预警数',
                                type: 'line',
                                smooth: true,
                                symbol: 'circle',
                                symbolSize: 8,
                                showSymbol: false,
                                lineStyle: {
                                    width: 3,
                                    color: '#ff0000',
                                    shadowColor: 'rgba(255, 0, 0, 0.5)',
                                    shadowBlur: 10
                                },
                                areaStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                        offset: 0,
                                        color: 'rgba(255, 0, 0, 0.7)'
                                    }, {
                                        offset: 1,
                                        color: 'rgba(255, 0, 0, 0)'
                                    }])
                                },
                                itemStyle: {
                                    color: '#ff0000',
                                    borderColor: '#ff0000',
                                    borderWidth: 2
                                },
                                emphasis: {
                                    itemStyle: {
                                        color: '#fff',
                                        borderColor: '#ff0000',
                                        borderWidth: 2
                                    }
                                },
                                data: personWarningData
                            }
                        ]
                    };
                    try {
                        trendChart.setOption(trendOption);
                        console.log('趋势图表渲染成功');
                    } catch (e) {
                        console.error('趋势图表渲染失败:', e);
                        $('#trendChart').html('<div style="text-align:center;padding:50px;color:#ff6b6b;">渲染失败</div>');
                    }

                    // 窗口大小变化时自动调整图表尺寸
                    window.addEventListener('resize', function() {
                        trendChart.resize();
                    });

                    // console.log('趋势分析图表数据加载成功');
                } else {
                    layer.msg('获取趋势分析数据失败: ' + res.msg);
                    // 显示错误信息
                    $('#trendChart').html('<div style="text-align:center;padding:50px;color:#ff6b6b;">加载失败</div>');
                }
            },
            error: function() {
                layer.msg('获取趋势分析图表接口请求失败');
                // 显示错误信息
                $('#trendChart').html('<div style="text-align:center;padding:50px;color:#ff6b6b;">加载失败</div>');
            }
        });
    }

    // 加载"车辆统计信息"模块数据
    function loadProcessedStatData(status) {
        // // 显示加载中状态
        // $('.stat-value').text('加载中...');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        // 调用接口获取处理统计数据
        $.ajax({
            url: ctx + '/homeController/processedData',
            type: 'POST',
            data: JSON.stringify({
                status: status,
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            success: function(res) {
                if (res.code === 0) {
                    var data = res.data;

                    // 更新处理总数
                    updateWithAnimation('.stat-card:nth-child(1) .stat-value', data.processedTotal || 0);

                    // 更新有效处理数（车辆处理数）
                    updateWithAnimation('.stat-card:nth-child(2) .stat-value', data.processedVehicles || 0);

                    // 更新人车检测数（人员处理数）
                    updateWithAnimation('.stat-card:nth-child(3) .stat-value', data.processedPersons || 0);

                    console.log('车辆统计信息数据加载成功');
                } else {
                    layer.msg('获取车辆统计信息失败: ' + res.msg);
                    // 设置默认值
                    $('.stat-value').text('0');
                }
            },
            error: function() {
                layer.msg('获取车辆统计信息接口请求失败');
                // 设置默认值
                $('.stat-value').text('0');
            }
        });
    }

    // 加载"黑白名单数量"模块数据
    function loadBlackAndWhiteCount() {
        // 显示加载中状态
        $('#blacklistValue, #whitelistValue').text('加载中...');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        // 调用接口获取黑白名单数量
        $.ajax({
            url: ctx + '/homeController/blackAndWhiteCount',
            type: 'POST',
            data: JSON.stringify({
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            success: function(res) {
                if (res.code === 0) {
                    var data = res.data;

                    // 更新黑名单数量
                    updateWithAnimation('#blacklistValue', data.blackCount || 0);

                    // 更新白名单数量
                    updateWithAnimation('#whitelistValue', data.whiteCount || 0);

                    console.log('黑白名单数量数据加载成功');
                } else {
                    layer.msg('获取黑白名单数量失败: ' + res.msg);
                    // 设置默认值
                    $('#blacklistValue, #whitelistValue').text('0');
                }
            },
            error: function() {
                layer.msg('获取黑白名单数量接口请求失败');
                // 设置默认值
                $('#blacklistValue, #whitelistValue').text('0');
            }
        });
    }

    // 加载"人脸预警统计"模块数据
    function loadFaceAlertData() {
        // 显示加载中状态
        $('#faceAlertTable tbody').html('<tr><td colspan="5" style="text-align:center;">加载中...</td></tr>');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }

        // 调用接口获取人脸预警统计数据
        $.ajax({
            url: ctx + '/homeController/peopleImageList',
            type: 'POST',
            data: JSON.stringify({
                deptIds:carModel
            }),
            contentType: 'application/json' ,
            // data: { currentPage: 1, pageSize: 10 },
            success: function(res) {
                if (res.code === 0) {
                    var list = res.data.list || [];

                    if (list.length === 0) {
                        $('#faceAlertTable tbody').html('<tr><td colspan="5" style="text-align:center;">暂无数据</td></tr>');
                        return;
                    }

                    var html = '';
                    $.each(list, function(index, item) {
                        var avatarDisplay = '👤'; // 默认头像显示

                        // 如果有图片链接则显示真实图片
                        if (item.ossUrl) {
                            avatarDisplay = '<img src="' + item.ossUrl + '" class="avatar" onerror="this.src=\'../../images/default-avatar.png\';this.onerror=null;" alt="头像" />';
                        } else if (item.personUrl) {
                            avatarDisplay = '<img src="' + item.personUrl + '" class="avatar" onerror="this.src=\'../../images/default-avatar.png\';this.onerror=null;" alt="头像" />';
                        }
                        // 排名图标和数字
                        var rankIcon = '';
                        if (index < 3) {
                            rankIcon = '<span class="three_icon" style="display:inline-block;vertical-align:middle;position:relative;"><span style="position:absolute;left:0;right:0;top:0;bottom:0;display:flex;align-items:center;justify-content:center;font-size:11px;color:#fff;">' + (index + 1) + '</span></span>';
                        } else {
                            rankIcon = '<span class="fore_icon" style="display:inline-block;vertical-align:middle;position:relative;"><span style="position:absolute;left:0;right:0;top:0;bottom:0;display:flex;align-items:center;justify-content:center;font-size:11px;color:#fff;">' + (index + 1) + '</span></span>';
                        }
                        // 格式化时间
                        var timeDisplay = '未知';
                        if (item.lastTime) {
                            timeDisplay = formatDateTime(item.lastTime);
                        }
                        // 构建表格行HTML
                        html += '<tr>' +
                            '<td>' + rankIcon + '</td>' +
                            '<td>' + avatarDisplay + '</td>' +
                            '<td>' + (item.peopleCount || 0) + '</td>' +
                            '<td>' + timeDisplay + '</td>' +
                            '<td>' + (item.lastCameraLocation || '未知位置') + '</td>' +
                            '</tr>';
                    });

                    $('#faceAlertTable tbody').html(html);
                    console.log('人脸预警统计数据加载成功');
                } else {
                    layer.msg('获取人脸预警统计失败: ' + res.msg);
                    $('#faceAlertTable tbody').html('<tr><td colspan="5" style="text-align:center;">加载失败</td></tr>');
                }
            },
            error: function() {
                layer.msg('获取人脸预警统计接口请求失败');
                $('#faceAlertTable tbody').html('<tr><td colspan="5" style="text-align:center;">加载失败</td></tr>');
            }
        });
    }

    // 加载"车牌预警统计"模块数据
    function loadCarAlertData() {
        // 显示加载中状态
        $('#carAlertTable tbody').html('<tr><td colspan="6" style="text-align:center;">加载中...</td></tr>');

        const carModel = []
        var value = typeSelect.getValue();
        if (value.length > 0) {
            value.forEach(data => {
                carModel.push(data.ID)
            })
        }
        // 调用接口获取车辆预警统计数据
        $.ajax({
            url: ctx + '/homeController/carImageList',
            type: 'POST',
            data: JSON.stringify({
                deptIds:carModel
            }),
            contentType: 'application/json',
            success: function(res) {
                if (res.code === 0) {
                    var list = res.data.list || [];

                    if (list.length === 0) {
                        $('#carAlertTable tbody').html('<tr><td colspan="6" style="text-align:center;">暂无数据</td></tr>');
                        return;
                    }

                    var html = '';
                    $.each(list, function(index, item) {
                        // 确定标签样式
                        var tagClass = 'blue-tag';
                        if (item.carColor === "蓝色") {
                            tagClass = 'blue-tag';
                        } else if (item.carColor === "绿色") {
                            tagClass = 'green-tag';
                        } else if (item.carColor === "黄色") {
                            tagClass = 'yellow-tag';
                        } else if (item.carColor === "白色") {
                            tagClass = 'white-tag';
                        }else if (item.carColor === "黑色") {
                            tagClass = 'black-tag';
                        }
                        // 排名图标和数字
                        var rankIcon = '';
                        if (index < 3) {
                            rankIcon = '<span class="three_icon" style="display:inline-block;vertical-align:middle;position:relative;"><span style="position:absolute;left:0;right:0;top:0;bottom:0;display:flex;align-items:center;justify-content:center;font-size:11px;color:#fff;">' + (index + 1) + '</span></span>';
                        } else {
                            rankIcon = '<span class="fore_icon" style="display:inline-block;vertical-align:middle;position:relative;"><span style="position:absolute;left:0;right:0;top:0;bottom:0;display:flex;align-items:center;justify-content:center;font-size:11px;color:#fff;">' + (index + 1) + '</span></span>';
                        }
                        // 格式化时间
                        var timeDisplay = '未知';
                        if (item.lastTime) {
                            timeDisplay = formatDateTime(item.lastTime);
                        }
                        // 构建表格行HTML
                        html += '<tr>' +
                            '<td>' + rankIcon + '</td>' +
                            '<td><span class="' + tagClass + '">' + (item.carNum || '无车牌') + '</span></td>' +
                            '<td>' + (item.carColor || '未知') + '</td>' +
                            '<td>' + (item.carCount || 0) + '</td>' +
                            '<td>' + timeDisplay + '</td>' +
                            '<td>' + (item.lastCameraLocation || '未知位置') + '</td>' +
                            '</tr>';
                    });

                    $('#carAlertTable tbody').html(html);
                    console.log('车辆预警统计数据加载成功');
                } else {
                    layer.msg('获取车辆预警统计失败: ' + res.msg);
                    $('#carAlertTable tbody').html('<tr><td colspan="6" style="text-align:center;">加载失败</td></tr>');
                }
            },
            error: function() {
                layer.msg('获取车辆预警统计接口请求失败');
                $('#carAlertTable tbody').html('<tr><td colspan="6" style="text-align:center;">加载失败</td></tr>');
            }
        });
    }
</script>
</body>
</html>