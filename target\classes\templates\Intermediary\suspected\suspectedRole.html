<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <script th:src="@{/scripts/security/crypto-js.js}"></script>
    <script th:src="@{/scripts/security/front_aes.js}"></script>
    <script th:src="@{/scripts/security/signature.js}"></script>
    <div th:replace="Importfile::html"></div>
</head>
<body>

<form class="layui-form"  lay-filter="edit-form">
    <div class="layui-fluid">
        <div class="layui-form-item">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">修改疑似人员判定规则</div>
                        <div class="layui-card-body">
                            <div class="layui-form" >
                                <div class="layui-form-item">
                                    <label class="layui-form-label">出现频次阈值</label>
                                    <div class="layui-input-inline">
                                        <input type="number" id="counts"
                                               name="counts" lay-verify="require" placeholder="请输入默认过期时间"
                                               autocomplete="off" class="layui-input"
                                               th:value="${suspectedRole.counts}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">默认过期时间(天)</label>
                                    <div class="layui-input-inline">
                                        <input type="number" id="expiration"
                                               name="expiration" lay-verify="require" placeholder="请输入默认过期时间"
                                               autocomplete="off" class="layui-input"
                                               th:value="${suspectedRole.expiration}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">出现时间范围(天)</label>
                                    <div class="layui-input-inline">
                                        <input type="number" id="dateRange"
                                               name="dateRange" lay-verify="require" placeholder="请输入出现时间范围"
                                               autocomplete="off" class="layui-input"
                                               th:value="${suspectedRole.dateRange}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="submit" id="subBtn">确认修改</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>


<script>
    //监听提交

    layui.use(['form', 'layedit', 'laydate'], function () {
        var form = layui.form
            , layer = layui.layer
            , layedit = layui.layedit
            , laydate = layui.laydate;
        //自定义验证规则
        // form.verify({
        //     newpassword: function (value) {
        //         var mediumRegex = new RegExp('^(?![a-zA-Z]+$)(?!\\d+$)(?![~!@#$%^&*()_+.]+$)(?![a-zA-Z\\d]+$)(?![a-zA-Z~!@#$%^&*()_+.]+$)(?![\\d~!@#$%^&*()_+.]+$)[a-zA-Z\\d~!@#$%^&*()_+.]+$', "g");  //强
        //         var message = mediumRegex.test(value);
        //         if (message == false) {
        //             return "密码输入强度过弱!";
        //         }
        //         if (value.length == 0) {
        //             return "请输入密码";
        //         }
        //     },
        //     confirmpassword: function (value) {
        //         var newpassword = $('#newpassword').val();
        //         if (value != newpassword) {
        //             return "确认密码和新密码请保持一致";
        //         }
        //     },
        //     pass: [
        //         /^[\S]{8,16}$/
        //         , '密码必须8到16位，且不能出现空格'
        //     ]
        //     , content: function (value) {
        //         layedit.sync(editIndex);
        //     }
        // });


        form.on('submit(submit)', function (data) {
            /*var formData = new FormData();
            // 将其他字段数据也添加到formData中
            for (var key in data.field) {
                console.log(data.field[key])
                formData.append(key, data.field[key]);
            }*/

            $.ajax({
                url: ctx + "suspectedRoleController/updateRole",
                type: 'POST',
                data: JSON.stringify(data.field),
                async: false,
                cache: false,
                contentType: 'application/json',
                dataType: 'json',
                success: function (result) {
                    if (result.code != 200) {
                        layer.alert(result.msg, {icon: 2});
                    } else {
                        layer.alert(result.msg, {icon: 1});
                    }
                },
                error: function (result) {
                    layer.alert(result.msg, {
                        icon: 5,
                        skin: 'layer-ext-moon'
                    });
                }
            });
            return false;
        });

    });
</script>
</body>
</html>