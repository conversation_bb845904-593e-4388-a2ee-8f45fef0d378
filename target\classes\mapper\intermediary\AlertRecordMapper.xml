<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.intermediary.dao.IAlertRecordDao">
    <resultMap id="alert" type="com.fwy.intermediary.entity.AlertRecord">
        <result column="code" property="code"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="alert_type" property="alertType"/>
        <result column="alert_object" property="alertObject"/>
        <result column="full_name" property="fullName"/>
        <result column="idcard_num" property="idcardNum"/>
        <result column="car_num" property="carNum"/>
        <result column="oss_url" property="ossUrl"/>
        <result column="operate_feedback" property="operateFeedback"/>
        <result column="state_id" property="stateId"/>
        <result column="update_user" property="updateUser"/>
<!--        新增字段-->
<!--        <result column="mark_id" property="markId"/>-->
        <result column="person_code" property="personCode"/>

        <result column="update_time" property="updateTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="findByCondition" parameterType="AlertRecordCondition" resultMap="alert">
        select * from alert_record where state_id != 3
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="stateId != null">
            and state_id = #{stateId}
        </if>
        <if test="alertType != null">
            and alert_type = #{alertType}
        </if>
        <if test="alertObject != null">
            and alert_object = #{alertObject}
        </if>
        <if test="fullName != null &amp;&amp; fullName !=''">
            and full_name like concat('%', #{fullName}, '%')
        </if>
        <if test="idcardNum != null &amp;&amp; idcardNum !=''">
            and idcard_num = #{idcardNum}
        </if>
        <if test="carNum != null &amp;&amp; carNum !=''">
            and car_num like concat('%', #{carNum}, '%')
        </if>
        <if test="updateUser != null &amp;&amp; updateUser != ''">
            and update_user like concat('%', #{updateUser}, '%')
        </if>
        <if test="startDate != null">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            <!--&lt;小于-->
            and create_time &lt;= #{endDate}
        </if>
        <if test="updateTime != null">
            and update_time >= #{startDate}
        </if>
        order by field(state_id,0,1,2,3), create_time desc
    </select>

    <select id="findUnRead" parameterType="AlertRecordCondition" resultMap="alert">
        select * from alert_record where state_id = 0
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
        <if test="stateId != null">
            and state_id = #{stateId}
        </if>
        <if test="alertType != null">
            and alert_type = #{alertType}
        </if>
        <if test="alertObject != null">
            and alert_object = #{alertObject}
        </if>
        <if test="idcardNum != null &amp;&amp; idcardNum !=''">
            and idcard_num = #{idcardNum}
        </if>
        <if test="updateUser != null &amp;&amp; updateUser != ''">
            and update_user like concat('%', #{updateUser}, '%')
        </if>
        <if test="startDate != null">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            <!--&lt;小于-->
            and create_time &lt;= #{endDate}
        </if>
        <if test="updateTime != null">
            and update_time >= #{startDate}
        </if>
    </select>


    <insert id="save" parameterType="com.fwy.intermediary.entity.AlertRecord">
        insert into alert_record (code, dept_id, dept_name,
                                  alert_type, alert_object, full_name,
                                  idcard_num, car_num, oss_url,
                                  state_id, operate_feedback, update_user,
                                  update_time, create_time)
        values (#{code,jdbcType=VARCHAR}, #{deptId,jdbcType=VARCHAR}, #{deptName,jdbcType=VARCHAR},
                #{alertType,jdbcType=INTEGER}, #{alertObject,jdbcType=INTEGER}, #{fullName,jdbcType=VARCHAR},
                #{idcardNum,jdbcType=VARCHAR}, #{carNum,jdbcType=VARCHAR}, #{ossUrl,jdbcType=VARCHAR},
                #{stateId,jdbcType=INTEGER}, #{operateFeedback,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>

    <select id="getProcessedDataForToday"
            resultType="com.fwy.intermediary.entity.response.processedForTodayResponce">
        SELECT
            COUNT(CASE WHEN alert_object = 1 THEN 1 END) AS processedPersons,
            COUNT(CASE WHEN alert_object = 2 THEN 1 END) AS processedVehicles
        FROM alert_record
        WHERE (create_time between  CURDATE() and CURDATE() + INTERVAL 1 DAY)
          AND state_id = 2
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </select>

    <select id="getWarningDataForToday"
            resultType="com.fwy.intermediary.entity.response.WarningDataForTodayResponse">
        SELECT
            COUNT(CASE WHEN alert_type = 2 AND alert_object = 1 THEN 1 END) AS blacklistPersonAlertCount,
            COUNT(CASE WHEN alert_type = 1 AND alert_object = 1 THEN 1 END) AS suspectedPersonAlertCount,
            COUNT(CASE WHEN alert_type = 2 AND alert_object = 2 THEN 1 END) AS blacklistVehicleAlertCount,
            COUNT(CASE WHEN alert_type = 1 AND alert_object = 2 THEN 1 END) AS suspectedVehicleAlertCount
        FROM alert_record
        WHERE (create_time between  CURDATE() and CURDATE() + INTERVAL 1 DAY) AND state_id != 3
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </select>
    <select id="getWarningListForToday"
            resultType="com.fwy.intermediary.entity.response.WarningListForTodayResponse">
        SELECT
            code,
            alert_type as alertType,
            car_num as carNum,
            state_id as stateId,
            oss_url as ossUrl,
            create_time as createTime
        FROM alert_record
        WHERE (create_time between  CURDATE() and CURDATE() + INTERVAL 1 DAY)
          AND alert_object = 2 AND state_id = 1
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>
    <select id="getProcessedData" resultType="com.fwy.intermediary.entity.response.ProcessedDataResponse">
        SELECT COUNT(alert_object) AS processedTotal,
        COUNT(CASE WHEN alert_object = 1 THEN 1 END) AS processedPersons,
        COUNT(CASE WHEN alert_object = 2 THEN 1 END) AS processedVehicles
        FROM alert_record
        WHERE  state_id = 2
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="status == 1">
            and create_time between DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
            AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY);
        </if>
        <if test="status == 2">
            and DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
        </if>
        <if test="status == 3">
            and DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(CURDATE(), '%Y')
        </if>
    </select>

    <select id="getWarningCountForMonth"
            resultType="com.fwy.intermediary.entity.response.WarningCountForMonthResponse">
        SELECT
            COUNT(CASE WHEN  alert_object = 1 THEN 1 END) AS personWarningCount,
            COUNT(CASE WHEN  alert_object = 2 THEN 1 END) AS carWarningCount,
            HOUR(create_time) AS hour
        FROM
            alert_record
        WHERE
            HOUR(create_time) BETWEEN 9 AND 17 AND state_id != 3
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY HOUR(create_time)
        ORDER BY HOUR(create_time)
        limit 10
    </select>
    <select id="getPeopleImageList"  resultType="com.fwy.intermediary.entity.response.PeopleImageResponse">
        select person_code code, count(*) peopleCount, i.oss_url ossUrl, i.type type,  MAX(r.create_time) lastTime ,
               SUBSTRING_INDEX(GROUP_CONCAT(r.dept_name ORDER BY r.create_time DESC), ',', 1) AS lastCameraLocation
        from
            portrait_collection_record r right join portrait_collection_image i on r.person_code = i.code
        where r.state_id != 3 and i.state_id != 3
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY person_code
        order by peopleCount desc
        limit 10
    </select>


    <select id="getCarImageList"  resultType="com.fwy.intermediary.entity.response.CarImageResponse" >
        SELECT i.code,r.car_num as carNum,i.type,i.state_id,i.oss_url as ossUrl,count(1)as carCount,MAX(r.create_time)as lastTime,
        SUBSTRING_INDEX(GROUP_CONCAT(r.dept_name ORDER BY r.create_time DESC), ',', 1) AS lastCameraLocation
        FROM car_collection_record r right join car_collection_image i
        on r.car_num = i.car_num
        <where>
            r.car_num in(select car_num from car_collection_image where state_id!=3 and type!=-1)
            <if test="deptIds != null and deptIds.size() != 0">
                and r.dept_id in
                <foreach collection="deptIds" item="deptId" index="index"
                         open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
        </where>
        GROUP BY
        i.CODE,
        r.car_num,
        i.type,
        i.state_id,
        i.oss_url
        order by
        count desc
        limit 10
    </select>
<!--    <select id="getPeopleImageList" resultType="com.fwy.intermediary.entity.response.PeopleImageResponse">-->
<!--        SELECT-->
<!--            person_code AS code,-->
<!--            COUNT(*) AS peopleCount,-->
<!--            MAX(create_time) AS lastTime,-->
<!--            SUBSTRING_INDEX(GROUP_CONCAT(dept_name ORDER BY create_time DESC), ',', 1) AS lastCameraLocation,-->
<!--            SUBSTRING_INDEX(GROUP_CONCAT(person_url ORDER BY create_time DESC), ',', 1) AS ossUrl-->
<!--        FROM portrait_collection_record-->
<!--        WHERE state_id != 3-->
<!--        GROUP BY person_code-->
<!--        ORDER BY peopleCount DESC-->
<!--    </select>-->
<!--    <select id="getCarImageList" resultType="com.fwy.intermediary.entity.response.CarImageResponse">-->
<!--        SELECT-->
<!--            car_num AS carNum,-->
<!--            COUNT(*) AS carCount,-->
<!--            MAX(create_time) AS lastTime,-->
<!--            SUBSTRING_INDEX(GROUP_CONCAT(dept_name ORDER BY create_time DESC), ',', 1) AS lastCameraLocation,-->
<!--            SUBSTRING_INDEX(GROUP_CONCAT(oss_url ORDER BY create_time DESC), ',', 1) AS ossUrl-->
<!--        FROM car_collection_record-->
<!--        GROUP BY car_num-->
<!--        ORDER BY carCount DESC-->
<!--    </select>-->
    <select id="getNewAlert" resultMap="alert">
        SELECT * FROM alert_record WHERE state_id = 0 AND (create_time between  CURDATE() and CURDATE() + INTERVAL 1 DAY)
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        ORDER BY create_time LIMIT 1
    </select>

    <select id="getPersonWarningListForToday"
            resultType="com.fwy.intermediary.entity.response.WarningListForTodayResponse">
        SELECT
            code,
            alert_type as alertType,
            person_code as personCode,
            (SELECT person_url FROM portrait_collection_record WHERE person_code = alert_record.person_code ORDER BY CREATE_TIME DESC LIMIT 1) as personUrl,
            state_id as stateId,
            oss_url as ossUrl,
            create_time as createTime
        FROM alert_record
        WHERE (create_time between  CURDATE() and CURDATE() + INTERVAL 1 DAY)
          AND alert_object = 1 AND state_id = 1
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>


</mapper>