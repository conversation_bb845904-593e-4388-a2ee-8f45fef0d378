<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fwy.intermediary.dao.BlackCarRecordMapper" >
  <resultMap id="BaseResultMap" type="com.fwy.intermediary.entity.BlackCarRecord" >
    <id column="code" property="code" jdbcType="VARCHAR" />
    <result column="car_id" property="carId" jdbcType="VARCHAR" />
    <result column="car_num" property="carNum" jdbcType="VARCHAR" />
    <result column="oss_url" property="ossUrl" jdbcType="VARCHAR" />
    <result column="state_id" property="stateId" jdbcType="INTEGER" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="operate_feedback" property="operateFeedback" jdbcType="VARCHAR" />
  </resultMap>

  <!--删除状态为3的数据-->
  <delete id="deleteDataByState">
        delete from black_car_record
        where state_id=3
  </delete>

  <select id="findRecordByPage" parameterType="com.fwy.intermediary.entity.show.BlackList" resultMap="BaseResultMap">
    select * from black_car_record
    <where>
        state_id !=3
      <if test="startDate != null">
        and create_time >= #{startDate}
      </if>
      <if test="deptIds != null and deptIds.size() != 0">
        and dept_id in
        <foreach collection="deptIds" item="deptId" index="index"
                 open="(" close=")" separator=",">
          #{deptId}
        </foreach>
      </if>
      <if test="endDate != null">
        <!--&lt;小于-->
        and create_time &lt;= #{endDate}
      </if>
      <if test="carNum != null and carNum !=''">
        and car_num like concat('%', #{carNum}, '%')
      </if>
    </where>
    order by create_time desc
  </select>

  <sql id="Base_Column_List" >
    code, car_id, car_num, oss_url, state_id, update_user, update_time, create_user,
    create_time
  </sql>

  <!--判断黑名单车辆是否存在-->
  <select id="ifExists" resultType="java.lang.Integer" parameterType="java.lang.String">
    select exists
    (select car_num from black_car_record
    where car_num = #{carNum,jdbcType=VARCHAR})
  </select>

  <!--查询所有黑名单车辆 state_id = 1-->
  <select id="getList" resultMap="BaseResultMap">
    select *
    from black_car_record
    where state_id=1
  </select>

  <!--通过carId查询黑名单-->
  <select id="selectByCarId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from black_car_record
    where car_id = #{carId,jdbcType=VARCHAR}
  </select>

  <!--通过车牌！删除！车辆信息-->
  <delete id="deleteByCarNum" parameterType="java.lang.String" >
    delete from black_car_record
    where car_num = #{carNum,jdbcType=VARCHAR}
  </delete>


  <!--插入到黑名单-->
  <insert id="insert" parameterType="com.fwy.intermediary.entity.BlackCarRecord" >
    insert into black_car_record (code, car_id, car_num, 
      oss_url, state_id, update_user, 
      update_time, create_user, create_time,dept_id,dept_name,operate_feedback
      )
    values (#{code,jdbcType=VARCHAR}, #{carId,jdbcType=VARCHAR}, #{carNum,jdbcType=VARCHAR}, 
      #{ossUrl,jdbcType=VARCHAR}, #{stateId,jdbcType=INTEGER}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},#{deptId},#{deptName},#{operateFeedback}
      )
  </insert>

  <!--通过车牌获取黑名单一条信息-->
  <select id="getByCarNum" resultMap="BaseResultMap" parameterType="java.lang.String">
    select *
    from black_car_record
    where car_num=#{carNum,jdbcType=VARCHAR}
  </select>

  <!--通过时间查找列表-->
  <select id="getListByTime" resultMap="BaseResultMap">
    select *
    from black_car_record
    where create_time
    between #{t1,jdbcType=TIMESTAMP} and #{t2,jdbcType=TIMESTAMP}
    and state_id=1
  </select>


  <update id="deleteById" parameterType="java.lang.String">
        UPDATE user SET state_id=2 WHERE car_id=#{carId,jdbcType=VARCHAR}
  </update>


  <update id="updateByPrimaryKey" parameterType="com.fwy.intermediary.entity.BlackCarRecord" >
    update black_car_record
    <set>
      <if test="ossUrl != null">
        oss_url = #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="stateId != null">
        state_id = #{stateId,jdbcType=INTEGER},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null and createUser != ''">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR}, <!-- 假设为VARCHAR类型，按需修改 -->
      </if>
      <if test="deptName != null and deptName != ''">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="operateFeedback != null and operateFeedback != ''">
        operate_feedback = #{operateFeedback,jdbcType=VARCHAR},
      </if>
    </set>
    where code = #{code,jdbcType=VARCHAR} AND car_id = #{carId,jdbcType=VARCHAR}
  </update>

  <update id="updateCarState" >
    update black_car_record
    <set>
      <if test="stateId != null">
        state_id = #{stateId,jdbcType=INTEGER},
      </if>
      <if test="operateFeedback != null and operateFeedback != ''">
        operate_feedback = #{operateFeedback,jdbcType=VARCHAR},
      </if>
    </set>
    where code = #{code}
  </update>

  <select id="countAll" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM black_car_record where 1=1
    <if test="deptIds != null and deptIds.size() != 0">
      and dept_id in
      <foreach collection="deptIds" item="deptId" index="index"
               open="(" close=")" separator=",">
        #{deptId}
      </foreach>
    </if>
  </select>

    <insert id="insertSelective" parameterType="com.fwy.intermediary.entity.BlackCarRecord" >
    insert into black_car_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        code,
      </if>
      <if test="carId != null" >
        car_id,
      </if>
      <if test="carNum != null" >
        car_num,
      </if>
      <if test="ossUrl != null" >
        oss_url,
      </if>
      <if test="stateId != null" >
        state_id,
      </if>
      <if test="updateUser != null" >
        update_user,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="createUser != null" >
        create_user,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="carId != null" >
        #{carId,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null" >
        #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="ossUrl != null" >
        #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="stateId != null" >
        #{stateId,jdbcType=INTEGER},
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="ifExistsByDept" resultType="java.lang.Integer">
    select exists
             (select code from black_car_record
              where car_num = #{carNum,jdbcType=VARCHAR} AND dept_id = #{deptId})
  </select>
  <select id="getOneByCarNum" resultType="com.fwy.intermediary.entity.BlackCarRecord">
    select * from black_car_record
    where car_num = #{carNum,jdbcType=VARCHAR} AND dept_id = #{deptId}
  </select>

</mapper>