<component name="libraryTable">
  <library name="Maven: com.github.theborakompanioni:thymeleaf-extras-shiro:2.0.0">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/theborakompanioni/thymeleaf-extras-shiro/2.0.0/thymeleaf-extras-shiro-2.0.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/theborakompanioni/thymeleaf-extras-shiro/2.0.0/thymeleaf-extras-shiro-2.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/theborakompanioni/thymeleaf-extras-shiro/2.0.0/thymeleaf-extras-shiro-2.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>