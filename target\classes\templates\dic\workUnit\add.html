<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="UTF-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet"
          th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style type="text/css">
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title"
                      style="margin-top: 20px;">
                <legend>单位信息</legend>
            </fieldset>

            <form class="layui-form" action="">
                <input type="hidden" name="id" th:value="${workUnit==null?'':workUnit.id}"/>
                <input type="hidden" name="orderCode" th:value="${workUnit==null?'':workUnit.orderCode}"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>名称：</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="dicName" lay-verify="required"
                                   autocomplete="off" class="layui-input" th:value="${workUnit==null?'':workUnit.dicName}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 180px;"><span style="color:red">*</span>单位代码：</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="code" lay-verify="required" maxlength="13"
                                   autocomplete="off" class="layui-input" th:value="${workUnit==null?'':workUnit.code}">
                        </div>
                    </div>

                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">上级区县：</label>
                        <div class="layui-input-inline">
                            <input type="hidden" id="parentId" th:value="${workUnit==null?'':workUnit.parentId}"/>
                            <!--<input type="hidden" value="" name="parentDicName" id="parentDicName"/>-->
                            <select name="parentId" xm-select="parentId-select" xm-select-search="" xm-select-radio="" xm-select-height="36px" >
                            </select>
                        </div>
                    </div>
                </div>
                <button style="display:none" lay-submit lay-filter="subBtn" id="subBtn"></button>
            </form>
        </div>
    </div>
</div>

<script>
    layui.use([ 'form', 'layedit', 'laydate', 'jquery'  ],
        function() {
            var form = layui.form, layer = layui.layer, layedit = layui.layedit, laydate = layui.laydate, $ = jQuery = layui.$;

            var originCode = [[${workUnit==null?'0':workUnit.code}]];
            //监听提交
            form.on('submit(subBtn)', function(data) {
                // 没修改代码时，不将code传到后台
                var submitCode = data.field.code;
                if(originCode == submitCode){
                    data.field.code = "";
                }

                var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
                $.ajax({
                    type : "POST",
                    url : ctx +'dicWorkUnitController/save',
                    data : data.field,
                    dataType : "json",
                    async: "false",
                    success:function(res){
                        layer.close(loading);
                        if(res.stateType == 0){
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            parent.layer.msg(res.stateMsg, {icon: 1});
                        }else{
                            layer.msg(res.stateMsg, {icon: 2});
                        }
                    },
                    error:function(){
                        layer.close(loading);
                        layer.msg(res.stateMsg, {icon: 2});
                    }
                });
                return false;
            });

            layui.formSelects.data('parentId-select', 'server', {
                url : ctx +'dicWorkUnitController/selectTreeJson',
                data: {"id": $("#parentId").val()},
                keyName: 'dicName',         //自定义返回数据中name的key, 默认 name
                keyVal: 'id',	//自定义返回数据中value的key, 默认 value
                keySel: 'selected',         //自定义返回数据中selected的key, 默认 selected
                keyDis: 'disabled',         //自定义返回数据中disabled的key, 默认 disabled
                keyChildren: 'children',    //联动多选自定义children
                response: {
                    statusCode: 0,          //成功状态码
                    msgName: 'msg',         //msg key
                    dataName: 'data'        //data key
                },
            }).on('deptId', function(id, vals, val, isAdd, isDisabled) {
                $("#parentId").val(val.id);
            },true);

        });


</script>
</body>
</html>