<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<<body>


<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
        <ul class="layui-tab-title">
            <li>投票消息</li>
        </ul>
        <div class="layui-tab-content" style="height: 475px">
            <div class="layui-tab-item" id="person">
                <div class="layui-card">
                    <div class="layui-card-body" style="height:auto;">
                        <ul class="layuiadmin-card-status layuiadmin-home2-usernote" style="height: 440px; width: 250px;overflow-y: scroll;" id="personWaring">

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}">
    <!--  <link rel="stylesheet" th:href="@{/admin/layui/css/admin.css}"> -->
    <script th:src="@{/plugins/jquery/jquery-3.4.1.min.js}"></script>
    <script th:src="@{/admin/layui/layui.all.js}"></script>
    <script>
        //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
        layui.use(['table','laydate','flow'], function() {
            var element = layui.element;
            var $ = layui.jquery,
                flow = layui.flow;


            flow.load({
                elem: '#machineWaring' //指定列表容器
                , done: function (pageNum, next) { //到达临界点（默认滚动触发），触发下一页
                    var lis = [];
                    //以jQuery的Ajax请求为例，请求下一页数据（注意：page是从2开始返回）
                    $.get( ctx + '/pollDataController/findPollDataForUser?pageNum=' + pageNum, function (res) {

                        //假设你的列表返回在data集合中
                        if (res.data.list != null) {
                            var data = res.data.list;
                            if (data.length > 0)
                                layui.each(data, function (index, item) {
                                    lis.push("<li><p>" + data[index].pollTitle
                                        + data[index].messageLogContent + "</p><span>"
                                        + data[index].createTimeStr + "</span></li>");
                                });
                        }
                        next(lis.join(''), pageNum < res.data.pages);
                    });
                }
            });
        });
    </script>
</body>
