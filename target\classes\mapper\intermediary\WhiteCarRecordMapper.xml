<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fwy.intermediary.dao.WhiteCarRecordMapper" >
  <resultMap id="BaseResultMap" type="com.fwy.intermediary.entity.WhiteCarRecord" >
    <id column="code" property="code" jdbcType="VARCHAR" />
    <result column="car_num" property="carNum" jdbcType="VARCHAR" />
    <result column="state_id" property="stateId" jdbcType="INTEGER" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <!--删除状态为3的数据-->
  <delete id="deleteDataByState">
        delete from white_car_record
        where state_id=3
  </delete>

  <select id="findRecordByPage" parameterType="com.fwy.intermediary.entity.show.WhiteList" resultMap="BaseResultMap">
    select * from white_car_record
    <where>
        state_id=1
      <if test="startDate != null">
        and create_time >= #{startDate}
      </if>
      <if test="deptIds != null and deptIds.size() != 0">
        and dept_id in
        <foreach collection="deptIds" item="deptId" index="index"
                 open="(" close=")" separator=",">
          #{deptId}
        </foreach>
      </if>
      <if test="endDate != null">
        <!--&lt;小于-->
        and create_time &lt;= #{endDate}
      </if>
      <if test="carNum != null and carNum !=''">
        and car_num like concat('%', #{carNum}, '%')
      </if>
    </where>
    order by create_time desc
  </select>

  <sql id="Base_Column_List" >
    code, car_num, state_id, update_user, update_time, create_user, create_time
  </sql>

  <!--判断白名单车辆是否存在-->
  <select id="ifExists" resultType="java.lang.Integer" parameterType="java.lang.String">
    select exists
    (select car_num from white_car_record
    where car_num = #{carNum,jdbcType=VARCHAR})
  </select>

  <!--查询所有白名单车辆 state_id = 1-->
  <select id="getList" resultMap="BaseResultMap">
    select *
    from white_car_record
    where state_id = 1
  </select>

  <insert id="save" parameterType="com.fwy.intermediary.entity.WhiteCarRecord" >
    insert into white_car_record (code, car_num, state_id,
      update_user, update_time, create_user,
      create_time,dept_id,dept_name)
    values (#{code,jdbcType=VARCHAR}, #{carNum,jdbcType=VARCHAR}, #{stateId,jdbcType=INTEGER},
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP},#{deptId},#{deptName})
  </insert>

  <select id="selectByCarNum" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from white_car_record
    where car_num = #{carNum,jdbcType=VARCHAR}
  </select>

  <!--通过车牌删除数据 -->
  <delete id="deleteByCarNum" parameterType="java.lang.String" >
    delete from white_car_record
    where car_num = #{carNum,jdbcType=VARCHAR}
  </delete>

  <insert id="insertSelective" parameterType="com.fwy.intermediary.entity.WhiteCarRecord" >
    insert into white_car_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        code,
      </if>
      <if test="carNum != null" >
        car_num,
      </if>
      <if test="stateId != null" >
        state_id,
      </if>
      <if test="updateUser != null" >
        update_user,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="createUser != null" >
        create_user,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null" >
        #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="stateId != null" >
        #{stateId,jdbcType=INTEGER},
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fwy.intermediary.entity.WhiteCarRecord" >
    update white_car_record
    <set >
      <if test="carNum != null" >
        car_num = #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="stateId != null" >
        state_id = #{stateId,jdbcType=INTEGER},
      </if>
      <if test="updateUser != null" >
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null" >
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where code = #{code,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fwy.intermediary.entity.WhiteCarRecord" >
    update white_car_record
    set car_num = #{carNum,jdbcType=VARCHAR},
      state_id = #{stateId,jdbcType=INTEGER},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where code = #{code,jdbcType=VARCHAR}
  </update>

  <select id="countAll" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM white_car_record where 1=1
    <if test="deptIds != null and deptIds.size() != 0">
      and dept_id in
      <foreach collection="deptIds" item="deptId" index="index"
               open="(" close=")" separator=",">
        #{deptId}
      </foreach>
    </if>
  </select>
    <select id="ifExistsByDept" resultType="java.lang.Integer">
      SELECT exists(select code from white_car_record
      where car_num = #{carNum,jdbcType=VARCHAR} AND dept_id = #{deptId})
    </select>
    <select id="getCarWhiteByDept" resultType="com.fwy.intermediary.entity.WhiteCarRecord">
      select code from white_car_record
      where car_num = #{carNum,jdbcType=VARCHAR} AND dept_id = #{deptId}
    </select>
</mapper>