<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="UTF-8">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<div th:replace="Importfile::html"></div>
</head>
<body>
<form class="layui-form" action="">
<input type="hidden" id="id" name="id" th:value="${user.id}" />
<input type="hidden" id="deptId" name="deptId" th:value="${user.deptId}" />
<input type="hidden" id="isStart" name="isStart" th:value="${user.isStart}" />
<input type="hidden" id="isSys" name="isSys" th:value="${user.isSys}" />
<input type="hidden" id="password" name="password" th:value="${user.password}" />
<div class="layui-fluid">
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">设置我的资料</div>
        <div class="layui-card-body" pad15="">
          
          <div class="layui-form" lay-filter="">
            <div class="layui-form-item" style="margin-bottom: 30px;">
              <label class="layui-form-label">所属部门</label>
              <div class="layui-input-inline">
                <input type="text" name="deptName" th:value="${user.dept.deptName}" readonly="" class="layui-input">
              </div>
             
            
            <label class="layui-form-label" style="margin-left: 80px;">用户名</label>
              <div class="layui-input-inline">
                <input type="text" name="userName" th:value="${user.userName}" readonly="" class="layui-input">
              </div>
             
            </div>
            <div class="layui-form-item" style="margin-bottom: 30px;">
              <label class="layui-form-label" >姓名</label>
              <div class="layui-input-inline">
                <input type="text" name="fullName" th:value="${user.fullName}" lay-verify="required|fullnameLength" autocomplete="off" placeholder="请输入姓名" class="layui-input">
              </div>
               <label class="layui-form-label" style="margin-left: 80px;">身份证号</label>
              <div class="layui-input-inline">
                <input type="text" name="idCardNumber" th:value="${user.userInfo.idCardNumber}" lay-verify="identity|uniqueCard" autocomplete="off" placeholder="请输入身份证号" class="layui-input">
              </div>
            </div>
            
            <div class="layui-form-item" style="margin-bottom: 30px;">
               <label class="layui-form-label">手机号</label>
              <div class="layui-input-inline" >
                <input type="text" name="phone" th:value="${user.phone}" lay-verify="required|phone" autocomplete="off" placeholder="请输入手机号" class="layui-input">
              </div>
              
            </div>
           
             
            <div class="layui-form-item">
              <div class="layui-input-block">
                <button class="layui-btn" lay-submit="" lay-filter="setmyinfo">确认修改</button>
                <button class="layui-btn layui-btn-normal" id="cancelBtn" >返回</button>
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
  </div>
</div>
</form>


 <script>
 //监听提交
 
 layui.use(['form', 'layedit', 'laydate'], function(){
  var form = layui.form;
  //自定义验证规则
   form.verify({
	   uniqueCard:function(value){
	    	 var id = $("#id").val();
  		 var checkMsg = '';
  		 var url = ctx +  "userController/uniqueIdCard?idCardNumber="+value+ "&id="+id;
  		 $.ajax({
				 url : url,
				 datatype : 'json',
				 async: false,
				 success : function(result) {
					 if (result) {
						 checkMsg += '身份证重复';
						 return checkMsg;
					 }
				    },error : function() {
						layer.msg("身份证验证失败");
					}
			});
  		 if(checkMsg != ''){
  	        return checkMsg;
  		 }	
     },
     fullnameLength:function(value){
    	 	if (value.length < 2) {
                 	return "用户姓名长度2到20个字符";
             	}
             	if (value.length > 20) {
                 	return "用户姓名长度2到20个字符";
             	}
	     }
  });
  
// 回到首页
	$('#cancelBtn').click(function(){
		parent.layui.admin.events.closeThisTabs();
	})
 form.on('submit(setmyinfo)', function(data){
	 
	   $.ajax({
			url : ctx+'userController/saveSelfInfo',
			type : 'POST',
			data : data.field,
			async : false,
			cache : false,
			dataType : 'json',
			/* beforeSend: function () {
			        // 禁用按钮防止重复提交
			        $(".layui-btn").attr({ disabled: "disabled" });
			    }, */
			success : function(result) {
				if (result.code != 0) {
					layer.alert(result.msg, {icon: 2});      						
				} else {
					layer.alert(result.msg, {
						icon : 1
					},function(){
						parent.layui.admin.events.closeThisTabs();
					});
				}
			},
			error : function(result) {
				layer.alert(result.msg, {
					icon : 5,
					skin : 'layer-ext-moon'
				});
			}
		});   
	   return false;
 });
 
 });
 </script>

</body>
</html>