<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>添加流程项</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <script type="text/javascript" th:src="@{/scripts/dic/operationflow/add.js}"></script>

</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" action="">
                <input type="hidden" name="id" th:value="${flow?.id}" id="id">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>节点名称</label>
                        <div class="layui-input-inline" style="width:250px">
                            <input type="text" name="title" th:value="${flow?.title}" lay-verify="required" autocomplete="off" class="layui-input" maxlength="50">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>节点路径</label>
                        <div class="layui-input-inline" style="width:250px">
                            <input type="text" name="router" th:value="${flow?.router}" lay-verify="required" autocomplete="off" class="layui-input" maxlength="100">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>倒计时</label>
                        <div class="layui-input-inline" style="width:250px">
                            <input type="text" name="timeout" th:value="${flow?.timeout}" lay-verify="required|number" autocomplete="off" class="layui-input" maxlength="4">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>节点描述</label>
                        <div class="layui-input-inline" style="width:250px">
                            <input type="text" name="description" th:value="${flow?.description}" lay-verify="required" autocomplete="off" class="layui-input" maxlength="50">
                        </div>
                    </div>
                </div>


                <!--<div class="layui-form-item">
                    <label class="layui-form-label">开关-默认关</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="close" lay-skin="switch" lay-text="ON|OFF">
                    </div>
                </div>-->

                <button style="display:none" lay-submit lay-filter="subBtn" id="subBtn"></button>
            </form>
        </div>
    </div>
</div>

</body>
</html>