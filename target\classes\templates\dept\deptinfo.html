<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
>
<head>
    <meta charset="utf-8">
    <title>添加部门</title>
    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}"
          media="all">
    <script th:src="@{/admin/layui/layui.js}" charset="utf-8"></script>
    <script th:src="@{/plugins/formSelects/xm-select.js}" charset="utf-8"></script>
    <script th:src="@{/plugins/jquery/jquery-3.4.1.min.js}" charset="utf-8"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .layui-form-item .layui-input-inline {
            width: 260px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title"
                      style="margin-top: 20px;">
                <legend>部门信息</legend>
            </fieldset>

            <form class="layui-form" action="">
                <input type="hidden" name="id" th:value="${dept==null?'':dept.id}"/>
                <input type="hidden" name="orderCode" th:value="${dept==null?'':dept.orderCode}"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>部门名称：</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="deptName" placeholder="请输入部门名称"
                                   oninput="del(this,'blank|char')" lay-verify="required|deptName"
                                   autocomplete="off" class="layui-input" th:value="${dept==null?'':dept.deptName}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span
                                style="color:red">*</span>上级部门：</label>
                        <div class="layui-input-inline">
                            <input type="hidden" id="parentId" th:value="${dept==null?'':dept.parentId}"/>
                            <input type="hidden" name="parentDeptName" id="parentDeptName"
                                   th:value="${dept==null?'':dept.parentName}"/>
                            <div class="xm-select" id="parentIdSelect" style="width: 260px;">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>部门编号：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="deptCode" autocomplete="off"
                                   oninput="del(this,'blank|char')" placeholder="请输入部门编号"
                                   class="layui-input" lay-verify="required|deptCode"
                                   th:value="${dept==null?'':dept.deptCode}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>部门类型：</label>
                        <div class="layui-input-inline">
                            <div class="xm-select" id="deptTypeSelect" style="width: 260px;">
                            </div>
                            <input type="hidden" id="deptType" th:value="${dept?.deptType}">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline" th:if="${session.user != null &&session.user.isSys==1}">
                        <label class="layui-form-label">是否显示：</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="isShow" value="1" lay-skin="switch"
                                   checked="dept.isShow==1?1:0"
                                   lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>部门级别：</label>
                        <div class="layui-input-inline">
                            <div class="xm-select" id="deptLevelSelect" style="width: 260px;">
                            </div>
                            <input type="hidden" id="LEVEL" th:value="${dept?.LEVEL}">
                        </div>
                    </div>
                </div>

                <div class="layui-inline" th:if="${session.user != null &&session.user.isSys==1}">
                    <label class="layui-form-label">是否系统级:</label>
                    <div class="layui-input-inline">
                        <input type="checkbox" value="1" th:attr="checked=${dept?.isSys == 1 ? 1 : 0}"
                               name="isSys" id="isSys" lay-skin="switch" lay-text="是|否">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">部门描述：</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入内容" name="remark" class="layui-textarea"
								  lay-verify="remark"
                                  oninput="del(this,'blank|char')"
                                  th:text="${dept==null?'':dept.remark}"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit="" lay-filter="demo1">保存</button>
                        <button class="layui-btn layui-btn-normal" id="cancelBtn" >返回</button>

                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script th:inline="javascript">
    var ctx = [[${#servletContext.contextPath}]] + '/';
</script>
<script>
    layui.use(['form', 'layedit', 'laydate', 'jquery'],
        function () {
            var form = layui.form, layer = layui.layer, layedit = layui.layedit, laydate = layui.laydate,
                $ = jQuery = layui.$;
            // 回到列表页面
            $('#cancelBtn').click(function () {
                window.parent.changTabs(ctx + 'deptController/deptList', '', '部门管理');
            })
            form.verify({
				deptName: function (value, item) {
                    if (value.length > 20) {
                        return '部门名称不能超过' + 20 + '个字符的长度';
                    }
                },
				deptCode: function (value, item) {
					if (top.window.parent.getBytes(value) > 20) {
						return '部门编号不能超过' + 20 + '个字符的长度';
					}
				},
				remark: function (value, item) {
					if (top.window.parent.getBytes(value) > 200) {
						return '部门描述不能超过' + 200 + '个字符的长度';
					}
				}
            });
            //监听提交
            form.on('submit(demo1)', function (data) {
                $.ajax({
                    type: "POST",
                    url: "saveDept",
                    data: data.field,
                    dataType: "json",
                    success: function (data) {
                        if (data.code == '0') {
                            layer.alert(data.msg, {
                                    icon: 6, skin: 'layer-ext-moon', closeBtn: 0
                                },
                                function () {
                                    window.parent.changTabs(ctx + 'deptController/deptList', '', '部门管理');
                                });
                        } else {
                            layer.msg(data.msg, {
                                icon: 5,
                                skin: 'layer-ext-moon'
                            });
                        }
                    },
                    error: function (data) {
                        layer.msg(data.msg, {
                            icon: 5,
                            skin: 'layer-ext-moon'
                        });
                    }
                });
                return false;
            });
        });

    function reloadXmselect() {
        jQuery.ajax({
            url: ctx + '/deptController/tree',
            data: {"deptId": $("#parentId").val()},
            method: "get",
            dataType: 'json',
            success: function (response) {
                dialog_dept.update({
                    data: response.data
                })
            },
            error: function (res) {
            }
        });
        jQuery.get(ctx + '/dataDetailController/formSelectByCode?dicCode=deptType&id='+$("#deptType").val(),function (res) {
            dialog_deptType.update({
                data:res.data,
            })
        });

        jQuery.get(ctx + '/dataDetailController/formSelectByCode?dicCode=deptLevel&id='+$("#LEVEL").val(),function (res) {
            deptLevelSelect.update({
                data:res.data,
            })
        });
    }

    jQuery(function () {
        reloadXmselect();
    });
    //部门 下拉框初始化
    var dialog_dept = xmSelect.render({
        el: '#parentIdSelect',
        filterable: true,
        name: 'parentId',
        tips: '请选择',
        layVerify: 'required',
        layVerType: 'msg',
        // layVerify: 'required|uniqueDept',
        // layVerify: 'required',
        // layVerType: 'msg',
        model: {label: {type: 'block'}},
        template:function(item) {
            return '<p title="' + item.name + '">' + item.name + '</p>';
        },
        on: function (data) {
            //arr:  当前多选已选中的数据
            var arr = data.arr;
            //change, 此次选择变化的数据,数组
            var change = data.change;
            //isAdd, 此次操作是新增还是删除
            var isAdd = data.isAdd;
            if (isAdd) {
                //选中则赋值
                $("#parentDeptName").val(arr[0].deptName);
            } else {
                //清空
                $("#parentDeptName").val("");
            }
        },
        // cascader: {
        //     //是否显示级联模式
        //     show: true,
        //     //间距
        //     indent: 200,
        //     //是否严格遵守父子模式
        //     strict: true,
        // },
        // showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
        // tree //开启树结构
        radio: true,//单选多选
        tree: {
            show: true,
            strict: false, //是否父子结构，父子结构父节点不会被选中
            indent: 30,//间距
            expandedKeys: [-1],
            clickCheck: true,
            clickExpand: true,//点击展开
        },
        clickClose: true,//点击关闭
        // autoRow: true,
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width: 'auto'
        },
        prop: {
            name: "deptName",
            value: "ID"
        },
        height: '200px',
        empty: '暂无数据',
        data: [],
        direction: 'auto',
    });
    //部门类型 下拉框初始化
    var dialog_deptType = xmSelect.render({
        el: '#deptTypeSelect',
        // filterable: true,
        name: 'deptType',
        tips: '请选择',
        // layVerify: 'required|uniqueDept',
        layVerify: 'required',
        layVerType: 'msg',
        model: {label: {type: 'block'}},
        template:function(item) {
            return '<p title="' + item.name + '">' + item.name + '</p>';
        },
        on: function (data) {
            //arr:  当前多选已选中的数据
            var arr = data.arr;
            //change, 此次选择变化的数据,数组
            var change = data.change;
            //isAdd, 此次操作是新增还是删除
            var isAdd = data.isAdd;
            // if (isAdd){
            //
            //     var value = dialog_dept.getValue();
            //     console.log(value)
            // }

            // alert('已有: ' + arr.length + ' 变化: ' + change.length + ', 状态: ' + isAdd)
        },
        // cascader: {
        //     //是否显示级联模式
        //     show: true,
        //     //间距
        //     indent: 200,
        //     //是否严格遵守父子模式
        //     strict: true,
        // },
        // showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
        // tree //开启树结构
        radio: true,//单选多选
        tree: {
            show: true,
            strict: false, //是否父子结构，父子结构父节点不会被选中
            indent: 30,//间距
            expandedKeys: [-1],
            clickCheck: true,
            clickExpand: true,//点击展开
        },
        clickClose: true,//点击关闭
        // autoRow: true,
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width: 'auto'
        },
        prop: {
            name: "disCription",
            value: "dicValue"
        },
        height: '200px',
        empty: '暂无数据',
        data: [],
        direction: 'auto',
    });

    //部门类型 下拉框初始化
    var deptLevelSelect = xmSelect.render({
        el: '#deptLevelSelect',
        // filterable: true,
        name: 'LEVEL',
        tips: '请选择',
        // layVerify: 'required|uniqueDept',
        layVerify: 'required',
        layVerType: 'msg',
        model: {label: {type: 'block'}},
        template:function(item) {
            return '<p title="' + item.name + '">' + item.name + '</p>';
        },

        radio: true,//单选多选
        clickClose: true,//点击关闭
        // autoRow: true,
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width: 'auto'
        },
        prop: {
            name: "disCription",
            value: "dicValue"
        },
        height: '200px',
        empty: '暂无数据',
        data: [],
        direction: 'auto',
    });


</script>
</body>
</html>