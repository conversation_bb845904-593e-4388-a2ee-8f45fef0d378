<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>客户端日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/plugins/dtree/dtree.css}">
    <link rel="stylesheet" th:href="@{/plugins/dtree/font/dtreefont.css}">
    <style>

    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">

            <div>
                    <div class="layui-inline">
                        <label class="layui-form-label ">日志描述：</label>
                        <div class="layui-input-inline ">
                            <input id="logDesc" class="layui-input" type="text" placeholder="请输入日志描述">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label" >开始日期：</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="beginTime" autocomplete="off" placeholder="请输入开始日期">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" >结束日期：</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="endTime" autocomplete="off" placeholder="请输入结束日期">
                        </div>
                    </div>
            </div>
                <div >
                <div class="layui-inline">
                    <label class="layui-form-label " >机器名称：</label>
                    <div class="layui-input-inline ">
                        <input id="jqmc" class="layui-input" type="text" placeholder="请输入机器名称">
                    </div>
                </div>
                    <div class="layui-inline" >
                        <label class="layui-form-label " ></label>
                        <button class="layui-btn icon-btn " id="searchBtn"><i
                                class="layui-icon">&#xe615;</i>查询
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary " id="unsetBtn"><i
                                class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </blockquote>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md2">
                    <div style="margin-top: 10px;">
                        <ul id="dataTree" class="dtree" th:data-id="${deptId}"></ul>
                    </div>
                </div>
                <div class="layui-col-md10">
                    <table class="layui-hide" id="log-table" lay-filter="log-table"></table>
                </div>
            </div>
            <script type="text/html" id="test-table-toolbar-barDemo">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail">详情</a>
            </script>
        </div>
    </div>
</div>
<script>
    layui.config({
        base: ctx + '/plugins/soulTable/'
    }).extend({
        dtree: 'dtree',
        soulTable: 'soulTable'
    });
    layui.use(['table', 'layer', 'form', 'jquery', 'laydate','dtree'], function () {
        var admin = layui.admin;
        var $ = layui.jquery;
        var form = layui.form;
        var dtree = layui.dtree;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var table = layui.table;
        table.render({
            elem: '#log-table'
            , url: ctx + '/clientLogController/logJson'
            , toolbar: '#test-table-toolbar-toolbarDemo'
            , title: '操作日志表'
            , cols: [
                [
                    {title: '操作', toolbar: '#test-table-toolbar-barDemo',fixed:'left', width: 100,align:'center'}
                    , {field: 'jqmc', title: '机器名称', width: 200,align:'center'}
                    , {field: 'logDesc', title: '日志描述', width: 200,align:'center'}
                    , {field: 'logResult', title:  '日志详情', width: 200,align:'center'}
                    /*, {field: 'status', title: '状态', width: 150}*/
                    /*, {
                    field: 'userName', title: '操作用户', templet: function (data) {
                        return data.user.fullName
                    }, width: 150
                    }*/
                    , {field: 'logTime', title: '生成时间',width:200,align:'center',templet:function(data){
                        return ''+layui.util.toDateString(data.logTime, 'yyyy-MM-dd HH:mm:ss');
                    }}

                ]
            ]
            , request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true
        });

//初始化树
        var DTreeNode = dtree.render({
            elem: "#dataTree",
            url: ctx + "/deptController/depTreeJson", // 使用url加载
            dataStyle: "layuiStyle",  //使用layui风格的数据格式
            dataFormat: "list",  //配置data的风格为list
            response: {message: "msg", statusCode: 0},  //修改response中返回数据的定义
            checkbar: true,//开启复选框
            checkbarType: "no-all",
            initLevel: 2,
            line: true, // 有树线
            ficon: "2",  // 设定一级图标样式。0表示方形加减图标，7表示文件图标
            icon: "-1", // 不设定二级图标样式。
            skin: "laySimple"
        });
        // 绑定节点点击事件
        dtree.on("node(dataTree)", function (obj) {
            var begintime = $("#begintime").val();
            var endtime = $("#endtime").val();
            var logDesc = $("#logDesc").val();
            var jqmc = $("#jqmc").val();
            var nodeId = obj.param.nodeId;
            var nodeIds = [];
            DTreeNode.clickNodeCheckbar(nodeId);// 点击节点选中复选框
            var checkbarNodes = dtree.getCheckbarNodesParam("dataTree");
            for (var i = 0; i < checkbarNodes.length; i++) {
                nodeIds[i] = checkbarNodes[i].nodeId;
            }

            table.reload('log-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    nodeIds: JSON.stringify(nodeIds)
                }
            });
        });

        // 绑定用复选框点击事件
        dtree.on("chooseDone(dataTree)", function (obj) {
            var begintime = $("#begintime").val();
            var endtime = $("#endtime").val();
            var logDesc = $("#logDesc").val();
            var jqmc = $("#jqmc").val();
            debugger;
            var nodeId = obj.checkbarParams;

            var nodeIds = [];
            for (var i = 0; i < nodeId.length; i++) {
                nodeIds[i] = nodeId[i].nodeId;
            }
            table.reload('log-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    nodeIds: JSON.stringify(nodeIds)
                }
            });
        });

        //日期
        laydate.render({
            elem: '#beginTime',
            type: 'datetime'
        });
        laydate.render({
            elem: '#endTime',
            type: 'datetime'
        });


        //监听行工具事件
        table.on('tool(log-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.open({
                    type: 2,
                    title: "查看日志详情",
                    btn:"返回",
                    shadeClose: true,
                    area: ['70%', '75%'],
                    offset: [ //为了演示，随机坐标
                        0.1 * ($(window).height() - 400)
                    ]
                    , content: ctx + '/clientLogController/logdetail?ID=' + data.id
                });
            }
        });

        //搜索及重置按钮
        $("#searchBtn").click(function () {
            var logDesc = $("#logDesc").val();
            var beginTime = $("#beginTime").val();
            var endTime = $("#endTime").val();
            var jqmc = $("#jqmc").val();
            table.reload('log-table', {
                where: { //设定异步数据接口的额外参数，任意设
                      logDesc: logDesc
                    , beginTime: beginTime
                    , endTime: endTime
                    , jqmc:jqmc
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#logDesc").val("");
            $("#beginTime").val("");
            $("#endTime").val("");
            $("#jqmc").val("");
            table.reload('log-table', {
                where: null
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            }); //只重载数据
        })

    });
</script>

</body>
</html>