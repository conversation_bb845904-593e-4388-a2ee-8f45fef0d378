/*.uploadImgBtn {*/
/*    width: 100px;*/
/*    height: 100px;*/
/*    cursor: pointer;*/
/*    position: relative;*/
/*    right: -180px;*/
/*    top:0;*/
/*    background: url("../images/black.png") no-repeat;*/
/*    -webkit-background-size: cover;*/
/*    background-size: cover;*/
/*}*/
.metric-icon {
    font-size: 40px;
    color: #00a0e9;
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(0, 160, 233, 0.5);
}

.metric-card {
    text-align: center;
    position: relative;
    width: 45%;
}
/* 已处理数量展示 */
.car_processed_value_icon {
    width: 180px;
    height: 180px;
    background: url("../images/carProcessedValueIcon.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    margin-top: 30px;
    position: relative;
}

.person_processed_value_icon {
    width: 180px;
    height: 180px;
    background: url("../images/personProcessedValueIcon.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    margin-top: 30px;
    position: relative;
}

/* 黑白名单图标样式 */
.blacklist-icon {
    width: 140px;
    height: 140px;
    background: url("../images/black.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    margin-top: 10px;
    position: relative;
}

.whitelist-icon {
    width: 140px;
    height: 140px;
    background: url("../images/white.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    margin-top: 10px;
    position: relative;
}

/* 黑白名单数量值定位 */
.blacklist-value-container, .whitelist-value-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.bl-wl-value {
    font-size: 16px;
    font-weight: bold;
    color: #00a0e9;
    text-shadow: 0 0 10px rgba(0, 160, 233, 0.5);
}

.bl-wl-label {
    font-size: 11px;
    color: #99ccff;
}

/* 车辆统计图标样式 */
.processed-total-icon {
    height: 140px;
    background: url("../images/processedTotalIcon.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    position: relative;
}

.processed-car-icon {
    height: 140px;
    background: url("../images/processeCar.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    position: relative;
}

.processed-person-icon {
    height: 140px;
    background: url("../images/processePerson.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    position: relative;
}

/*预警情况*/
.suspected_alert_value_icon {
    /*width: 125px;*/
    height: 140px;
    background: url("../images/suspectedAlertValueIcon.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    position: relative;
}

.suspected_processed_value_icon {
    /*width: 125px;*/
    height: 140px;
    background: url("../images/suspectedProcessedValueIcon.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    position: relative;
}

.blacklist_alert_value_icon {
    /*width: 125px;*/
    height: 140px;
    background: url("../images/blacklistAlertValueIcon.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    position: relative;
}

.blacklist_processed_value_icon {
    /*width: 125px;*/
    height: 140px;
    background: url("../images/blacklistProcessedValueIcon.png") no-repeat center center;
    background-size: contain;
    margin: 0 auto;
    position: relative;
}

/* 预警情况值定位 */
.alert-icon-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.alert-value-title {
    font-size: 11px;
    color: #99ccff;
    margin-bottom: 20px;
    position: relative;
    top: -24px;
    white-space: nowrap;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.alert-value-number {
    font-size: 16px;
    /*font-weight: bold;*/
    color: #00a0e9;
    text-shadow: 0 0 10px rgba(0, 160, 233, 0.5);
    /*background-color: rgba(0, 36, 76, 0.7);*/
    padding: 1px 10px;
    border-radius: 10px;
    position: relative;
    top: 35px;
    min-width: 40px;
    max-width: 80px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.alert-value-number2 {
    font-size: 16px;
    /*font-weight: bold;*/
    color: #FFBB00;
    text-shadow: 0 0 10px rgba(0, 160, 233, 0.5);
    padding: 1px 10px;
    border-radius: 10px;
    position: relative;
    top: 35px;
    min-width: 40px;
    max-width: 80px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 预警卡片样式 */
.alert-cards {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-around;
    align-items: center;
    padding: 10px 0;
    width: 100%;
}

.alert-card {
    width: 22%;
    text-align: center;
    margin: 0 5px;
    position: relative;
    overflow: visible;
}

.circle-animation {
    position: relative;
}

.circle-animation::before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    border: 1px dashed rgba(0, 160, 233, 0.5);
    animation: rotate 20s linear infinite;
    z-index: -1;
}

.circle-animation::after {
    content: "";
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 1px dashed rgba(0, 160, 233, 0.7);
    animation: rotate 15s linear infinite reverse;
    z-index: -1;
}

/* 车辆统计信息卡片样式 */
.stat-cards {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-around;
    align-items: center;
    padding: 5px 0;
}

.stat-card {
    width: 31%;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stat-icon-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.stat-title {
    font-size: 11px;
    color: #99ccff;
    margin-bottom: 20px;
    position: relative;
    top: -22px;
    white-space: nowrap;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #00a0e9;
    text-shadow: 0 0 10px rgba(0, 160, 233, 0.5);
    padding: 1px 10px;
    border-radius: 10px;
    position: relative;
    top: 35px;
    min-width: 40px;
    max-width: 80px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 实时预警列表样式 */
#alertTable {
    font-size: 11px;
    color: #FFFFFF;
    border-collapse: collapse;
}

#alertTable th, 
#alertTable td {
    font-size: 11px;
    color: #FFFFFF;
    border: none;
    padding: 5.2px 6px;
}

#alertTable thead th {
    background-color: rgba(0, 76, 153, 0.3);
    color: #00a0e9;
    font-weight: normal;
}


#faceAlertTable {
    font-size: 11px;
    color: #FFFFFF;
    border-collapse: collapse;
}

#faceAlertTable th,
#faceAlertTable td {
    font-size: 11px;
    color: #FFFFFF;
    border: none;
    padding: 3px 6px;
}

#faceAlertTable thead th {
    background-color: rgba(0, 76, 153, 0.3);
    color: #00a0e9;
    font-weight: normal;
    border-bottom: 1px solid rgba(0, 76, 153, 0.3);
}

#faceAlertTable tr:hover {
    background-color: rgba(0, 160, 233, 0.1);
}

#carAlertTable {
    font-size: 11px;
    color: #FFFFFF;
    border-collapse: collapse;
}

#carAlertTable th,
#carAlertTable td {
    font-size: 11px;
    color: #FFFFFF;
    border: none;
    padding: 5px 6px;
}

#carAlertTable thead th {
    background-color: rgba(0, 76, 153, 0.3);
    color: #00a0e9;
    font-weight: normal;
    border-bottom: 1px solid rgba(0, 76, 153, 0.3);
}

#carAlertTable tr:hover {
    background-color: rgba(0, 160, 233, 0.1);
}

.data-table {
    width: 100%;
    border-spacing: 0;
    color: #fff;
    border-collapse: collapse;
}

.data-table th {
    background-color: rgba(0, 70, 140, 0.5);
    color: #00a0e9;
    /*padding: 8px 6px;*/
    font-weight: normal;
    border: none;
}

.data-table td {
    /*padding: 8px 6px;*/
    text-align: center;
    border: none;
}

.data-table img.avatar {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #0a4c82;
    box-shadow: 0 0 5px rgba(0, 171, 255, 0.5);
}

.data-table tr:nth-child(odd) {
    background-color: rgba(0, 60, 120, 0.2);
}

.data-table tr:nth-child(even) {
    background-color: rgba(0, 60, 120, 0.1);
}


.fore_icon {
    width: 22px;
    height: 22px;
    /*background: linear-gradient(135deg, #607d8b 60%, #b0bec5 100%);*/
    background: url("../images/fore.png") no-repeat center center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    /*box-shadow: 0 0 6px #607d8b99;*/
    /*border: 1px solid #607d8b;*/
    position: relative;
}
.three_icon {
    width: 22px;
    height: 22px;
    /*background: linear-gradient(135deg, #ffb300 60%, #fff176 100%);*/
    background: url("../images/three.png") no-repeat center center;;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    /*box-shadow: 0 0 6px #ffb30099;*/
    /*border: 1px solid #ffb300;*/
    position: relative;
}
.three_icon span, .fore_icon span {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #fff;
    position: absolute;
    left: 0;
    top: 0;
}

/*.title_icon {*/
/*    background: url("../images/title.png") no-repeat center center;*/
/*    background-size: 100% 100%;*/
/*    display: flex;*/
/*    align-items: center;*/
/*    !*justify-content: center;*!*/
/*    padding-left: 30px;*/
/*    font-size: 16px;*/
/*    position: relative;*/
/*    border-top-left-radius: 5px;*/
/*    border-top-right-radius: 5px;*/
/*    font-weight: bold;*/
/*    color: #fff;*/
/*    width: 100%;*/
/*    box-sizing: border-box;*/
/*    height: 30px;*/
/*    margin: 0;*/
/*}*/

.title_icon {
    background: url("../images/title.png") no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    /*align-items: center;*/
    /*justify-content: center;*/
    padding-left: 30px;
    font-size: 16px;
    position: relative;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    font-weight: bold;
    color: #fff;
    width: 100%;
    box-sizing: border-box;
    height: 30px;
    margin-bottom: 10px;
    align-items: center;
    justify-content: space-between;
}

.title_icon::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
}

.tab-buttons {
    display: flex;
    gap: 5px;
}
.tab-button {
    padding: 2px 8px;
    margin: 0;
    background-color: rgba(0, 60, 120, 0.5);
    border: 1px solid #004c7d;
    color: white;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    line-height: 1.2;
}
.tab-button.active {
    background-color: #00a0e9;
    box-shadow: 0 0 5px #00a0e9;
}

.section-title1 {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 20px;
    /*color: #03CDFF;*/
    color: #55FFF4;
    padding: 10px 0px;
    line-height: 30px;
    text-shadow: 0px 2px 2px rgba(0,0,0,0.3);
    text-align: center;
    font-style: normal;
    text-transform: none;
    /*background: linear-gradient(90.00000000000192deg, #55FFF4 0%, #6EDDFF 100%);*/
}

.section-title2 {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 20px;
    /*color: #03CDFF;*/
    color: #55FFF4;
    padding: 10px 0px;
    line-height: 30px;
    text-shadow: 0px 2px 2px rgba(0,0,0,0.3);
    text-align: center;
    font-style: normal;
    text-transform: none;
    /*background: linear-gradient(90.00000000000192deg, #55FFF4 0%, #6EDDFF 100%);*/
}

.big_title{

    background: url("../images/bigTitle.png") no-repeat center center;
    height: 55px;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-bottom: 10px;
}

/*弹窗图片*/
.header_title{
    background: url("../images/headerTitle.png") no-repeat center center;
    height: 40px;
    background-size: 100% 100%;
    display: flex;
    /*justify-content: center;*/
    align-items: center;
    position: relative;
    margin: 0;
    padding: 0;
    border: none;
}

.video-text {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    color: #00a0e9;
    text-shadow: 0 0 10px rgba(0, 160, 233, 0.5);
}
