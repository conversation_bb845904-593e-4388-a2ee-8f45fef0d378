$(function() {
	
	$("#imgs").click(
		function() {
			$("#imgs").attr("src", "./validateCode?rnd=" + Math.random());
		});
	
});

$(document).keydown(function(event) {
	if (event.keyCode == 13) {
		$("#btnLogin").click();
	}
});


$(function  () {
	layui.use('form', function(){
		var form = layui.form;
		//自定义验证规则
        form.verify({
            userName: function(value) {
            	if(value.length == 0){
            		return "请输入用户名";
            	}
                if (value.length < 3) {
                    return "用户名长度3到32个字符";
                }
                if (value.length > 32) {
                    return "用户名长度3到32个字符";
                }
            },
            password: function(value) {
            	if(value.length == 0){
            		return "请输入密码";
            	}
                if (value.length < 8) {
                    return "密码长度至少为8位";
                }
            },
            checkCode: function(value) {
            	if(value.length == 0){
            		return "请输入验证码";
            	}
            }
        });
		
		//监听提交
		form.on('submit(login)', function(data){
		    //密码加密
			data.field.password = RSAEncryptService($("#showpw").val());
			$("#password").val(data.field.password);
			// 生成数据散列码，在需要参与散列的input元素的class属性中添加signature
			data.field.signature = signature($("#ajaxform"));
			$("#signature").val(data.field.signature);
	  	 	$.ajax({   
                url:'./login',       
                method:'post',       
                data:data.field,        
                dataType:'JSON',
				beforeSend: function(){
					$('#showpw').attr("disabled",true);
					var subbtn = $('#btnLogin');
					subbtn.attr("disabled", true);
					subbtn.html("登 录 中");
				},
                success:function(res){
	      			   
                    if (res.code != 0) {
						$('#showpw').attr("disabled",false);
						var subbtn = $('#btnLogin');
						subbtn.attr("disabled", false);
						subbtn.html("登 录"); 
						/*if (res.data == 'userName') {
							var $input = $("#userName", $("#ajaxform"));
							$input.parent().append('<small data-bv-validator="notEmpty" data-bv-validator-for="userName" class="help-block">' + res.msg+ '</small>');    	      						 
							$input.parent().addClass("has-error");
							return false;
						}else if(res.data == 'password'){
							var $input = $("#showpw", $("#ajaxform"));
							$input.parent().append('<small data-bv-validator="notEmpty" data-bv-validator-for="showpw" class="help-block">' + res.msg+ '</small>');    	      						 
							$input.parent().addClass("has-error");
						}else if (res.data == 'checkCode') {
								var $input = $("#checkCode", $("#ajaxform"));
								$input.parent().append('<small data-bv-validator="notEmpty" data-bv-validator-for="checkCode" class="help-block">' + res.msg+ '</small>');    	      						 
								$input.parent().parent().addClass("has-error");
							$("#imgs").click(); //刷新验证码
							return false;
						}*/
						layer.msg(res.msg, {icon: 5});
						//现在登录失败就更新验证码
						// if (res.data == 'checkCode') {
							$("#imgs").click(); //刷新验证码
							return false;
						// }
						
					} else {
						window.location = res.data;   //执行页面跳转
					}
				},         
                error:function (res) {
                    $('#showpw').attr("disabled",false);
					var subbtn = $('#btnLogin');
					subbtn.attr("disabled", false);
					subbtn.html("登 录");
					layer.msg(res.ErrorMessage, {icon: 5, anim: 6});

                }           
          	});         
        });   
	});
})
