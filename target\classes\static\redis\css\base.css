html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    font-size: 13px;
}

body {
    padding: 0;
    margin: 0;
    font-family: "Roboto", sans-serif;
    font-size: 1rem;
    font-weight: normal;
    line-height: 1.5;
    color: #707070;
    background-color: #f6f6f6;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: #333;
}

*, ::before, ::after {
    box-sizing: inherit;
}

a {
    color: #707070;
    touch-action: manipulation;
    /*color: #03A9F4;*/
    text-decoration: none;
    background-color: transparent;
}

.main {
    position: relative;
    display: block;
}

article, aside, footer, header, nav, section {
    display: block;
}

.header {
    background-color: #32c787;
    position: fixed;
    width: 100%;
    height: 52px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    color: #FFFFFF;
    padding: 0 2rem;
    z-index: 10;
    display: flex;
    align-items: center;
    top: 0;
    left: 0;
}

.header-logo {
    min-width: calc(270px - 2rem);
}

.header-logo > h1 {
    line-height: 100%;
    font-size: 1.3rem;
    font-weight: normal;
}

.header-logo > h1 > a {
    color: #FFFFFF;
}

.side {
    width: 237px;
    position: fixed;
    left: 0;
    padding: 72px 2rem 0.5rem 2rem;
    height: 100%;
    overflow: hidden;
    z-index: 9;
}

.scrollbar-inner {
    height: 100%;
}

.navigation {
    margin-left: 0px;
    height: 80%;
    list-style: none;
    padding: 0;
}

.navigation > div:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.navigation div {
    padding-left: 1px;
}

.navigation span {
    display: block;
    line-height: 25px;
    padding: .5rem .5rem;
    cursor: pointer;
}

.content {
    padding: 60px 30px 0 270px;
}

.content-title {
    padding: 1rem 0 0 0;
    position: relative;
}

.breadcrumb {
    border-radius: 0;
    padding: 0.75rem 0.25rem 0 0;
    list-style: none;
    background-color: transparent;
    border-radius: 2px;
}

ol, ul, dl {
    margin-top: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item, .breadcrumb-item > a {
    color: #707070;
}

.breadcrumb-item {
    float: left;
}

.breadcrumb::after {
    display: block;
    content: "";
    clear: both;
}

.main-content {
    flex-grow: 1;
    min-height: calc(100vh - 15rem);
    box-sizing: border-box;
    background-color: #ffff;
}