/**
 * layui中常用的方法
 */


// /*****
//  * 打开弹出层方法
//  * @url 打开弹窗的地址
//  * @width 弹窗的宽
//  * @height 弹窗的高
//  * @submitButId 弹窗表单提交按钮的id 有#
//  */
// openWindow = function(url,title,width,heigth,submitButId){
// 		layer.open({
// 	  		type:2,
// 	  		title: title,
// 	  		area: [width, heigth],
// 	  		fixed: false, //不固定
// 	          maxmin: true,
// 	          content: url,
// 	          btn: ['确定', '取消'],
// 	          yes: function(index,layero){
// 	          	var submit = layero.find('iframe').contents().find(submitButId);
// 	          	submit.click();
// 	          	return false;
// 	          },
// 	          btn2: function(){
// 	            layer.closeAll();
// 	          },
// 	          zIndex: layer.zIndex, //重点1
// 	          success: function(layero,index){
// //	             layer.setTop(layero); //重点2
// //	        	 layer.iframeAuto(index);//弹窗自适应
// 	          }
// 	  	});
// };
//
// /*****
//  * 在弹出层中打开弹出层
//  * @url 打开弹窗的地址
//  * @width 弹窗的宽
//  * @height 弹窗的高
//  * @submitButId 弹窗表单提交按钮的id 有#
//  */
// openWindowTop = function(url,title,width,heigth,submitButId,flahTable){
// 	top.layer.open({
//   		type:2,
//   		title: title,
//   		area: [width, heigth],
//   		fixed: false, //不固定
//           maxmin: true,
//           content: url,
//           btn: ['确定', '取消'],
//           yes: function(index,layero){
//           	var submit = layero.find('iframe').contents().find(submitButId);
//           	submit.click();
//           	return false;
//           },
//           end: function () {
//               layui.table.reload(flahTable);
//              },
//           btn2: function(){
//             layer.closeAll();
//           },
//           zIndex: layer.zIndex, //重点1
//           success: function(layero,index){
// //             layer.setTop(layero); //重点2
// //        	 layer.iframeAuto(index); //打开窗口自适应
//           }
//   	});
// };
//
// /*****
//  * @url  接口地址
//  * @table  layerui.table  对象
//  * @tableName  重载表格的名称 没有#
//  */
// //  删除函数
//  deleteDemo =  function(url,table,tableName){
// 	 layer.confirm('是否删除',{icon:3,btn:['确定','取消'],title:'提示'},function(){
// 			var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
// 			$.ajax({
// 				url:url,
// 				success:function(res){
// 					layer.close(loading);
// 					 if(res.code == 0){
// 							layer.msg(res.msg,{icon: 1});
// 							layui.table.reload(tableName);
// 						}else{
// 							layer.msg(res.msg,{icon: 2});
// 						}
// 				},
// 				erro:function(res){
// 					layer.close(loading);
// 					layer.msg('操作失败',{icon: 2});
// 				}
// 			})
// 	 })
//  };
//  /**
//   * table表单数据提交方法
//   * @saveUrl 提交表单的地址
//   * @data 提交表单的数据data.field
//   * @tableName 重载表格的名称没有#号
//   */
//  submitForm = function(saveUrl,data,tableName){
// 	  console.log(saveUrl);
// 	  var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
// 	   $.ajax({
// 		         method:"POST",
// 			   	 url:saveUrl,       //提交表单的地址
// 			   	 data:data,      //提交表单的数据
// 			   	 success:function(res){
// 			   		 if(res.code == 0){
// 			   			 var index = parent.layer.getFrameIndex(window.name);
// 		   			      parent.layer.close(index);
// 		   			  	  parent.layer.msg(res.msg, { icon: 1});
// 		   			      parent.layui.table.reload(tableName);
// 					}else{
// 						  layer.msg(res.msg,{icon: 2});
// 				    }
// 			   	 },
// 			   	 error:function(){
// 			   		 console.log("操作失败");
// 			   		 layer.close(loading);
// 			   		 layer.msg('操作失败',{icon: 2});
// 			   	 }
// 			});
//  };
//  /**
//   * Tab iframe table表单数据提交方法
//   * @saveUrl 提交表单的地址
//   * @data 提交表单的数据data.field
//   * @flashUrl 刷新的页面地址
//   * @TabName 标签页名字
//   */
//  tabSubmitForm = function(saveUrl,data,flashUrl,TabName){
// 		var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
// 		$.ajax({
// 			type : "POST",
// 			url : saveUrl,
// 			data : data,
// 			dataType : "json",
// 			success : function(data) {
// 				console.log(data);
// 				if (data.code == 0) {
// 					layer.alert(data.msg, {
// 						icon : 1, skin: 'layer-ext-moon',closeBtn: 0},
// 						function(){
// 							window.parent.changTabs(flashUrl,'',TabName);
// 						});
// 				} else {
// 					 layer.msg(data.msg, {icon : 2,skin : 'layer-ext-moon'});
// 				}
// 				layer.close(loading);
// 			},
// 			error : function(data) {
// 			   layer.close(loading);
// 			   layer.msg('保存数据失败', {icon : 2,skin : 'layer-ext-moon'});
// 			}
// 		});
// 		return false; //防止提交两次表单
//  };

;!function (win) {
    "use strict";
    var doc = document

        ,Xadmin = function(){
        this.v = '1.1'; //版本号
    };

    Xadmin.prototype.init = function() {
        var tab_list = this.get_data();
        for(var i in tab_list){
            this.add_lay_tab(tab_list[i].title,tab_list[i].url,i);
        }
        element.tabChange('xbs_tab', i);
    };

    /**
     * [open 打开弹出层]
     * @param  {[type]}  title [弹出层标题]
     * @param  {[type]}  url   [弹出层地址]
     * @param  {[type]}  w     [宽]
     * @param  {[type]}  h     [高]
     * @param  {Boolean} full  [全屏]
     * @param  {[Boolean]} maxmin [最大最小化]
     * @return {[type]}        [description]
     */
    Xadmin.prototype.open = function (title,url,w,h,full,maxmin) {
        if (title == null || title == '') {
            var title=false;
        }
        if (w == null || w == '') {
            var w=($(window).width()*0.9);
        }
        if (h == null || h == '') {
            var h=($(window).height() - 50);
        }
        var index = layer.open({
            type: 2,
            area: [w+'px', h +'px'],
            fix: false, //不固定
            maxmin: maxmin,//放大缩小按钮
            shadeClose: true,
            shade:0.4,
            title: title,
            content: url
        });
        if(full){
            layer.full(index);
        }
    };
    /**
     * Tab iframe table表单数据提交方法
     * @param saveUrl   提交表单的地址
     * @param data      提交表单的数据data.field
     * @param flashUrl  刷新的页面地址
     * @param TabName   标签页名字
     * @returns {boolean}
     */
    Xadmin.prototype.tabSubmitForm = function(saveUrl,data,flashUrl,TabName){
        var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
        $.ajax({
            type : "POST",
            url : saveUrl,
            data : data,
            dataType : "json",
            success : function(data) {
                if (data.code == 0) {
                    layer.alert(data.msg, {icon : 1, skin: 'layer-ext-moon',closeBtn: 0},function(){
                            window.parent.changTabs(flashUrl,'',TabName);
                    });
                } else {
                    layer.msg(data.msg, {icon : 2,skin : 'layer-ext-moon'});
                }
                layer.close(loading);
            },
            error : function(data) {
                layer.close(loading);
                layer.msg('保存数据失败', {icon : 2,skin : 'layer-ext-moon'});
            }
        });
        return false; //防止提交两次表单
    };

    /**
     * 弹出框提交表单方式
     * 调用的地方需要使用return false;
     * @param saveUrl    提交表单的地址
     * @param data       提交表单的数据data.field
     * @param tableName  重载表格的名称没有#号
     */
    Xadmin.prototype.submitForm = function(saveUrl,data,tableName){
        var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
        $.ajax({
            method:"POST",
            url:saveUrl,       //提交表单的地址
            data:data,      //提交表单的数据
            success:function(res){
                if(res.code == 0){
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    parent.layer.msg(res.msg, { icon: 1});
                    parent.layui.table.reload(tableName);
                }else{
                    layer.msg(res.msg,{icon: 2});
                }
            },
            error:function(data){
                layer.close(loading);
                layer.msg('操作失败',{icon: 2});
            }
        });
        return false;
    };
    /**
     *  删除函数
     * @param url         接口地址
     * @param tableName   重载表格的名称 没有#
     */
    Xadmin.prototype.deleteDemo =  function(url,tableName){
        layer.confirm('是否删除',{icon:3,btn:['确定','取消'],title:'提示'},function(){
            var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
            $.ajax({
                url:url,
                success:function(res){
                    layer.close(loading);
                    if(res.code == 0){
                        layer.msg(res.msg,{icon: 1});
                        layui.table.reload(tableName);
                    }else{
                        layer.msg(res.msg,{icon: 2});
                    }
                },
                error:function(res){
                    layer.close(loading);
                    layer.msg('操作失败',{icon: 2});
                }
            })
        })
    };
    /*****
     * 在弹出层中打开弹出层
     * @url 打开弹窗的地址
     * @width 弹窗的宽
     * @height 弹窗的高
     * @submitButId 弹窗表单提交按钮的id 有#
     */
    /**
     *
     * @param url          提交地址
     * @param title        弹出框标题
     * @param w        弹出框宽度
     * @param h       弹出层高度
     * @param submitButId  提交表单的按钮id 有#
     * @param flahTable    刷新表单名称
     */
    Xadmin.prototype.openWindowTop = function(url,title,w,h,submitButId,flahTable){
        var flag = true;
        if (w == null || w == '') {
            var w=($(window).width()*0.9);
        }
        if (h == null || h == '') {
            var h=($(window).height() - 50);
        }
        top.layer.open({
            type:2,
            title: title,
            area: [w+'px', h+'px'],
            fixed: false, //不固定
            maxmin: true,
            content: url,
            btn: ['确定', '取消'],
            yes: function(index,layero){
                if (flag){
                    // alert(flag);
                    flag=false;
                    var submit = layero.find('iframe').contents().find(submitButId);
                    submit.click();
                    setTimeout(function () {
                        flag=true;
                    },1000);
                }
                return false;
            },
            end: function () {
                layui.table.reload(flahTable);
            },
            btn2: function(){
                layer.closeAll();
            },
            zIndex: layer.zIndex, //重点1
            success: function(layero,index){
//             layer.setTop(layero); //重点2
//        	 layer.iframeAuto(index); //打开窗口自适应
            }
        });
    };

    /**
     *
     * @param url            打开弹窗的地址
     * @param title          弹窗标题
     * @param w              弹窗宽度
     * @param h              弹窗高度
     * @param submitButId    提交表单按钮id 有#
     */
    Xadmin.prototype.openWindow = function(url,title,w,h,submitButId){
        var flag = true;
        if (w == null || w == '') {
            var w=($(window).width()*0.9);
        }
        if (h == null || h == '') {
            var h=($(window).height() - 50);
        }
        layer.open({
            type:2,
            title: title,
            area: [w+'px', h+'px'],
            fixed: false, //不固定
            maxmin: true,
            content: url,
            btn: ['确定', '取消'],
            yes: function(index,layero){
                if (flag){
                    flag=false;
                    var submit = layero.find('iframe').contents().find(submitButId);
                    submit.click();
                    setTimeout(function () {
                        flag=true;
                    },1000);
                }
                return false;
            },
            btn2: function(){
                layer.closeAll();
            },
            zIndex: layer.zIndex, //重点1
            success: function(layero,index){
//	             layer.setTop(layero); //重点2
//	        	 layer.iframeAuto(index);//弹窗自适应
            }
        });
    };


    /**
     *  全屏添加页面
     * @param url            打开弹窗的地址
     * @param title          弹窗标题
     * @param submitButId    提交表单按钮id 有#
     */
    Xadmin.prototype.openWindowFull = function(url,title,submitButId){
        var flag = true;
        var index = layer.open({
            type:2,
            title: title,
            area: ['100%','100%'],
            fixed: true, //不固定
            maxmin: false,
            content: url,
            btn: ['确定', '取消'],
            yes: function(index,layero){
                if (flag){
                    flag=false;
                    var submit = layero.find('iframe').contents().find(submitButId);
                    submit.click();
                    setTimeout(function () {
                        flag=true;
                    },1000);
                }
                return false;
            },
            btn2: function(){
                layer.closeAll();
            },
            zIndex: layer.zIndex, //重点1
            success: function(layero,index){
//	             layer.setTop(layero); //重点2
//	        	 layer.iframeAuto(index);//弹窗自适应
            }
        });
        layer.full(index);
    };
    /**
     *  打开详情窗口 只提供关闭按钮
     * @param url
     * @param title
     * @param width
     * @param heigth
     * @param submitButId
     */
    Xadmin.prototype.openWindowdetail = function(url,title,width,height,submitButId){
        layer.open({
            type:2,
            title: title,
            area: [width, height],
            fixed: false, //不固定
            maxmin: true,
            content: url,
            shade: 0.8,
            btn: ['取消'],
            btn1: function(){
                layer.closeAll();
            },
            zIndex: layer.zIndex, //重点1
            success: function(layero){
                layer.setTop(layero); //重点2
            }
        });
    }
    /**
     * 更改是否启用函数
     * @param url    后台地址
     */
    Xadmin.prototype.changeStart = function(url){
        $.ajax({
            url:url,
            dataType:'json',
            success:function(res){
                if(res.code == 0){
                    layer.msg(res.msg,{icon: 1});
                }else{
                    layer.msg(res.msg,{icon: 2});
                }
            },
            error:function(data){
                layer.msg('操作失败',{icon: 2});
            }
        });
    };
    /**
     * 删除空格和特殊字符
     * @param object input对象
     * @param args 要删除的参数 blank空格 char特殊字符
     */
    Xadmin.prototype.del = function (object,args) {
        var list = args.split('|');
        var msg = "";
        for(var j = 0,len=list.length; j < len; j++) {
            switch(list[j]) {
                case 'blank':
                    msg += delBlank(object);
                    break;
                case 'char':
                    msg += delChars(object);
                    break;
                default:
            }
        }
        if(msg!==''){
            layer.msg("请勿输入"+msg+"!", {icon : 5,shift : 6,time : 3000});
        }
    };
    win.xadmin = new Xadmin();

}(window);



//清除空格
function delBlank(object) {
    var blank = /[, ]/g;
    if(blank.test(object.value)){
        object.value=object.value.replace(blank,'');
        $(object).addClass("layui-form-danger");
        return "空格 ";
    }
    return "";
}
//清除特殊字符
function delChars(object) {
    // 放过了 ' 检测 防止输入中文时 '被删除
    var chars =  /^[^`~!@$%&*?:<>/\\|=+^{}\[\]\"【】‘’“”￥——、，。；：？《》！]*$/i;
    if(!chars.test(object.value)){
        object.value =  object.value.replace(new RegExp("[\\`,\\~,\\!,\\@,\\$,\\%,\\&,\\*,\\?,\\:,\\<,\\>,\\/,\\\\,\\|,\\=,\\+,\\^,\\{,\\},\\\[,\\\],\\【,\\】,\\‘,\\’,\\￥,\\—,\\、,\\，,\\。,\\；,\\：,\\？,\\《,\\》,\\！,\\“,\\”,\\\"]", "gm"), "");
        $(object).addClass("layui-form-danger");
        return "特殊字符 ";
    }
    return "";
}