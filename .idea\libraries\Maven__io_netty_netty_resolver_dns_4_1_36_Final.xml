<component name="libraryTable">
  <library name="Maven: io.netty:netty-resolver-dns:4.1.36.Final">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/netty/netty-resolver-dns/4.1.36.Final/netty-resolver-dns-4.1.36.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/netty/netty-resolver-dns/4.1.36.Final/netty-resolver-dns-4.1.36.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/netty/netty-resolver-dns/4.1.36.Final/netty-resolver-dns-4.1.36.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>