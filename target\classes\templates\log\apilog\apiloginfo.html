<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>操作日志</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>
  <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
  <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
  <style>
  	.label-width{
  		width:100px;
  	}
  	.input-width{
  		width:75% !important;
  	}
  </style>
</head>
<body>
  
	<div id="" class="layui-layer-content" style="overflow: visible;">
			<fieldset class="layui-elem-field layui-field-title"
					style="margin-top: 20px;">
					<legend>api日志详情</legend>
			</fieldset>
	        
	   		<div class="layui-form-item">
	            <label class="layui-form-label label-width">id</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.id}" disabled="disabled">
	            </div>
	        </div> 
	        <div class="layui-form-item">
	            <label class="layui-form-label label-width"> 功能模块</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.module}" disabled="disabled">
	            </div>
	        </div>
		<div class="layui-form-item">
			<label class="layui-form-label label-width"> 操作类型</label>
			<div class="layui-input-inline input-width">
				<input name="authName" type="text" class="layui-input" maxlength="50"
					   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.type}" disabled="disabled">
			</div>
		</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 操作描述</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.description}" disabled="disabled">
		</div>
	</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 请求参数</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.requParam}" disabled="disabled">
		</div>
	</div>
		<div class="layui-form-item">
			<label class="layui-form-label label-width">返回参数</label>
			<div class="layui-input-inline input-width">
				<textarea name="copyrightInfo" id="copyrightInfo" th:text="${apiLogInfo?.respParam}" class="layui-textarea" style="width:510px; height:20px" disabled="disabled"></textarea>
			</div>
		</div>
		 <div class="layui-form-item">
		<label class="layui-form-label label-width"> 操作员id</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.userId}" disabled="disabled">
		</div>
	</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 操作员名称</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.userName}" disabled="disabled">
		</div>
	</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 操作方法</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.method}" disabled="disabled">
		</div>
	</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 请求uri</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.uri}" disabled="disabled">
		</div>
	</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 请求ip</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.ip}" disabled="disabled">
		</div>
	</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 操作时间</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.createTime}" disabled="disabled">
		</div>
	</div>   <div class="layui-form-item">
		<label class="layui-form-label label-width"> 操作版本号</label>
		<div class="layui-input-inline input-width">
			<input name="authName" type="text" class="layui-input" maxlength="50"
				   lay-vertype="tips" lay-verify="required" required="" th:value="${apiLogInfo?.ver}" disabled="disabled">
		</div>
	</div>

</body>
</html>