<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>参数项编辑</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
	<script type="text/javascript" th:src="@{/scripts/dic/param/dicParamEdit.js}"></script>

</head>
<body>  
  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body">
		  <form class="layui-form" lay-filter="example">
			  <input type="hidden" th:value="${dicParames?.id}" name="id">
			  <div class="layui-form-item">
				  <div class="layui-inline" style="margin-bottom: 30px;">
					  <label class="layui-form-label"><span style="color:red">*</span>参数键</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <input class="layui-input" th:value="${dicParames?.parmsKey}" name="parmsKey"  id="parmsKey" type="text" lay-verify="required|parmsKey" autocomplete="off">
						  <input type="hidden" th:value="${dicParames?.parmsKey}" name="oldParmsKey">
					  </div>
				  </div>
				  <div class="layui-inline" style="margin-bottom: 30px;">
					  <label class="layui-form-label"><span style="color:red">*</span>参数名称</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <input class="layui-input" th:value="${dicParames?.parmsName}" name="parmsName"  id="mc" type="text" lay-verify="required" autocomplete="off" placeholder="请输入名称">
					  </div>
				  </div>
				  <div class="layui-inline" style="margin-bottom: 30px;">
					  <label class="layui-form-label"><span style="color:red">*</span>中文说明</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <textarea id="description" th:text="${dicParames?.description}" name="description" placeholder="请输入内容" lay-verify="required" class="layui-textarea" ></textarea>
					  </div>
				  </div>
				  <!-- <div class="layui-inline" style="margin-bottom: 30px;">
					  <label class="layui-form-label"><span style="color:red">*</span>业务类型</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <select xm-select="ywlx_select" xm-select-show-count="1"   xm-select-radio=""
								  lay-vertype="tips" lay-verify="required" disabled>
						  </select>
						  <input type="hidden" name="businessId" id="businessId" th:value="${dicParames?.businessId}"/>
                      <input type="hidden" value="" name="businessName" id="businessName"/>
					  </div>
				  </div> -->
				  <div class="layui-inline" style="margin-bottom: 30px;">
					  <label class="layui-form-label"><span style="color:red">*</span>参数类别</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <select id="parmsType" name="parmsType" lay-filter="parmsType" lay-verify="required">
							  <option value="">请选择业务类别</option>
							  <option value="1" th:selected="${dicParames.parmsType==1}">系统类别</option>
                              <option value="2" th:selected="${dicParames.parmsType==2}">业务类别</option>
                              <option value="3" th:selected="${dicParames.parmsType==3}">硬件类别</option>
						  </select>
					  </div>
				  </div>

				  <div class="layui-inline" style="margin-bottom: 30px;">
					  <label class="layui-form-label"><span style="color:red">*</span>控件类型</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <select id="controlType" name="controlType" lay-filter="controlType" lay-verify="required">
							  <option value="">请选择控件类型</option>
							  <option value="1" th:selected="${dicParames.controlType==1}">下拉框</option>
							  <option value="2" th:selected="${dicParames.controlType==2}">数值</option>
							  <option value="3" th:selected="${dicParames.controlType==3}">勾选框</option>
							  <option value="4" th:selected="${dicParames.controlType==4}">文本框</option>
						  </select>
					  </div>
				  </div>
			  </div>
			  <div class="layui-form-item">
				  <div class="layui-inline" id="xztj-input" style="margin-bottom: 30px;display: none;">
					  <label class="layui-form-label"><span style="color:red">*</span>限制条件</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <textarea id="dataSource" name="dataSource" th:text="${dicParames?.dataSource}" placeholder="请输入内容" class="layui-textarea" disabled></textarea>
						  <button type="button" class="layui-btn layui-btn-normal layui-btn-xs" id="LAY-component-form-setval">选择</button>
					  </div>
				  </div>
				  <div class="layui-inline" id="sjy-select" style="margin-bottom: 30px;display: none;">
					  <label class="layui-form-label"><span style="color:red">*</span>数据源</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <select lay-filter="sjy_select" xm-select="sjy_select" xm-select-show-count="1"
                                  xm-select-radio="" xm-select-search lay-vertype="tips">
						  </select>
						  <input type="hidden" name="dicName" id="dicName">
					  </div>
				  </div>
				  <div class="layui-inline" id="mrz-input" style="margin-bottom: 30px;display: none;">
					  <label class="layui-form-label" style="padding-left: 25px"><span style="color:red">*</span>默认值</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <input class="layui-input" name="defaultValueNumber"  th:value="${dicParames?.defaultValue}" id="defaultValueNumber" lay-verify="type|length" type="text" autocomplete="off" placeholder="请输入默认值">
					  </div>
				  </div>
				  <div class="layui-inline" id="mrz-check" style="margin-bottom: 30px;display: none;">
					  <label class="layui-form-label" style="padding-left: 25px"><span style="color:red">*</span>默认值</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <input type="checkbox" name="isStart" th:attr="checked=${dicParames?.defaultValue == '1' ? true : false}" id="isStart" lay-skin="switch" lay-text="选中|不选中" >
						  <input name="defaultValueCheck"  id="defaultValueCheck" type="hidden" th:value="${dicParames?.defaultValue}">
					  </div>
				  </div>
				  <div class="layui-inline" id="mrz-select" style="margin-bottom: 30px;display: none;">
					  <label class="layui-form-label" style="padding-left: 25px"><span style="color:red">*</span>默认值</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <select lay-filter="mrz_select" xm-select="mrz_select" id="id_select" xm-select-show-count="1"   xm-select-radio=""
								  xm-select-search="" >
						  </select>
						  <input type="hidden" name="defaultValueSelect" id="defaultValueSelect" th:value="${dicParames?.defaultValue}">
					  		<input type="hidden" name="defaultKeySelect" id="defaultKeySelect">
					  </div>
				  </div>
			  </div>
			  <button style="display:none" lay-submit lay-filter="subBtn" id="subBtn"></button>
		  </form>
      </div>
    </div>
  </div>
	  <script th:inline="javascript">
	  	var ctx = [[${basePath}]];
		var top_value = [[${top_value}]];
		editparam(ctx,top_value);
	  </script>
</body>
</html>