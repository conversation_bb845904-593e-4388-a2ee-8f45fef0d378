<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>权限管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>
</head>
<body>
  
  <div class="layui-fluid">
        <div class="layui-card">
          <div class="layui-card-body">
            <table class="layui-hide" id="action-table" lay-filter="action-table"></table>
           	<input name="id" id="id" type="hidden" th:value="${id}">
          </div>
        </div>
  </div>
  
  <script>
   
  layui.use('table' , function(){
    var admin = layui.admin
    ,table = layui.table;
  
    table.render({
      elem: '#action-table'
      ,url: ctx + 'authController/actionList'
      ,title: '动作列表'
   	  ,where:{id: $("#id").val()}
      ,cols: [
    	  [
	        {field:'display', title:'说明',align:'center'}
	        ,{field:'controName', title:'控制器',align:'center'}
	        ,{field:'actionName', title:'动作',align:'center'}
	      ]
   	  	]
   	  ,request: {
   	    pageName: 'pageNum' //页码的参数名称，默认：page
        ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
      }
   	  ,parseData: function(res){ //res 即为原始返回的数据
    	return {
 	        "code": res.code, //解析接口状态
 	        "msg": res.msg, //解析提示文本
 	        "count": res.data.size, //解析数据长度
 	        "data": res.data //解析数据列表
   	  	}	
      }
      ,page: false
    });    
  
  });
  </script>

</body></html>