/**
 * 检查页面中所有含有query-options的input元素，获取对应的id，和对应的query-options，组成最终需要传给后台的查询实体类json字符串
 * @param queryinfo:搜索总体的json
 * @param searchInputName:搜索框input的id
 * @returns {*}
 */
function allQueryinfo() {
    var queryinfo = "{\"whereInfos\":[]}";
    var jsonResult = $.parseJSON(queryinfo);
    //检测所有含有query-options的dom元素
    var dom = $("[query-options]")
    var idarray = [];
    for (var i = 0; i < dom.length; i++) {
        idarray[i] = dom[i].id;
    }
    // console.log(idarray);
    for (var i = 0; i < idarray.length; i++) {
        var searchInputNameStr = idarray[i];
        searchInputNameStr = "#" + searchInputNameStr;
        var searchInputName = $(searchInputNameStr).val();
        if (searchInputName != "") {
            var searchInputNameOptions = $(searchInputNameStr).attr("query-options");
            var jsonO = $.parseJSON(searchInputNameOptions);
            jsonO.whereType = "where";
            jsonO.andOr = 'and';
            jsonO.queryValue = searchInputName;
            jsonResult.whereInfos.push(jsonO);
        }
    }
    return JSON.stringify(jsonResult)
}

/**
 * 检查页面中所有含有query-options的input元素，获取对应的id，和对应的query-options，组成最终需要传给后台的查询实体类json字符串
 * @param queryinfo:搜索总体的json
 * @param searchInputName:搜索框input的id
 * @returns {*}
 */
function allQueryinfo2() {
    var queryinfo = "{\"whereInfos\":[]}";
    var jsonResult = $.parseJSON(queryinfo);
    //检测所有含有query-options的dom元素
    var dom = $("[query-options]")
    var idarray = [];
    for (var i = 0; i < dom.length; i++) {
        idarray[i] = dom[i].id;
    }
    // console.log(idarray);
    for (var i = 0; i < idarray.length; i++) {
        var searchInputNameStr = idarray[i];
        searchInputNameStr = "#" + searchInputNameStr;
        var searchInputName = $(searchInputNameStr).val();
        if (searchInputName != "") {
            var searchInputNameOptions = $(searchInputNameStr).attr("query-options");//单引号改为双引号
            searchInputNameOptions = changeCode(searchInputNameOptions);
            var jsonO = $.parseJSON(searchInputNameOptions);
            jsonO.whereType = "where";
            jsonO.andOr = 'and';
            jsonO.queryValue = searchInputName;
            jsonResult.whereInfos.push(jsonO);
        }
    }
    return JSON.stringify(jsonResult)
}
/**
 * 双引号转换
 */
function changeCode(str){
    //首先将单引号全部置换为双引号，再将末尾和头部切换
    if(str != null && str != ""){
        str = str.replace(/'/g, '"');
        return  str;
    }
}
/**
 * 重置所有的搜索条件
 */
function unsetAllQueryinfo() {
    var dom = $("[query-options]")
    for (var i = 0; i < dom.length; i++) {
        $("#" + dom[i].id).val("");
    }
}

//点击行内选中该行，不需要只点击checkbox
$(document).on("click", ".layui-table-body table.layui-table tbody tr", function () {
    var obj = event ? event.target : event.srcElement;
    var tag = obj.tagName;
    var checkbox = $(this).find("td div.laytable-cell-checkbox div.layui-form-checkbox I");
    if (checkbox.length != 0) {
        if (tag == 'DIV') {
            checkbox.click();
        }
    }
});
$(document).on("click", "td div.laytable-cell-checkbox div.layui-form-checkbox", function (e) {
    e.stopPropagation();
});


function compareDate(begintimeStr, endtimeStr) {
    if (begintimeStr == "") {
        return true;
    }
    if (endtimeStr == "") {
        return true;
    }
    var starttime = new Date(Date.parse(begintimeStr));
    var endtime = new Date(Date.parse(endtimeStr));
    //进行比较
    if (endtime < starttime) {
        return false;
    } else {
        return true;
    }
    // return (endtime>=starttime && endtime<=endtime);
}