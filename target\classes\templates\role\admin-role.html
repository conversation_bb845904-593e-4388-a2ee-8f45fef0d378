<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>角色管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <blockquote class="layui-elem-quote quoteBox" id="search">
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto"
                                   style="width: unset;padding: 9px 15px 9px 0px;">角色名称：</label>
                            <div class="layui-input-inline mr0">
                                <input class="layui-input" id="searchValue" autocomplete="off" placeholder="请输入角色名称">
                            </div>
                        </div>
                        <div class="layui-inline" >
                            <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                            <button id="searchBtn" lay-event="search_btn" class="layui-btn">
                                <i class="layui-icon">&#xe615;</i>查询
                            </button>
                            <button type="reset" class="layui-btn" id="unsetBtn">
                                <i class="layui-icon">&#xe669;</i>重置
                            </button>
                        </div>
                    </blockquote>
                    <table class="layui-hide" id="role_table" lay-filter="role_table"></table>

                    <script type="text/html" id="topToolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i
                                    class="layui-icon">&#xe608;</i>增加
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i
                                    class="layui-icon">&#xe642;</i>编辑
                            </button>
                            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i
                                    class="layui-icon">&#xe640;</i>删除
                            </button>
                        </div>
                    </script>

                    <script type="text/html" id="activeToolbar">
                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    //回车搜索
    $("blockquote").on('keyup','input,select',function (e) {
        if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.use('table', function () {
        var admin = layui.admin
            , table = layui.table;

        table.render({
            elem: '#role_table'
            , url: ctx + 'roleController/roleJson'
            , toolbar: '#topToolbar'
            ,defaultToolbar: ['filter']
            , title: '角色数据表'
            , cols: [
                [
                    {type: 'checkbox'}
                    , {field: 'id', title: 'ID', hide: true, sort: true,align:'center'}
                    , {field: 'roleName', title: '角色名称',align:'center'}
                    , {fixed: 'right', title: '操作', toolbar: '#activeToolbar', align: 'center'}
                ]
            ]
            , request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true
        });
        //头工具栏事件
        table.on('toolbar(role_table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id),
                width = '600', height = '550', submitButId = '#submitBut';
            switch (obj.event) {
                case 'add_btn':
                    var url = ctx + 'roleController/addRole',
                        title = '新增角色';
                    xadmin.openWindow(url, title, width, height, submitButId);
                    break;
                case 'edit_btn':
                    var data = checkStatus.data;
                    if (data.length == 1) {
                        var url = ctx + 'roleController/editRole?id=' + data[0].id,
                            title = '编辑角色';
                        xadmin.openWindow(url, title, width, height, submitButId);
                    } else {
                        layer.msg("请选择一条数据", {
                            icon: 2
                        })
                    }
                    break;
                case 'delete_btn':
                    var data = JSON.stringify(checkStatus.data);
                    var checked = checkStatus.data;
                    if (checked.length < 1) {
                        layer.msg('请至少选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    var ids = '';
                    for (var i = 0; i < checked.length; i++) {
                        if(i != checked.length-1){
                            ids += checked[i].id + ','
                        }else {
                            ids += checked[i].id
                        }
                    }
                    layer.confirm('是否删除', {btn: ['确定', '取消'], title: '提示'}, function () {
                        var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                        $.ajax({
                            method: "POST",
                            url: ctx + 'roleController/batchDeleteRole',
                            data: {'ids': ids},
                            async: true,
                            cache: false,
                            success: function (res) {
                                layer.close(loading);
                                if (res.code == 0) {
                                    //layui.table.reload("csmb-table");
                                    layer.msg(res.msg, {icon: 1});
                                    table.reload("role_table");

                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            erro: function (res) {
                                layer.close(loading);
                                layer.msg('操作失败', {icon: 2});
                            }
                        })
                    })
                    break;
            }
            ;
        });

        //监听行工具事件
        table.on('tool(role_table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                // 删除函数
                var url = ctx + 'roleController/deleteRole?id=' + data.id;
                layer.confirm('是否删除',{icon:3,btn:['确定','取消'],title:'提示'},function(){
                    var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
                    $.ajax({
                        url:url,
                        success:function(res){
                            layer.close(loading);
                            if(res.code == 0){
                                layer.msg(res.msg,{icon: 1});
                                table.reload("role_table");
                            }else{
                                layer.msg(res.msg,{icon: 2});
                            }
                        },
                        erro:function(res){
                            layer.close(loading);
                            layer.msg('操作失败',{icon: 2});
                        }
                    })
                });
                //xadmin.deleteDemo(url, table, tableName);
            } else if (obj.event === 'edit') {
                var url = ctx + 'roleController/editRole?id=' + data.id,
                    title = '编辑角色', width = '600', height = '550', submitButId = '#submitBut';
                xadmin.openWindow(url, title, width, height, submitButId);
            }
        });
        //搜索及重置按钮
        $("#searchBtn").click(function () {
            var searchValue = $("#searchValue");
            table.reload('role_table', {
                where: { //设定异步数据接口的额外参数
                    roleName: searchValue.val()
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
            return false;
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#searchValue").val("");
            location.reload();
            table.reload('role_table', {
                where: null
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            }); //只重载数据
            return false;
        })

    });

</script>

</body>
</html>