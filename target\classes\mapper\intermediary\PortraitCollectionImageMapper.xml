<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.intermediary.dao.IPortraitCollectionImageDao">
    <resultMap id="portraitImage" type="com.fwy.intermediary.entity.PortraitCollectionImage">
        <result column="code" property="code"/>
        <result column="oss_url" property="ossUrl"/>
        <result column="count" property="count"/>
        <result column="type" property="type"/>
        <result column="state_id" property="stateId"/>
        <result column="picid" property="picId"/>
        <result column="create_time" property="createTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>

        <!--    标记时间（新增字段）-->
        <result column="mark_time" property="markTime"
                jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="deleteRecord">
        update from portrait_collection_record
        set state_id = 3
        where state_id != 3  and  person_code = #{personCode};
    </update>

    <select id="findRecordCountByPage" parameterType="PortraitImageCondition" resultMap="portraitImage">
        select person_code code, count(*) count, i.oss_url oss_url, i.type type,  MAX(r.create_time) update_time from
        portrait_collection_record r right join portrait_collection_image i on r.person_code = i.code
        where r.state_id != 3 and i.state_id != 3
        <if test="type != null">
            AND i.type = #{type}
        </if>
        <if test="startDate != null">
            AND r.create_time >= #{startDate}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="endDate != null">
            AND r.create_time &lt;= #{endDate}
        </if>
        GROUP BY person_code
        HAVING 1=1
        <if test="minCount != null">
            AND count >= #{minCount}
        </if>
        <if test="maxCount != null">
            AND count &lt;= #{maxCount}
        </if>

        order by count desc
    </select>

    <select id="findByCondition" parameterType="PortraitImageCondition" resultMap="portraitImage">
        select person_code code, count(*) count, i.oss_url oss_url, i.type type,  MIN(r.create_time) create_time from
        portrait_collection_record r right join portrait_collection_image i on r.person_code = i.code
        where  i.state_id != 3

        <if test="type != null">
            AND i.type = #{type}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY person_code
        HAVING 1=1
        <if test="startDate != null">
            AND MIN(r.create_time) >= #{startDate}
        </if>
        <if test="endDate != null">
            AND MIN(r.create_time) &lt;= #{endDate}
        </if>

        order by count desc
    </select>

    <update id="updateRecordImg">
        update portrait_collection_record
        set person_url = #{ossUrl}
        where person_code = #{code};
    </update>

    <update id="changeMarkTime">
        update portrait_collection_image
        set mark_time = #{markTime}
        where code = #{code}
    </update>

    <select id="selectOneByPersonCode" resultMap="portraitImage">
        select * from portrait_collection_image
        where state_id != 3
        and code = #{personCode}
    </select>
    <select id="selectOneByPicId" resultMap="portraitImage">
        select * from portrait_collection_image
        where state_id != 3
        and picid = #{selectPic}
    </select>

</mapper>