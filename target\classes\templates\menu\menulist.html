<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="UTF-8">
<title>Insert title here</title>
<div th:replace="Importfile::html"></div>
<script th:src="@{/scripts/security/main/openWay.js}"></script>
</head>
    <style>
        /*固定input宽度*/
        .layui-input, .layui-textarea {
            display: block;
            width: 180px;
            padding-left: 10px;
        }
    </style>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
              <blockquote class="layui-elem-quote quoteBox" id="search">
          	   <div class="layui-inline">
	           	  	<label class="layui-form-label w-auto" style="width: unset;padding: 9px 5px 9px 5px;">菜单：</label>
	              	<div class="layui-input-inline mr0">
		            	<input class="layui-input" id="searchValue" autocomplete="off" placeholder="请输入关键字">
	              	</div>
	            </div>
	            <div class="layui-inline" >
                    <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
	            	<button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</button>
	            	<button class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
	            </div>
               </blockquote>
            <table class="layui-hide" id="tree-table" lay-filter="tree-table"></table>
           
            <script type="text/html" id="top_toolbar">
              <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i class="layui-icon">&#xe608;</i>增加</button>
				<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i class="layui-icon">&#xe642;</i>编辑</button>
				<button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i class="layui-icon">&#xe640;</i>删除</button>
              </div>
            </script>            
          </div>
        </div>
      </div>
    </div>
  </div>
	
</body>
<script type="text/javascript">
    //回车搜索
    $("blockquote").on('keyup','input,select',function (e) {
        if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#search_btn").trigger("click");//触发搜索按钮的点击事件
        }
    });
function updataMenu(id){
	//alert(id);
	var url= ctx + 'menuController/editMenu?id='+id,
	title = '编辑菜单';
	top.layui.index.openTabsPage(url, title);
}
 	layui.config({
        base:ctx + 'plugins/'
    }).extend({
        treetable: 'treetable-lay/treetable'
    }).use(['table', 'treetable'], function () {
        var $ = layui.jquery;
        var table = layui.table;
        var treetable = layui.treetable;
        var form = layui.form;
        // 渲染表格
         renderTable = treetable.render({
                treeColIndex: 2,
                treeSpid: "0",
                treeIdName: 'id',
                treePidName: 'parentId',
                toolbar: '#top_toolbar',
                defaultToolbar: ['filter'],
                treeLinkage: false,
                elem: '#tree-table',
                defaultToolbar:[],
                url: ctx + 'menuController/menuJson',
                cols: [
                	[{type: 'checkbox'},
                		{field: 'id', title: 'ID',hide:true},
	                    {field: 'menuName', title: '菜单名',width:200},
	                    {field: 'orderCode', title: '分类',width:180},
	                    {field: 'menuUrl', title: '菜单路径',width:300},
	                    {field: 'isShow',title:'状态',width:100,
	                    	templet:function(data){
	                    	    //return html = data.isShow ? '<span class="fClr3">显示</span>':'<span class="fClr3">隐藏</span>'
		                    	var html = '';
		                    	if(data.isShow == 1){
		                    		html += '<input type="checkbox" value='+data.id+'  checked=true name="isShow" id="isShow" lay-skin="switch" lay-text="显示|隐藏">'
		                    	}else{
		                    		html += '<input type="checkbox" value='+data.id+' name="isShow" id="isShow" lay-skin="switch" lay-text="显示|隐藏">'
		                    	}
	                    	    		return	html;
	                    	}
	                    },{title:'操作', align: 'center',fixed: 'right',width: '250',
	                    	templet:function(data){
	                    	   var html = '';
	                    	   html += '<a class="layui-btn layui-btn-normal layui-btn-xs" onclick=updataMenu('+data.id+') lay-event="edit">编辑</a>' +
	                    	           '<a class="layui-btn layui-btn-danger layui-btn-xs" onclick=deleteMenu('+data.id+') lay-event="del">删除</a>'+
	                    	           '<a class="layui-btn layui-btn-normal layui-btn-xs" onclick="moveinfo('+data.id+',1,\''+data.orderCode+'\')">上移</a>' +
	                    	           '<a class="layui-btn layui-btn-warm layui-btn-xs" onclick="moveinfo('+data.id+',2,\''+data.orderCode+'\')">下移</a>';
	                    	           return html;
	                    }}
                     ]
                	],
                done: function () {
                    layer.closeAll('loading');
                }
            });
         form.on('switch()', function(data){
             var id = data.value;
        	 var isShow =this.checked ? '1' : '0';
        	$.ajax({
        			url:ctx + 'menuController/showMenu?id='+id+'&isshow='+isShow,
        		    success:function(res){
        		    	 if(res.code == 0){
        						layer.msg(res.stateMsg,{icon: 2});
        					}else{
        						layer.msg(res.stateMsg,{icon: 1});
        					}
        		    },
        		    error:function(data){
        		    	layer.msg('操作失败',{icon: 1});
        		    }
        		});
        });
         //监听表格事件
         table.on('toolbar(tree-table)',function(obj){
        	   var checkStatus = table.checkStatus(obj.config.id);
        	   var butName = obj.event;
        	   var data = checkStatus.data;
        	 if(butName == 'add_btn'){
        		 var url = ctx + 'menuController/addMenu',
          	    	title = '新增菜单';
	         	top.layui.index.openTabsPage(url, title);
        	 }else if(butName == 'edit_btn'){
        		 if(data.length == 1){
        			 var url= ctx + 'menuController/editMenu?id='+data[0].id,
        				title = '编辑菜单';
        			 top.layui.index.openTabsPage(url, title);
                 }else{
               		layer.msg("只能选择一条数据", {
              		  icon: 2});
                 }
        	 }else if(butName == 'delete_btn'){
        		 if(data.length == 1){
        			 deleteMenu(data[0].id);
        		 }else{
        			 layer.msg("只能选择一条数据", {
               		  icon: 2});
        		 }
        	 }
         });
      	$("#search_btn").click(function(){
    		 //treeTable 的搜索功能
           		    var keyword = $('#searchValue').val();
                       var searchCount = 0;
                       $('#tree-table').next('.treeTable').find('.layui-table-body tbody tr td').each(function () {
                           $(this).css('background-color', 'transparent');
                           var text = $(this).text();
                           if (keyword != '' && text.indexOf(keyword) >= 0) {
                               $(this).css('background-color', 'rgba(250,230,160,0.5)');
                               if (searchCount == 0) {
                                   treetable.expandAll('#tree-table');
                                   $('html,body').stop(true);
                                   $('html,body').animate({scrollTop: $(this).offset().top - 150}, 500);
                               }
                               searchCount++;
                           }
                       });
                       if (keyword == '') {
                           layer.msg("请输入搜索内容", {icon: 5});
                       } else if (searchCount == 0) {
                           layer.msg("没有匹配结果", {icon: 5});
                       }
    	});
      	$("#unset_Btn").click(function(){
      		location.reload();
      	})
    });

 	
    function deleteMenu(id){
       var url = ctx + 'menuController/deleteMenu?id=' + id;
       layer.confirm('您确定要删除？', {icon:3,btn:['确定','取消'],title:'提示'}, function () {
			var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
			$.ajax({
				url:url,
				success:function(res){
					layer.close(loading);
					 if(res.stateType == 0){
							layer.msg(res.stateMsg,{icon: 1});
							location.reload()
						}else{
							layer.alert(res.stateMsg,{icon: 2});
						}
				},
				erro:function(res){
					layer.close(loading);
					layer.alert('操作失败',{icon: 2});
				}
			})
		})
 }
    function moveinfo(id,type,orderCode){
    	var orderCode = orderCode + '';
    	var loading = layer.msg('正在移动', {icon: 16, shade: 0.3, time:0});
    	$.ajax({
			url : ctx + 'menuController/moveMenu',
			type : "POST",
			data : {'id':id,'movetype':type,'orderCode':orderCode},
			async : true,
			cache : false,
			success : function(res){
				layer.close(loading);
				 if(res.stateType == 0){
						layer.msg(res.stateMsg,{icon: 1});
						location.reload()
					}else{
						layer.msg(res.stateMsg,{icon: 1});
					}
			},
			erro : function(res){
				layer.close(loading);
				layer.msg('操作失败',{icon: 2});
			}
		})
    }
</script>
</html>