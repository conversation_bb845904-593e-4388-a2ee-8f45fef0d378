<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>字典管理</title>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/common/openWay.js}"></script>
    <!--<script th:replace="loading::loading"></script>-->
    <style>
        .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-inline {
            width: 200px;
        }
    </style>
    <style>
        #search2 {
            margin-bottom: 5px;
        }
    </style>
</head>
<body onload="load()">
<!--<div th:replace="loading::demo"></div>-->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <blockquote class="layui-elem-quote quoteBox" id="search2">
                        <div class="layui-form-item layui-row">
                            <div class="layui-col-md4 layui-col-sm4 layui-col-xs4 layui-col-lg4 layui-form">
                                <label class="layui-form-label">数据值：</label>
                                <div class="layui-input-inline ">
                                    <input class="layui-input" id="dicValue" autocomplete="off" placeholder="请输入数据值">
                                    <input id="dicCode" type="hidden" th:value="${dicCode}">
                                    <input id="subAjax" type="hidden">
                                </div>
                            </div>
                            <div class="layui-col-md4 layui-col-sm4 layui-col-xs4 layui-col-lg4">
                                <!--<label class="layui-form-label "></label>-->
                                <button class="layui-btn icon-btn" id="search_btn2"><i class="layui-icon">&#xe615;</i>查询
                                </button>
                                <button class="layui-btn" id="unset_Btn2"><i class="layui-icon">&#xe669;</i>重置</button>
                            </div>
                        </div>
                    </blockquote>
                    <table class="layui-hide" id="dataDetail" lay-filter="dataDetail"></table>

                    <script type="text/html" id="topToolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i
                                    class="layui-icon">&#xe608;</i>增加
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i
                                    class="layui-icon">&#xe642;</i>编辑
                            </button>
                            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i
                                    class="layui-icon">&#xe640;</i>删除
                            </button>
                        </div>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    //回车搜索
    $("blockquote").on('keyup', 'input,select', function (e) {
        if (e.keyCode == 13) {//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#search_btn2").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.use('table', function () {
        var admin = layui.admin
            , table = layui.table
            , form = layui.form;
        var dicCode = $("#dicCode").val();

        form.render();

        table.render({
            elem: '#dataDetail'
            , url: ctx + 'dataDetailController/dataDetailJson?dicCode=' + dicCode
            , toolbar: '#topToolbar'
            , title: '字典详情数据表'
            , defaultToolbar: []
            , cols: [
                [
                    {type: 'checkbox', width: '5.1%'}
                    , {field: 'id', title: 'ID', hide: true}
                    , {field: 'dicType', hide: true, title: '数据类型'}
                    , {field: 'dicValue', title: '数据值 ', width: '25%'}
                    , {field: 'disCription', title: '数据描述 ', width: '30%'}
                    , {field: 'createTime', title: '创建时间', width: '10%', align: 'center'}
                    , {field: 'updateTime', title: '修改时间', width: '10%', align: 'center'}
                    , {
                    field: 'isStart', width: '10%', align: 'center', title: '是否启用', templet: function (data) {
                        var html = '';
                        if (data.isStart) {
                            html += '<input type="checkbox" value=' + data.id + '  checked=true name="isStart" id="isStart" lay-skin="switch" lay-filter="isStart" lay-text="启用|停用">'
                        } else {
                            html += '<input type="checkbox" value=' + data.id + ' name="isStart" id="isStart" lay-skin="switch" lay-filter="isStart" lay-text="启用|停用">'
                        }
                        return html;
                    }
                }
                    , {
                    title: '操作', align: 'center', width: '10%', fixed: 'right', templet: function (data) {
                        var html = '';
                        html += '<a class="layui-btn layui-btn-normal layui-btn-xs" onclick="moveInfo(' + data.id + ',1,\'' + data.sortNum + '\')">上移</a>' +
                            '<a class="layui-btn layui-btn-warm layui-btn-xs" onclick="moveInfo(' + data.id + ',2,\'' + data.sortNum + '\')">下移</a>';
                        return html;
                    }
                }
                ]
            ]
            , request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , parseData: function (res) { //res 即为原始返回的数据
                console.log(res);
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true
        });
        var active = {
            reload: function () {
                var dicValue = $("#dicValue");
                table.reload('dataDetail', {
                    page: {
                        curr: 1
                    },
                    where: {dicValue: dicValue.val()}
                })
            }
        };
        //重置
        $("#unset_Btn2").click(function () {
            $("#search2 :input").val("");
            table.reload('dataDetail', {
                where: null
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
        $(document).keydown(function (event) {
            if (event.keyCode == 13) {
                var type = 'reload';
                active[type] ? active[type].call(this) : '';
            }
        });
        //头工具栏事件
        table.on('toolbar(dataDetail)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            var data = checkStatus.data;
            var width = '600px', height = '450px', submitButId = '#submitBut';
            switch (obj.event) {
                case 'add_btn':
                    var url = ctx + 'dataDetailController/addDataDetail?dicCode=' + dicCode,
                        title = '新增字典数据';
                    openWindow(url, title, width, height, submitButId);
                    break;
                case 'edit_btn':
                    if (data.length == 1) {
                        var url = ctx + 'dataDetailController/editDataDetail?id=' + data[0].id,
                            title = '编辑字典详情';
                        openWindow(url, title, width, height, submitButId);
                    } else {
                        layer.msg("请选择一条数据")
                    }
                    break;
                case 'delete_btn':
                    if (data.length == 1) {
                        deleteUser(data[0].id);
                    } else {
                        layer.msg("请选择一条数据");
                    }
                    break;
            }
            ;
        });

        var ids = new Array();
        //当前表格中的全部数据:在表格的checkbox全选的时候没有得到数据, 因此用全局存放变量
        var table_data = new Array();
        //复选框选中监听,将选中的id 设置到缓存数组,或者删除缓存数组
        table.on('checkbox(dataDetail)', function (obj) {
            if (obj.checked == true) {
                if (obj.type == 'one') {
                    ids.push(obj.data.id);
                } else {
                    for (var i = 0; i < table_data.length; i++) {
                        ids.push(table_data[i].id);
                    }
                }
            } else {
                if (obj.type == 'one') {
                    for (var i = 0; i < ids.length; i++) {
                        if (ids[i] == obj.data.id) {
                            ids.remove(i);
                        }
                    }
                } else {
                    for (var i = 0; i < ids.length; i++) {
                        for (var j = 0; j < table_data.length; j++) {
                            if (ids[i] == table_data[j].id) {
                                ids.remove(i);
                            }
                        }
                    }
                }
            }
        });
        var map2 = {};
        var set = new Set();
        table.on('edit(dataDetail)', function (obj) {
            var value = obj.value //得到修改后的值
                , data = obj.data //得到所在行所有键值
                , field = obj.field; //得到字段
            map2[data.id] = data;
            set.add(data.id);
            console.log(set);
        });
        form.on('switch(isStart)', function (data) {
            var id = data.value;
            var isStart = this.checked ? '1' : '0';
            var url = ctx + 'dataDetailController/changeStart?id=' + id + '&isStart=' + isStart;
            $.ajax({
                url: url,
                type: "POST",
                data: {"id": id, "isStart": isStart},
                success: function (result) {
                    if (res.code == 0) {
                        layer.closeAll('loading');
                        layer.msg("更新成功!", {icon: 6, skin: 'layer-ext-moon'});
                    } else {
                        layer.closeAll('loading');
                        layer.msg("更新失败!", {icon: 5, skin: 'layer-ext-moon'});
                    }
                },
                error: function (data) {
                    layer.closeAll('loading');
                    layer.msg("更新失败!", {icon: 5, skin: 'layer-ext-moon'});
                }
            });
        });
        $("#subAjax").click(function () {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);

        });
        //查询
        $("#search_btn2").click(function () {
            console.log(11);
            var type = 'reload';
            active[type] ? active[type].call(this) : '';
        })
    });

    function deleteUser(id) {
        var url = ctx + 'dataDetailController/deleteDataDetail?id=' + id;
        layer.confirm('您确定要删除？', {icon: 3, btn: ['确定', '取消'], title: '提示'}, function () {
            var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
            $.ajax({
                url: url,
                type: "POST",
                async: false,
                success: function (res) {
                    layer.close(loading);
                    if (res.code == 0) {
                        layer.closeAll('loading');
                        layer.msg(res.msg, {icon: 1});
                        location.reload();
                    } else {
                        layer.closeAll('loading');
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function (res) {
                    layer.close(loading);
                    layer.msg(res.msg, {icon: 2});
                }
            })
        })
    }

    //上移下移
    function moveInfo(id, moveType, sortNum) {
        var sortNum = sortNum;
        var loading = layer.msg('正在移动', {icon: 16, shade: 0.3, time: 0});
        $.ajax({
            url: ctx + 'dataDetailController/moveDataDetail',
            type: "POST",
            data: {'id': id, 'moveType': moveType, 'sortNum': sortNum},
            async: false,
            success: function (res) {
                layer.close(loading);
                if (res.code == 0) {
                    layer.msg(res.msg, {icon: 1});
                    location.reload();
                } else {
                    layer.msg(res.msg, {icon: 5});
                }
            },
            error: function (res) {
                layer.close(loading);
                layer.msg('操作失败', {icon: 2});
            }
        })
    };

    openWindow = function (url, title, width, heigth, submitButId) {
        layer.open({
            type: 2,
            title: title,
            area: [width, heigth],
            fixed: false, //不固定
            maxmin: true,
            content: url,
            btn: ['确定', '取消'],
            yes: function (index, layero) {
                var submit = layero.find('iframe').contents().find(submitButId);
                submit.click();
                return false;
            },
            btn2: function () {
                layer.closeAll();
            },
            zIndex: layer.zIndex, //重点1
            success: function (layero, index) {
//	             layer.setTop(layero); //重点2
//	        	 layer.iframeAuto(index);//弹窗自适应
            }
        });
    };
</script>

</body>
</html>