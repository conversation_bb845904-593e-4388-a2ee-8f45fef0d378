<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>
    <meta charset="utf-8">
    <title>警银通支付配置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <style type="text/css">
        .layui-form-label{
            width: 150px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>基本信息</legend>
                    </fieldset>
                    <form class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">是否内外网</label>
                            <div class="layui-input-block">
                                <input type="radio" name="inner" value="0" title="内网"
                                       th:checked="${application.pzjPayInterFace?.inner == 0}"> <input
                                    type="radio" name="inner" value="1" title="外网"
                                    th:checked="${application.pzjPayInterFace?.inner == 1}">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">支付方式</label>
                            <div class="layui-input-block">
                                <input type="radio" name="payType" value="1" title="警银通"
                                       th:checked="${application.pzjPayInterFace?.payType == 1}"> <input
                                    type="radio" name="payType" value="2" title="通用支付"
                                    th:checked="${application.pzjPayInterFace?.payType == 2}">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">通用支付地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="payAddress" id="payAddress"
                                       th:value="${application.pzjPayInterFace?.payAddress}" autocomplete="off"
                                       class="layui-input" style="width: 500px" lay-verify="required"
                                       lay-reqText="必须输入支付地址">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">警银通内网支付地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="payJytInAddress" id="payJytInAddress"
                                       th:value="${application.pzjPayInterFace?.payJytInAddress}" autocomplete="off"
                                       class="layui-input" style="width: 500px;" disabled="disabled">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">警银通外网支付地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="payJytOutAddress" id="payJytOutAddress"
                                       th:value="${application.pzjPayInterFace?.payJytOutAddress}" autocomplete="off"
                                       class="layui-input" style="width: 500px"  disabled="disabled">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="dosubmit">
                                    <i class="layui-icon layui-icon-ok"></i>确定
                                </button>
                            </div>
                        </div>
                    </form>
                    <script>
                        layui.use(['form','layer','jquery'] , function(){
                            var $ = layui.jquery;
                            var layer = layui.layer;
                            var form = layui.form;
                            form.render();

                            form.on("submit(dosubmit)",function(data){
                                var saveUrl = ctx + 'configController/saveSysPayInfo';
                                $.ajax({
                                    url:saveUrl,       //提交表单的地址
                                    data: data.field,      //提交表单的数据
                                    success:function(data){
                                        if (data.code == '0') {
                                            layer.alert(data.msg, {
                                                icon : 6,
                                                skin: 'layer-ext-moon' //样式类名
                                                ,closeBtn: 0
                                            }, function(){
                                                parent.location.reload();
                                            });
                                        } else {
                                            layer.msg(data.msg, {
                                                icon : 5,
                                                skin : 'layer-ext-moon'
                                            });
                                        }
                                    },
                                    error:function(){
                                    }
                                });
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

</html>