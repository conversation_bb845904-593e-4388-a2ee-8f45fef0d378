<!DOCTYPE html >
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="UTF-8">
<title>微信用户管理</title>
<link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}">
<link>
<script type="text/javascript" th:src="@{/admin/layui/layui.js}"></script>
<div th:replace="Importfile::html"></div>
<script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
<link rel="stylesheet"
	th:href="@{/plugins/formSelects/formSelects-v4.css}" />
</head>
<style>
	/*固定input宽度*/
	.layui-input, .layui-textarea {
		display: block;
		width: 180px;
		padding-left: 10px;
	}
</style>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          <blockquote class="layui-elem-quote quoteBox" id="search">
          	<form id="search_form">
				<div class="layui-inline">
					<label class="layui-form-label w-auto">微信号：</label>
					<div class="layui-input-inline mr0">
						<input class="layui-input" id="wename"  placeholder="请输入微信号" >
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label w-auto">身份证号：</label>
					<div class="layui-input-inline mr0">
						<input class="layui-input" id="idCardnumber" placeholder="请输入身份证号" >
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label label-width">微信籍贯：</label>
					<div class="layui-input-inline mr0">
						<input class="layui-input" id="wePlaceOfOrigin" placeholder="请输入籍贯">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label w-auto">电话号码：</label>
					<div class="layui-input-inline mr0">
						<input class="layui-input" id="phoneNumber" autocomplete="off" placeholder="请输入电话号码" >
					</div>
				</div>
				<div class="layui-inline layui-form">
					<label class="layui-form-label">状态：</label>
					<div class="layui-input-inline" style="width: 180px;">
						<select name="status" id="status">
							<option value="">请选择状态</option>userJson
							<option value="0">未绑定</option>
							<option value="1">绑定中</option>
							<option value="2">已解绑</option>
						</select>
					</div>
				</div>
				<div class="layui-inline" >
					<label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
	            	<a class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</a>
	            	<a class="layui-btn" id="reset_btn"><i class="layui-icon">&#xe669;</i>重置</a>
	            </div>
	        </form>
	        </blockquote>
            <table class="layui-hide" id="usertable" lay-filter="usertable"></table>
            <script type="text/html" id="topToolbar">
              <div class="layui-btn-container">
			  </div>
            </script>
          </div>
        </div>
      </div>
    </div>
  </div>

<script th:inline="javascript">
	//回车搜索
	$("blockquote").on('keyup','input,select',function (e) {
		if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
			$("#search_btn").trigger("click");//触发搜索按钮的点击事件
		}
	});

layui.use(['layer', 'element', 'table', 'form','laydate'], function () {
  var admin = layui.admin,
  table = layui.table;
  var layer = layui.layer,
  form = layui.form,
  element = layui.element;
  var $ = layui.$;
  var laydate = layui.laydate;
  laydate.render({
	  elem:'#lastLoginTime',
	  range:true
  });
  laydate.render({
	  elem:'#createTime',
	  range:true
  });
  form.render();
  table.render({
    toolbar: '#topToolbar',
	defaultToolbar: ['filter'],
	 height:'full-50'
	,limit:20,
    elem: '#usertable',
    even: false,
    title: '微信用户列表',
    url: ctx + 'wxuserController/wxuserJson',
    page: true,
      cols: [
          [{type: 'checkbox'},
              {field: 'wename', title: '微信号',align:'center'},
              {field: 'weNickname', title: '昵称',align:'center'},
              {field: 'wePlaceOfOrigin', title: '籍贯',align:'center'},
              {field: 'idCardnumber', title: '身份证号',width:180,align:'center'},
              {field: 'phoneNumber', title: '电话号码', width:120,align:'center'},
              {field: 'status',title:'状态',align:'center',width:100,
                  templet:function(data){
                      if(data.status==0){
                          return "未绑定"
                      }else if(data.status==1){
                          return "绑定中"
                      }else if(data.status==2){
                          return "已解绑"
                      }
                  }
              },{field: 'createTime', title: '创建时间',align:'center'}
              ,{title:'操作',width:100,fixed: 'right', align: 'center',
              templet:function(data){
                  var html = '';
                  html += '<a class="layui-btn layui-btn-xs layui-btn-primary" onclick=checkUser('+data.id+') lay-event="check">详情</a>'
                  return html;
              }
          }
          ]
      ],request: {
      pageName: 'pageNum', //页码的参数名称，默认：page
      limitName: 'pageSize' //每页数据量的参数名，默认：limit
    },parseData: function(res){ //res 即为原始返回的数据
	  return {
	 	"code": res.code, //解析接口状态
	 	"msg": res.msg, //解析提示文本
	 	"count": res.data.total, //解析数据长度
	 	"data": res.data.list //解析数据列表
	  }	
	}
	
  });
    var active = {
        reload:function(){
            var wename = $("#wename"),
				openId = $("#openId"),
                wePlaceOfOrigin = $("#wePlaceOfOrigin"),
                phoneNumber = $("#phoneNumber"),
                status = $("#status"),
                idCardnumber = $("#idCardnumber")
            table.reload('usertable',{
                page:{
                    curr:1
                },
                where:{
                    wename : wename.val(),idCardnumber : idCardnumber.val(),
                    openId:openId.val(),
                    wePlaceOfOrigin : wePlaceOfOrigin.val(),phoneNumber : phoneNumber.val(),
                    status:status.val()
                }
            })
        }
    }
  $("#search_btn").click(function(){
  	var type = 'reload';
      active[type] ? active[type].call(this) : '';
  })
 $("#reset_btn").click(function(){
	 window.location.reload();
  })
});
	function checkUser(id){
	    console.log(id)
    	var urlstr = ctx + 'wxuserController/wxshowUser?id='+ id;
    	var tit = "查看微信用户";
   	 	top.layui.index.openTabsPage(urlstr,tit);
	}

$(document).on("click",".layui-table-body table.layui-table tbody tr",function(){
    var obj = event ? event.target : event.srcElement;
    var tag = obj.tagName;
    var checkbox = $(this).find("td div.laytable-cell-checkbox div.layui-form-checkbox I");
    if(checkbox.length!=0){
        if(tag == 'DIV') {
            checkbox.click();
        }
    }
    
});

$(document).on("click","td div.laytable-cell-checkbox div.layui-form-checkbox",function(e){
    e.stopPropagation();
});
</script>
</body>
</html>