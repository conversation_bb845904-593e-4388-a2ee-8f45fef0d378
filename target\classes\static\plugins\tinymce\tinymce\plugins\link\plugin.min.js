/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.2 (2020-08-17)
 */
!function(i){"use strict";var n,t,e,r,o=tinymce.util.Tools.resolve("tinymce.PluginManager"),u=tinymce.util.Tools.resolve("tinymce.util.VK"),a=function(r){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===r;var n,e}},l=function(n){return function(t){return typeof t===n}},c=a("string"),f=a("array"),s=function(t){return n===t},m=l("boolean"),g=l("function"),d=function(t){var n=t.getParam("link_assume_external_targets",!1);return m(n)&&n?1:!c(n)||"http"!==n&&"https"!==n?0:n},v=function(t){return t.getParam("default_link_target")},h=function(t){return t.getParam("target_list",!0)},p=function(t){return t.getParam("rel_list",[],"array")},y=function(t){return t.getParam("allow_unsafe_link_target",!1,"boolean")},k=function(){},x=function(t){return function(){return t}},b=x(!1),O=x(!(n=null)),w=function(){return C},C=(t=function(t){return t.isNone()},{fold:function(t,n){return t()},is:b,isSome:b,isNone:O,getOr:r=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:x(null),getOrUndefined:x(undefined),or:r,orThunk:e,map:w,each:k,bind:w,exists:b,forall:O,filter:w,equals:t,equals_:t,toArray:function(){return[]},toString:x("none()")}),A=function(e){var t=x(e),n=function(){return o},r=function(t){return t(e)},o={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:O,isNone:b,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return A(t(e))},each:function(t){t(e)},bind:r,exists:r,forall:r,filter:function(t){return t(e)?o:C},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(b,function(t){return n(e,t)})}};return o},N={some:A,none:w,from:function(t){return null===t||t===undefined?C:A(t)}},P=Array.prototype.indexOf,_=Array.prototype.push,S=function(t,n){return e=t,r=n,-1<P.call(e,r);var e,r},T=function(t){for(var n=[],e=0,r=t.length;e<r;++e){if(!f(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);_.apply(n,t[e])}return n},D=function(t,n){return T(function(t,n){for(var e=t.length,r=new Array(e),o=0;o<e;o++){var i=t[o];r[o]=n(i,o)}return r}(t,n))},M=function(t,n){for(var e=0;e<t.length;e++){var r=n(t[e],e);if(r.isSome())return r}return N.none()},L=function(t,n){return t?N.some(n):N.none()},E=tinymce.util.Tools.resolve("tinymce.util.Tools"),R=function(t){return c(t.value)?t.value:""},U=function(e){return void 0===e&&(e=R),function(t){return N.from(t).map(function(t){return n=t,r=e,o=[],E.each(n,function(t){var n=c(t.text)?t.text:c(t.title)?t.title:"";if(t.menu===undefined){var e=r(t);o.push({text:n,value:e})}}),o;var n,r,o})}},q={sanitize:function(t){return U(R)(t)},sanitizeWith:U,createUi:function(n,e){return function(t){return{name:n,type:"selectbox",label:e,items:t}}},getValue:R},K=function(){return(K=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var o in n=arguments[e])Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o]);return t}).apply(this,arguments)},z=Object.keys,I=function(t,e,r,o){return function(t,n){for(var e=z(t),r=0,o=e.length;r<o;r++){var i=e[r];n(t[i],i)}}(t,function(t,n){(e(t,n)?r:o)(t,n)}),{}},j=function(t,n){var e,r={};return I(t,n,(e=r,function(t,n){e[n]=t}),k),r},B=function(t){return!(!/(^|[ ,])rtc([, ]|$)/.test(t.getParam("plugins","","string"))||!o.get("rtc"))},V=function(t){return/^\w+:/i.test(t)},F=function(t){var n=t.getAttribute("data-mce-href");return n||t.getAttribute("href")},W=function(t,n){var e,r,o=["noopener"],i=t?t.split(/\s+/):[],u=function(t){return t.filter(function(t){return-1===E.inArray(o,t)})},a=n?0<(e=u(e=i)).length?e.concat(o):o:u(i);return 0<a.length?(r=a,E.trim(r.sort().join(" "))):""},$=function(t,n){return n=n||t.selection.getNode(),Q(n)?t.dom.select("a[href]",n)[0]:t.dom.getParent(n,"a[href]")},H=function(t,n){var e=n?n.innerText||n.textContent:t.getContent({format:"text"});return e.replace(/\uFEFF/g,"")},G=function(t){return t&&"A"===t.nodeName&&!!F(t)},J=function(t){return 0<E.grep(t,G).length},X=function(t){return!(/</.test(t)&&(!/^<a [^>]+>[^<]+<\/a>$/.test(t)||-1===t.indexOf("href=")))},Q=function(t){return t&&"FIGURE"===t.nodeName&&/\bimage\b/i.test(t.className)},Y=function(t){return n=["title","rel","class","target"],e=function(n,e){return t[e].each(function(t){n[e]=0<t.length?t:null}),n},r={href:t.href},function(t,n){for(var e=0,r=t.length;e<r;e++)n(t[e],e)}(n,function(t){r=e(r,t)}),r;var n,e,r},Z=function(t,n){var e,r,o=K({},n);if(!(0<p(t).length)&&!1===y(t)){var i=W(o.rel,"_blank"===o.target);o.rel=i||null}return N.from(o.target).isNone()&&!1===h(t)&&(o.target=v(t)),o.href=(e=o.href,"http"!==(r=d(t))&&"https"!==r||V(e)?e:r+"://"+e),o},tt=function(l,c,f){var s=l.selection.getNode(),m=$(l,s),g=Z(l,Y(f));l.undoManager.transact(function(){var n,t,e,r,o,i,u,a;f.href===c.href&&c.attach(),m?(l.focus(),o=l,i=m,u=f.text,a=g,u.each(function(t){i.hasOwnProperty("innerText")?i.innerText=t:i.textContent=t}),o.dom.setAttribs(i,a),o.selection.select(i)):(n=l,t=s,e=f.text,r=g,Q(t)?ot(n,t,r):e.fold(function(){n.execCommand("mceInsertLink",!1,r)},function(t){n.insertContent(n.dom.createHTML("a",r,n.dom.encode(t)))}))})},nt=function(t,n,e){var r,o,i,u,a,l,c;B(t)?t.execCommand("createlink",!1,(o=(r=e)["class"],i=r.href,u=r.rel,a=r.target,l=r.text,c=r.title,j({"class":o.getOrNull(),href:i,rel:u.getOrNull(),target:a.getOrNull(),text:l.getOrNull(),title:c.getOrNull()},function(t,n){return!1===s(t)}))):tt(t,n,e)},et=function(t){var e;B(t)?t.execCommand("unlink"):(e=t).undoManager.transact(function(){var t=e.selection.getNode();if(Q(t))rt(e,t);else{var n=e.dom.getParent(t,"a[href]",e.getBody());n&&e.dom.remove(n,!0)}e.focus()})},rt=function(t,n){var e=t.dom.select("img",n)[0];if(e){var r=t.dom.getParents(e,"a[href]",n)[0];r&&(r.parentNode.insertBefore(e,r),t.dom.remove(r))}},ot=function(t,n,e){var r=t.dom.select("img",n)[0];if(r){var o=t.dom.create("a",e);r.parentNode.insertBefore(o,r),o.appendChild(r)}},it=function(n,t,e,r){var o,i=r[t],u=0<n.length;return i!==undefined?(o=i,M(e,function(t){return L(t.value===o,t)}).map(function(t){return{url:{value:t.value,meta:{text:u?n:t.text,attach:k}},text:u?n:t.text}})):N.none()},ut=function(t,i){var u={text:t.text,title:t.title},r=function(t){var n,e,r=(n=t.url,L(u.text.length<=0,N.from(n.meta.text).getOr(n.value))),o=(e=t.url,L(u.title.length<=0,N.from(e.meta.title).getOr("")));return r.isSome()||o.isSome()?N.some(K(K({},r.map(function(t){return{text:t}}).getOr({})),o.map(function(t){return{title:t}}).getOr({}))):N.none()},o=function(t,n){var e,r,o=(e=i,("link"===(r=n.name)?e.link:"anchor"===r?e.anchor:N.none()).getOr([]));return it(u.text,n.name,o,t)};return{onChange:function(t,n){var e=n.name;return"url"===e?r(t()):S(["anchor","link"],e)?o(t(),n):(("text"===e||"title"===e)&&(u[e]=t()[e]),N.none())}}},at=tinymce.util.Tools.resolve("tinymce.util.Delay"),lt=tinymce.util.Tools.resolve("tinymce.util.Promise"),ct=function(t){var n=t.href;return 0<n.indexOf("@")&&-1===n.indexOf("/")&&-1===n.indexOf("mailto:")?N.some({message:"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",preprocess:function(t){return K(K({},t),{href:"mailto:"+n})}}):N.none()},ft=function(u,a){return M([ct,(e=d(u),r=u.getParam("link_default_protocol","http","string"),function(t){var n=t.href;return 1===e&&!V(n)||0===e&&/^\s*www[\.|\d\.]/i.test(n)?N.some({message:"The URL you entered seems to be an external link. Do you want to add the required "+r+":// prefix?",preprocess:function(t){return K(K({},t),{href:r+"://"+n})}}):N.none()})],function(t){return t(a)}).fold(function(){return lt.resolve(a)},function(i){return new lt(function(n){var e,t,r,o;e=u,t=i.message,r=function(t){n(t?i.preprocess(a):a)},o=e.selection.getRng(),at.setEditorTimeout(e,function(){e.windowManager.confirm(t,function(t){e.selection.setRng(o),r(t)})})})});var e,r},st=function(t){var n=t.dom.select("a:not([href])"),e=D(n,function(t){var n=t.name||t.id;return n?[{text:n,value:"#"+n}]:[]});return 0<e.length?N.some([{text:"None",value:""}].concat(e)):N.none()},mt=function(t){var n=t.getParam("link_class_list",[],"array");return 0<n.length?q.sanitize(n):N.none()},gt=tinymce.util.Tools.resolve("tinymce.util.XHR"),dt=function(n){var e=function(t){return n.convertURL(t.value||t.url,"href")},t=n.getParam("link_list");return new lt(function(n){c(t)?gt.send({url:t,success:function(t){return n(function(t){try{return N.some(JSON.parse(t))}catch(n){return N.none()}}(t))},error:function(t){return n(N.none())}}):g(t)?t(function(t){return n(N.some(t))}):n(N.from(t))}).then(function(t){return t.bind(q.sanitizeWith(e)).map(function(t){return 0<t.length?[{text:"None",value:""}].concat(t):t})})},ht=function(t,n){var e=p(t);if(0<e.length){var r=n.is("_blank");return(!1===y(t)?q.sanitizeWith(function(t){return W(q.getValue(t),r)}):q.sanitize)(e)}return N.none()},vt=[{text:"Current window",value:""},{text:"New window",value:"_blank"}],pt=function(t){var n=h(t);return f(n)?q.sanitize(n).orThunk(function(){return N.some(vt)}):!1===n?N.none():N.some(vt)},yt=function(t,n,e){var r=t.getAttrib(n,e);return null!==r&&0<r.length?N.some(r):N.none()},kt=function(f,s){return dt(f).then(function(t){var n,e,r,o,i,u,a,l,c=(e=s,r=(n=f).dom,o=X(n.selection.getContent())?N.some(H(n.selection,e)):N.none(),i=e?N.some(r.getAttrib(e,"href")):N.none(),u=e?N.from(r.getAttrib(e,"target")):N.none(),a=yt(r,e,"rel"),l=yt(r,e,"class"),{url:i,text:o,title:yt(r,e,"title"),target:u,rel:a,linkClass:l});return{anchor:c,catalogs:{targets:pt(f),rels:ht(f,c.target),classes:mt(f),anchor:st(f),link:t},optNode:N.from(s),flags:{titleEnabled:f.getParam("link_title",!0,"boolean")}}})},xt=function(h){var t,n;(n=$(t=h),kt(t,n)).then(function(t){var i,u,n,e,r,o,a,l,c,f,s,m,g,d;return e=function(t){var e=t.getData();if(!e.url.value)return et(i),void t.close();var n=function(n){return N.from(e[n]).filter(function(t){return!u.anchor[n].is(t)})},r={href:e.url.value,text:n("text"),target:n("target"),rel:n("rel"),"class":n("linkClass"),title:n("title")},o={href:e.url.value,attach:e.url.meta!==undefined&&e.url.meta.attach?e.url.meta.attach:function(){}};ft(i,r).then(function(t){nt(i,o,t)}),t.close()},r=i=h,c=(n=u=t).anchor.text.map(function(){return{name:"text",type:"input",label:"Text to display"}}).toArray(),f=n.flags.titleEnabled?[{name:"title",type:"input",label:"Title"}]:[],s=N.from(v(r)),o=s,a=n.anchor,m={url:{value:l=a.url.getOr(""),meta:{original:{value:l}}},text:a.text.getOr(""),title:a.title.getOr(""),anchor:l,link:l,rel:a.rel.getOr(""),target:a.target.or(o).getOr(""),linkClass:a.linkClass.getOr("")},g=n.catalogs,d=ut(m,g),{title:"Insert/Edit Link",size:"normal",body:{type:"panel",items:T([[{name:"url",type:"urlinput",filetype:"file",label:"URL"}],c,f,function(t){for(var n=[],e=function(t){n.push(t)},r=0;r<t.length;r++)t[r].each(e);return n}([g.anchor.map(q.createUi("anchor","Anchors")),g.rels.map(q.createUi("rel","Rel")),g.targets.map(q.createUi("target","Open link in...")),g.link.map(q.createUi("link","Link list")),g.classes.map(q.createUi("linkClass","Class"))])])},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:m,onChange:function(n,t){var e=t.name;d.onChange(n.getData,{name:e}).each(function(t){n.setData(t)})},onSubmit:e}}).then(function(t){h.windowManager.open(t)})},bt=function(t){var n=i.document.createElement("a");n.target="_blank",n.href=t,n.rel="noreferrer noopener";var e,r,o=i.document.createEvent("MouseEvents");o.initMouseEvent("click",!0,!0,i.window,0,0,0,0,0,!1,!1,!1,!1,0,null),e=n,r=o,i.document.body.appendChild(e),e.dispatchEvent(r),i.document.body.removeChild(e)},Ot=function(t,n){return t.dom.getParent(n,"a[href]")},wt=function(t){return Ot(t,t.selection.getStart())},Ct=function(t,n){if(n){var e=F(n);if(/^#/.test(e)){var r=t.$(e);r.length&&t.selection.scrollIntoView(r[0],!0)}else bt(n.href)}},At=function(t){return function(){xt(t)}},Nt=function(t){return function(){Ct(t,wt(t))}},Pt=function(r){r.on("click",function(t){var n=Ot(r,t.target);n&&u.metaKeyPressed(t)&&(t.preventDefault(),Ct(r,n))}),r.on("keydown",function(t){var n,e=wt(r);e&&13===t.keyCode&&(!0===(n=t).altKey&&!1===n.shiftKey&&!1===n.ctrlKey&&!1===n.metaKey)&&(t.preventDefault(),Ct(r,e))})},_t=function(e){return function(n){var t=function(t){return n.setActive(!e.mode.isReadOnly()&&!!$(e,t.element))};return e.on("NodeChange",t),function(){return e.off("NodeChange",t)}}},St=function(r){return function(n){var t=r.dom.getParents(r.selection.getStart());n.setDisabled(!J(t));var e=function(t){return n.setDisabled(!J(t.parents))};return r.on("NodeChange",e),function(){return r.off("NodeChange",e)}}};!function Tt(){o.add("link",function(t){var n,e,r,i,o,u,a;(n=t).ui.registry.addToggleButton("link",{icon:"link",tooltip:"Insert/edit link",onAction:At(n),onSetup:_t(n)}),n.ui.registry.addButton("openlink",{icon:"new-tab",tooltip:"Open link",onAction:Nt(n),onSetup:St(n)}),n.ui.registry.addButton("unlink",{icon:"unlink",tooltip:"Remove link",onAction:function(){return et(n)},onSetup:St(n)}),(e=t).ui.registry.addMenuItem("openlink",{text:"Open link",icon:"new-tab",onAction:Nt(e),onSetup:St(e)}),e.ui.registry.addMenuItem("link",{icon:"link",text:"Link...",shortcut:"Meta+K",onAction:At(e)}),e.ui.registry.addMenuItem("unlink",{icon:"unlink",text:"Remove link",onAction:function(){return et(e)},onSetup:St(e)}),(r=t).ui.registry.addContextMenu("link",{update:function(t){return J(r.dom.getParents(t,"a"))?"link unlink openlink":"link"}}),o=function(t){var n=i.selection.getNode();return t.setDisabled(!$(i,n)),function(){}},(i=t).ui.registry.addContextForm("quicklink",{launch:{type:"contextformtogglebutton",icon:"link",tooltip:"Link",onSetup:_t(i)},label:"Link",predicate:function(t){return!!$(i,t)&&i.getParam("link_context_toolbar",!1,"boolean")},initValue:function(){var t=$(i);return t?F(t):""},commands:[{type:"contextformtogglebutton",icon:"link",tooltip:"Link",primary:!0,onSetup:function(t){var n=i.selection.getNode();return t.setActive(!!$(i,n)),_t(i)(t)},onAction:function(t){var n=$(i),e=t.getValue();if(n)i.undoManager.transact(function(){i.dom.setAttrib(n,"href",e),i.selection.collapse(!1),t.hide()});else{var r={href:e,attach:function(){}},o=X(i.selection.getContent())?N.some(H(i.selection,n)).filter(function(t){return 0<t.length}).or(N.from(e)):N.none();nt(i,r,{href:e,text:o,title:N.none(),rel:N.none(),target:N.none(),"class":N.none()}),t.hide()}}},{type:"contextformbutton",icon:"unlink",tooltip:"Remove link",onSetup:o,onAction:function(t){et(i),t.hide()}},{type:"contextformbutton",icon:"new-tab",tooltip:"Open link",onSetup:o,onAction:function(t){Nt(i)(),t.hide()}}]}),Pt(t),(u=t).addCommand("mceLink",function(){u.getParam("link_quicklink",!1,"boolean")?u.fire("contexttoolbar-show",{toolbarKey:"quicklink"}):At(u)()}),(a=t).addShortcut("Meta+K","",function(){a.execCommand("mceLink")})})}()}(window);