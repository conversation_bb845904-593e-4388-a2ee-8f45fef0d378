<component name="libraryTable">
  <library name="Maven: org.crazycake:shiro-redis:2.4.6">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/crazycake/shiro-redis/2.4.6/shiro-redis-2.4.6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/crazycake/shiro-redis/2.4.6/shiro-redis-2.4.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/crazycake/shiro-redis/2.4.6/shiro-redis-2.4.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>