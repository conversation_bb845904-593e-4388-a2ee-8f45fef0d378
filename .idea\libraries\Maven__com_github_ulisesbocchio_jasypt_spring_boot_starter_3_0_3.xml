<component name="libraryTable">
  <library name="Maven: com.github.ulisesbocchio:jasypt-spring-boot-starter:3.0.3">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/ulisesbocchio/jasypt-spring-boot-starter/3.0.3/jasypt-spring-boot-starter-3.0.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/ulisesbocchio/jasypt-spring-boot-starter/3.0.3/jasypt-spring-boot-starter-3.0.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/ulisesbocchio/jasypt-spring-boot-starter/3.0.3/jasypt-spring-boot-starter-3.0.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>