<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>定时任务管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <blockquote class="layui-elem-quote quoteBox" id="search">
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto"
                                   style="width: unset;padding: 9px 15px 9px 0px;">任务名称：</label>
                            <div class="layui-input-inline mr0">
                                <input class="layui-input" id="searchValue" autocomplete="off" placeholder="请输入任务名称">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto"
                                   style="width: unset;padding: 9px 15px 9px 0px;">任务类名：</label>
                            <div class="layui-input-inline mr0">
                                <input class="layui-input" id="searchValue2" autocomplete="off" placeholder="请输入任务类名称">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                            <button id="searchBtn" lay-event="search_btn" class="layui-btn">
                                <i class="layui-icon">&#xe615;</i>查询
                            </button>
                            <button type="reset" class="layui-btn" id="unsetBtn">
                                <i class="layui-icon">&#xe669;</i>重置
                            </button>
                        </div>
                    </blockquote>
                    <table class="layui-hide" id="task_table" lay-filter="task_table"></table>

                    <script type="text/html" id="topToolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i
                                    class="layui-icon">&#xe608;</i>增加
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i
                                    class="layui-icon">&#xe642;</i>编辑
                            </button>
                            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i
                                    class="layui-icon">&#xe640;</i>删除
                            </button>
                        </div>
                    </script>

                    <script type="text/html" id="activeToolbar">
                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    //回车搜索
    $("blockquote").on('keyup','input,select',function (e) {
        if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.use('table', function () {
        var admin = layui.admin
            ,table = layui.table
            ,form=layui.form;

        table.render({
            elem: '#task_table'
            , url: ctx + 'taskController/taskJson'
            , toolbar: '#topToolbar'
            ,defaultToolbar: ['filter']
            , title: '任务数据表'
            , cols: [
                [
                    {type: 'checkbox'}
                    , {field: 'id', title: 'ID', hide: true, sort: true}
                    , {field: 'name', title: '任务名称',width:120,align:'center'}
                    , {field: 'cron', title: '任务表达式',width:120,align:'center'}
                    , {field: 'job_name', title: '任务类名称',width:200,align:'center'}
                    , {field: 'describe', title: '任务描述',align:'center'}
                    ,{field: 'createby', title: '创建人',width:120,align:'center'}
                    ,{field: 'createTime', title: '创建日期',align:'center'}
                    ,{field: 'status',title:'状态',width:100,align:'center',
                    templet:function(data){
                        var html = '';
                        if(data.status=='1'){
                            html += '<input type="checkbox" value='+data.id+'  checked=true name="status" id="status" lay-skin="switch" lay-text="启用|关闭">'
                        }else{
                            html += '<input type="checkbox" value='+data.id+' name="status" id="status" lay-skin="switch" lay-text="启用|关闭">'
                        }
                        return	html;
                    }
                    }
                    , {fixed: 'right', title: '操作', toolbar: '#activeToolbar', align: 'center'}
                ]
            ]
            , request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true
        });
        form.on('switch()', function(data){
            var id = data.value;
            var status =this.checked=='1' ? '1' : '0';
            $.ajax({
                url:ctx + 'taskController/showTask?id='+id+'&status='+status,
                success:function(res){
                    if(res.code == 0){
                        layer.msg(res.stateMsg,{icon: 2});
                    }else{
                        layer.msg(res.stateMsg,{icon: 1});
                    }
                },
                error:function(data){
                    layer.msg('操作失败',{icon: 2});
                }
            });
        });
        //头工具栏事件
        table.on('toolbar(task_table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id),
                width = '800', height = '500', submitButId = '#submitBut';
            switch (obj.event) {
                case 'add_btn':
                    var url = ctx + 'taskController/addTask',
                        title = '新增任务';
                    xadmin.openWindowFull(url, title, submitButId);
                    break;
                case 'edit_btn':
                    var data = checkStatus.data;
                    if (data.length == 1) {
                        var url = ctx + 'taskController/editTask?id=' + data[0].id,
                            title = '编辑任务';
                        xadmin.openWindowFull(url, title,submitButId);
                    } else {
                        layer.msg("请选择一条数据", {
                            icon: 2
                        })
                    }
                    break;
                case 'delete_btn':
                    var data = JSON.stringify(checkStatus.data);
                    var checked = checkStatus.data;
                    if (checked.length < 1) {
                        layer.msg('请至少选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    var ids = '';
                    for (var i = 0; i < checked.length; i++) {
                        if(i != checked.length-1){
                            ids += checked[i].id + ','
                        }else {
                            ids += checked[i].id
                        }
                    }
                    layer.confirm('您确定要删除？', {icon:3,btn:['确定','取消'],title:'提示'}, function () {
                        var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                        $.ajax({
                            method: "POST",
                            url: ctx + 'taskController/batchDeleteTask',
                            data: {'ids': ids},
                            async: true,
                            cache: false,
                            success: function (res) {
                                layer.close(loading);
                                console.log(res);
                                if (res.stateType == 0) {
                                    //layui.table.reload("csmb-table");
                                    layer.msg(res.stateMsg, {icon: 1});
                                    table.reload("task_table");
                                } else {
                                    layer.msg(res.stateMsg, {icon: 2});
                                }
                            },
                            error: function (res) {
                                layer.close(loading);
                                layer.msg('操作失败', {icon: 2});
                            }
                        })
                    })
                    break;
            }
            ;
        });

        //监听行工具事件
        table.on('tool(task_table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                // 删除函数
                var url = ctx + 'taskController/deleteTask?id=' + data.id;
                layer.confirm('您确定要删除',{icon:3,btn:['确定','取消'],title:'提示'},function(){
                    var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
                    $.ajax({
                        url:url,
                        success:function(res){
                            layer.close(loading);
                            if(res.code == 0){
                                layer.msg(res.msg,{icon: 1});
                                table.reload("task_table");
                            }else{
                                layer.msg(res.msg,{icon: 2});
                            }
                        },
                        error:function(res){
                            layer.close(loading);
                            layer.msg('操作失败',{icon: 2});
                        }
                    })
                });
                //xadmin.deleteDemo(url, table, tableName);
            } else if (obj.event === 'edit') {
                var url = ctx + 'taskController/editTask?id=' + data.id,
                    title = '编辑任务', width = '800', height = '500', submitButId = '#submitBut';
                xadmin.openWindowFull(url, title, submitButId);
            }
        });
        //搜索及重置按钮
        $("#searchBtn").click(function () {
            var searchValue = $("#searchValue");
            var searchValue2 = $("#searchValue2");
            table.reload('task_table', {
                where: { //设定异步数据接口的额外参数
                    name: searchValue.val(),
                    job_name:searchValue2.val()
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
            return false;
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#searchValue").val("");
            $("#searchValue2").val("");
            location.reload();
            table.reload('task_table', {
                where: null
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            }); //只重载数据
            return false;
        })

    });

</script>

</body>
</html>