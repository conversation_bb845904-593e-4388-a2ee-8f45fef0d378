<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>预警统计</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
</head>

<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <div class="layui-inline">
                    <label class="layui-form-label">地点：</label>
                    <div class="layui-input-inline">
                        <div class="xm-select-demo" id="typeSelect"></div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                    <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                    <button class="layui-btn" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                </div>
            </blockquote>
            <!--  展示图表1,2的位置  -->
            <div style="width: 1200px;height:250px;display: flex;justify-content: space-around;">
                <div id="chartContainer" style="width: 48%;height:100%;"></div>
                <div id="chartContainer1" style="width: 48%;height:100%;"></div>
            </div>
            </br>
            </br>

            <!--  展示图表3,4的位置  -->
            <div style="width: 1200px;height:250px;display: flex;justify-content: space-around;">
                <div id="chartContainer2" style="width: 48%;height:100%;"></div>
                <div id="chartContainer3" style="width: 48%;height:100%;"></div>
            </div>

        </div>
    </div>
</div>

<!--</br>-->
<!--</br>-->
<!--</br>-->
<!--&lt;!&ndash;  展示图表1,2的位置  &ndash;&gt;-->
<!--<div style="width: 1200px;height:250px;display: flex;justify-content: space-around;">-->
<!--    <div id="chartContainer" style="width: 48%;height:100%;"></div>-->
<!--    <div id="chartContainer1" style="width: 48%;height:100%;"></div>-->
<!--</div>-->
<!--</br>-->
<!--</br>-->

<!--&lt;!&ndash;  展示图表3,4的位置  &ndash;&gt;-->
<!--<div style="width: 1200px;height:250px;display: flex;justify-content: space-around;">-->
<!--    <div id="chartContainer2" style="width: 48%;height:100%;"></div>-->
<!--    <div id="chartContainer3" style="width: 48%;height:100%;"></div>-->
<!--</div>-->

<script th:src="@{/plugins/echarts/echarts.min.js}"></script>
<script>
    var deptId = [[${deptId}]];
    console.log(deptId);

    $(document).ready(function () {
        // 异步请求获取数据
        // $.ajax({
        //     url: ctx + '/reportController/getMonthAndCount1',
        //     type: 'GET',
        //     dataType: 'json',
        //     success: function (res) {
        //         if (res.code === 0) {
        //             var obj1 = res.data;
        //             var data1 = obj1['list1'];
        //             var data2 = obj1['list2'];
        //             var data3 = obj1['list3'];
        //             var xAxisData = [];
        //             var dataMap = {
        //                 a1: [],
        //                 a2: [],
        //                 a3: []
        //             };
        //
        //             // 将数据按月份存储到dataMap中
        //             data1.forEach(item => {
        //                 var month = item['month'];
        //                 var counts = item['count'];
        //                 xAxisData.push(month);
        //                 dataMap['a1'].push(counts);
        //             });
        //             data2.forEach(item => {
        //                 var month = item['month'];
        //                 var counts = item['count'];
        //                 dataMap['a2'].push(counts);
        //             });
        //             data3.forEach(item => {
        //                 var month = item['month'];
        //                 var counts = item['count'];
        //                 dataMap['a3'].push(counts);
        //             });
        //             console.log(xAxisData);
        //             console.log(dataMap['a1']);
        //             console.log(dataMap['a2']);
        //             console.log(dataMap['a3']);
        //             // 填充到折线图的series中
        //             var option = {
        //                 title: {text: '预警统计'},
        //                 tooltip: {trigger: 'axis'},
        //                 legend: {data: ['全部预警','黑名单预警', '疑似预警']},
        //                 grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
        //                 toolbox: {feature: {saveAsImage: {}}},
        //                 xAxis: {
        //                     type: 'category',
        //                     boundaryGap: false,
        //                     data: xAxisData
        //                 },
        //                 yAxis: {
        //                     type: 'value'
        //                 },
        //                 series: [
        //                     {name: '全部预警',type: 'line',
        //                         data: dataMap['a1']},
        //                     {name: '黑名单预警',type: 'line',
        //                         data: dataMap['a2']},
        //                     {name: '疑似预警',type: 'line',
        //                         data: dataMap['a3']}
        //                 ]
        //             };
        //
        //             // 渲染折线图
        //             var chartContainer = $('#chartContainer').get(0);
        //             echarts.init(chartContainer).setOption(option);
        //         } else {
        //             layer.msg('获取数据失败');
        //         }
        //     },
        //     error: function () {
        //         layer.msg('获取数据失败');
        //     }
        // });
        const carModel = [];
        carModel.push(deptId);
        getMonthAndCount1(carModel);
    });

    $(document).ready(function () {
        // 异步请求获取数据
        // $.ajax({
        //     url: ctx + '/reportController/getMonthAndCount2',
        //     type: 'GET',
        //     dataType: 'json',
        //     success: function (res) {
        //         if (res.code === 0) {
        //             var obj1 = res.data;
        //             var data1 = obj1['list1'];
        //             var data2 = obj1['list2'];
        //             var data3 = obj1['list3'];
        //             var xAxisData = [];
        //             var dataMap = {
        //                 a1: [],
        //                 a2: [],
        //                 a3: []
        //             };
        //
        //             // 将数据按月份存储到dataMap中
        //             data1.forEach(item => {
        //                 var month = item['month'];
        //                 var count = item['count'];
        //                 xAxisData.push(month);
        //                 dataMap['a1'].push(count);
        //             });
        //             data2.forEach(item => {
        //                 var month = item['month'];
        //                 var count = item['count'];
        //                 dataMap['a2'].push(count);
        //             });
        //             data3.forEach(item => {
        //                 var month = item['month'];
        //                 var count = item['count'];
        //                 dataMap['a3'].push(count);
        //             });
        //             console.log(xAxisData);
        //             // 填充到折线图的series中
        //             var option1 = {
        //                 title: {text: '处理次数统计'},
        //                 tooltip: {trigger: 'axis'},
        //                 legend: {data: ['总次数','车辆处理次数', '人员处理次数']},
        //                 grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
        //                 toolbox: {feature: {saveAsImage: {}}},
        //                 xAxis: {
        //                     type: 'category',
        //                     boundaryGap: false,
        //                     data: xAxisData
        //                 },
        //                 yAxis: {
        //                     type: 'value'
        //                 },
        //                 series: [
        //                     {name: '总次数',type: 'line',
        //                         data: dataMap['a1']},
        //                     {name: '人员处理次数',type: 'line',
        //                         data: dataMap['a2']},
        //                     {name: '车辆处理次数',type: 'line',
        //                         data: dataMap['a3']}
        //                 ]
        //             };
        //
        //             // 渲染折线图
        //             var chartContainer = $('#chartContainer1').get(0);
        //             echarts.init(chartContainer).setOption(option1);
        //         } else {
        //             layer.msg('获取数据失败');
        //         }
        //     },
        //     error: function () {
        //         layer.msg('获取数据失败');
        //     }
        // });
        const carModel = [];
        carModel.push(deptId);
        getMonthAndCount2(carModel);
    });

    /*图3*/
    $(document).ready(function () {
        // 异步请求获取数据
        // $.ajax({
        //     url: ctx + '/reportController/getMonthAndCount3',
        //     type: 'GET',
        //     dataType: 'json',
        //     success: function (res) {
        //         if (res.code === 0) {
        //             var obj1 = res.data;
        //             var data1 = obj1['list1'];
        //             var data2 = obj1['list2'];
        //             var xAxisData = [];
        //             var dataMap = {
        //                 a1: [],
        //                 a2: []
        //             };
        //
        //             // 将数据按月份存储到dataMap中
        //             data1.forEach(item => {
        //                 var month = item['month'];
        //                 var count = item['count'];
        //                 xAxisData.push(month);
        //                 dataMap['a1'].push(count);
        //             });
        //             data2.forEach(item => {
        //                 var month = item['month'];
        //                 var count = item['count'];
        //                 dataMap['a2'].push(count);
        //             });
        //             console.log(xAxisData);
        //             // 填充到折线图的series中
        //             var option2 = {
        //                 title: {text: '疑似预警统计'},
        //                 tooltip: {trigger: 'axis'},
        //                 legend: {data: ['疑似人员', '疑似车辆']},
        //                 grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
        //                 toolbox: {feature: {saveAsImage: {}}},
        //                 xAxis: {
        //                     type: 'category',
        //                     boundaryGap: false,
        //                     data: xAxisData
        //                 },
        //                 yAxis: {type: 'value'},
        //                 series: [
        //                     {name: '疑似人员',type: 'line',
        //                         data: dataMap['a1']},
        //                     {name: '疑似车辆',type: 'line',
        //                         data: dataMap['a2']}
        //                 ]
        //             };
        //
        //             // 渲染折线图
        //             var chartContainer = $('#chartContainer2').get(0);
        //             echarts.init(chartContainer).setOption(option2);
        //         } else {
        //             layer.msg('获取数据失败');
        //         }
        //     },
        //     error: function () {
        //         layer.msg('获取数据失败');
        //     }
        // });
        const carModel = [];
        carModel.push(deptId);
        getMonthAndCount3(carModel);
    });


    /*图4*/
    $(document).ready(function () {
        // 异步请求获取数据
        // $.ajax({
        //     url: ctx + '/reportController/getMonthAndCount4',
        //     type: 'GET',
        //     dataType: 'json',
        //     success: function (res) {
        //         if (res.code === 0) {
        //             var obj1 = res.data;
        //             var data1 = obj1['list1'];
        //             var data2 = obj1['list2'];
        //             var xAxisData = [];
        //             var dataMap = {
        //                 a1: [],
        //                 a2: []
        //             };
        //
        //             // 将数据按月份存储到dataMap中
        //             data1.forEach(item => {
        //                 var month = item['month'];
        //                 var count = item['count'];
        //                 xAxisData.push(month);
        //                 dataMap['a1'].push(count);
        //             });
        //             data2.forEach(item => {
        //                 var month = item['month'];
        //                 var count = item['count'];
        //                 dataMap['a2'].push(count);
        //             });
        //             console.log(xAxisData);
        //             console.log(dataMap['a1']);
        //             console.log(dataMap['a2']);
        //             console.log(xAxisData);
        //             // 填充到折线图的series中
        //             var option3 = {
        //                 title: {text: '黑名单预警统计'},
        //                 tooltip: {trigger: 'axis'},
        //                 legend: {data: ['黑名单人员', '黑名单车辆']},
        //                 grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
        //                 toolbox: {feature: {saveAsImage: {}}},
        //                 xAxis: {
        //                     type: 'category',
        //                     boundaryGap: false,
        //                     data: xAxisData
        //                 },
        //                 yAxis: {type: 'value'},
        //                 series: [
        //                     {name: '黑名单人员',type: 'line',
        //                         data: dataMap['a1']},
        //                     {name: '黑名单车辆',type: 'line',
        //                         data: dataMap['a2']},
        //                 ]
        //             };
        //
        //             // 渲染折线图
        //             var chartContainer = $('#chartContainer3').get(0);
        //             echarts.init(chartContainer).setOption(option3);
        //         } else {
        //             layer.msg('获取数据失败');
        //         }
        //     },
        //     error: function () {
        //         layer.msg('获取数据失败');
        //     }
        // });
        const carModel = [];
        carModel.push(deptId);
        getMonthAndCount4(carModel);
    });

    //搜索及重置按钮
    $("#searchBtn").click(function () {

        const carModel = []
        typeSelect.getValue().forEach(data => {
            carModel.push(data.ID)
        })
        console.log("carModel",carModel);
        getMonthAndCount1(carModel);
        getMonthAndCount2(carModel);
        getMonthAndCount3(carModel);
        getMonthAndCount4(carModel);

    })

    //搜索及重置按钮
    $("#unsetBtn").click(function () {
        window.location.reload();
    })

    var typeSelect = xmSelect.render({
        el: '#typeSelect',
        radio: false,
        // clickClose: true,
        filterable: true,
        toolbar: {show: true},
        name: 'deptIds',
        layVerify: 'required',
        prop: {name: 'deptName', value: 'ID'},
        data: [],
        // 启用严格模式，确保不会有重复选中项
        strict: true,
        style: {
            paddingLeft: '0px',
            position: 'relative',
            width: '160px',
            height: '38px'
        },
        tree: {
            show: true,
            strict: false, //是否父子结构，父子结构父节点不会被选中
            indent: 30,//间距
            expandedKeys: [-1],
            clickCheck: true,
            clickExpand: true,//点击展开
        },

    });

    $.ajax({
        url: ctx + '/deptController/deptDetail',
        type: 'PUT',
        success: function (res) {

            // 1. 将ID统一转为字符串
            // var data = res.data.map(item => {
            //     return {
            //         deptName: item.deptName,
            //         ID: item.ID.toString() // 确保ID是字符串
            //     };
            // });

            // 3. 更新下拉框数据
            typeSelect.update({
                data: res.data,
            });

            // 4. 设置初始值（确保deptId转为字符串）
            var initValue = deptId ? deptId.toString() : "";
            if (initValue) {
                typeSelect.setValue([initValue]);
            }
        }
    });

    function getMonthAndCount1(deptIds){

        console.log("deptIds",deptIds);
        // 异步请求获取数据
        $.ajax({
            url: ctx + '/reportController/getMonthAndCount1',
            type: 'POST',
            data: JSON.stringify({
                deptIds:deptIds,
            }),
            contentType: 'application/json' ,
            success: function (res) {
                if (res.code === 0) {
                    var obj1 = res.data;
                    var data1 = obj1['list1'];
                    var data2 = obj1['list2'];
                    var data3 = obj1['list3'];
                    var xAxisData = [];
                    var dataMap = {
                        a1: [],
                        a2: [],
                        a3: []
                    };

                    // 将数据按月份存储到dataMap中
                    data1.forEach(item => {
                        var month = item['month'];
                        var counts = item['count'];
                        xAxisData.push(month);
                        dataMap['a1'].push(counts);
                    });
                    data2.forEach(item => {
                        var month = item['month'];
                        var counts = item['count'];
                        dataMap['a2'].push(counts);
                    });
                    data3.forEach(item => {
                        var month = item['month'];
                        var counts = item['count'];
                        dataMap['a3'].push(counts);
                    });
                    console.log(xAxisData);
                    console.log(dataMap['a1']);
                    console.log(dataMap['a2']);
                    console.log(dataMap['a3']);
                    // 填充到折线图的series中
                    var option = {
                        title: {text: '预警统计'},
                        tooltip: {trigger: 'axis'},
                        legend: {data: ['全部预警','黑名单预警', '疑似预警']},
                        grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
                        toolbox: {feature: {saveAsImage: {}}},
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: xAxisData
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {name: '全部预警',type: 'line',
                                data: dataMap['a1']},
                            {name: '黑名单预警',type: 'line',
                                data: dataMap['a2']},
                            {name: '疑似预警',type: 'line',
                                data: dataMap['a3']}
                        ]
                    };

                    // 渲染折线图
                    var chartContainer = $('#chartContainer').get(0);
                    echarts.init(chartContainer).setOption(option);
                } else {
                    layer.msg('获取数据失败');
                }
            },
            error: function () {
                layer.msg('获取数据失败');
            }
        });
    }

    function getMonthAndCount2(deptIds){

        $.ajax({
            url: ctx + '/reportController/getMonthAndCount2',
            type: 'POST',
            data: JSON.stringify({
                deptIds:deptIds,
            }),
            contentType: 'application/json' ,
            success: function (res) {
                if (res.code === 0) {
                    var obj1 = res.data;
                    var data1 = obj1['list1'];
                    var data2 = obj1['list2'];
                    var data3 = obj1['list3'];
                    var xAxisData = [];
                    var dataMap = {
                        a1: [],
                        a2: [],
                        a3: []
                    };

                    // 将数据按月份存储到dataMap中
                    data1.forEach(item => {
                        var month = item['month'];
                        var count = item['count'];
                        xAxisData.push(month);
                        dataMap['a1'].push(count);
                    });
                    data2.forEach(item => {
                        var month = item['month'];
                        var count = item['count'];
                        dataMap['a2'].push(count);
                    });
                    data3.forEach(item => {
                        var month = item['month'];
                        var count = item['count'];
                        dataMap['a3'].push(count);
                    });
                    console.log(xAxisData);
                    // 填充到折线图的series中
                    var option1 = {
                        title: {text: '处理次数统计'},
                        tooltip: {trigger: 'axis'},
                        legend: {data: ['总次数','车辆处理次数', '人员处理次数']},
                        grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
                        toolbox: {feature: {saveAsImage: {}}},
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: xAxisData
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {name: '总次数',type: 'line',
                                data: dataMap['a1']},
                            {name: '人员处理次数',type: 'line',
                                data: dataMap['a2']},
                            {name: '车辆处理次数',type: 'line',
                                data: dataMap['a3']}
                        ]
                    };

                    // 渲染折线图
                    var chartContainer = $('#chartContainer1').get(0);
                    echarts.init(chartContainer).setOption(option1);
                } else {
                    layer.msg('获取数据失败');
                }
            },
            error: function () {
                layer.msg('获取数据失败');
            }
        });
    }

    function getMonthAndCount3(deptIds){
        $.ajax({
            url: ctx + '/reportController/getMonthAndCount3',
            type: 'POST',
            data: JSON.stringify({
                deptIds:deptIds,
            }),
            contentType: 'application/json' ,
            success: function (res) {
                if (res.code === 0) {
                    var obj1 = res.data;
                    var data1 = obj1['list1'];
                    var data2 = obj1['list2'];
                    var xAxisData = [];
                    var dataMap = {
                        a1: [],
                        a2: []
                    };

                    // 将数据按月份存储到dataMap中
                    data1.forEach(item => {
                        var month = item['month'];
                        var count = item['count'];
                        xAxisData.push(month);
                        dataMap['a1'].push(count);
                    });
                    data2.forEach(item => {
                        var month = item['month'];
                        var count = item['count'];
                        dataMap['a2'].push(count);
                    });
                    console.log(xAxisData);
                    // 填充到折线图的series中
                    var option2 = {
                        title: {text: '疑似预警统计'},
                        tooltip: {trigger: 'axis'},
                        legend: {data: ['疑似人员', '疑似车辆']},
                        grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
                        toolbox: {feature: {saveAsImage: {}}},
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: xAxisData
                        },
                        yAxis: {type: 'value'},
                        series: [
                            {name: '疑似人员',type: 'line',
                                data: dataMap['a1']},
                            {name: '疑似车辆',type: 'line',
                                data: dataMap['a2']}
                        ]
                    };

                    // 渲染折线图
                    var chartContainer = $('#chartContainer2').get(0);
                    echarts.init(chartContainer).setOption(option2);
                } else {
                    layer.msg('获取数据失败');
                }
            },
            error: function () {
                layer.msg('获取数据失败');
            }
        });

    }

    function getMonthAndCount4(deptIds){
        $.ajax({
            url: ctx + '/reportController/getMonthAndCount4',
            type: 'POST',
            data: JSON.stringify({
                deptIds:deptIds,
            }),
            contentType: 'application/json' ,
            success: function (res) {
                if (res.code === 0) {
                    var obj1 = res.data;
                    var data1 = obj1['list1'];
                    var data2 = obj1['list2'];
                    var xAxisData = [];
                    var dataMap = {
                        a1: [],
                        a2: []
                    };

                    // 将数据按月份存储到dataMap中
                    data1.forEach(item => {
                        var month = item['month'];
                        var count = item['count'];
                        xAxisData.push(month);
                        dataMap['a1'].push(count);
                    });
                    data2.forEach(item => {
                        var month = item['month'];
                        var count = item['count'];
                        dataMap['a2'].push(count);
                    });
                    console.log(xAxisData);
                    console.log(dataMap['a1']);
                    console.log(dataMap['a2']);
                    console.log(xAxisData);
                    // 填充到折线图的series中
                    var option3 = {
                        title: {text: '黑名单预警统计'},
                        tooltip: {trigger: 'axis'},
                        legend: {data: ['黑名单人员', '黑名单车辆']},
                        grid: {left: '3%',right: '4%',bottom: '3%',containLabel: true},
                        toolbox: {feature: {saveAsImage: {}}},
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: xAxisData
                        },
                        yAxis: {type: 'value'},
                        series: [
                            {name: '黑名单人员',type: 'line',
                                data: dataMap['a1']},
                            {name: '黑名单车辆',type: 'line',
                                data: dataMap['a2']},
                        ]
                    };

                    // 渲染折线图
                    var chartContainer = $('#chartContainer3').get(0);
                    echarts.init(chartContainer).setOption(option3);
                } else {
                    layer.msg('获取数据失败');
                }
            },
            error: function () {
                layer.msg('获取数据失败');
            }
        });
    }
</script>
</body>
</html>