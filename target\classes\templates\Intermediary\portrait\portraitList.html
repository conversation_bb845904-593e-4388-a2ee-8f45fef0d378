<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <style>
        /*固定input宽度*/
        .layui-input, .layui-textarea {
            display: block;
            width: 180px;
            padding-left: 10px;
        }

        .layui-none {
            width: 100%;
            line-height: 26px;
            padding: 15px;
            text-align: center;
            color: #999;
        }

        .card-container {
            padding: 10px 15px;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .layui-row .layui-card {
            width: 190px;
            margin-left: 5px;
            margin-right: 5px;
            margin-bottom: 20px;
            height: 140px;
            background-color: #f2f2f2;
            border: 1px solid #ccc; /* 添加边框 */
            border-radius: 4px; /* 添加边框圆角 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
        }


        .card-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
        }

        .image-wrapper {
            height: 90px;
        }

        .card-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .text-wrapper {
            text-align: center;
        }

        .card-label {
            margin: 0;

            font-size: 11px;
            line-height: 1.5;
        }

        .card-text {
            margin: 0;
            font-size: 11px;
            line-height: 1.5;
        }

        .red {
            color: red;
        }

        .green {
            color: green;
        }

        .orange {
            color: orange;
        }

        .red-block {
            background-color: red;
        }

        .orange-block {
            background-color: orange;
        }

        .left-column {
            width: 90px;
            float: left
        }

        .right-column {
            width: 70px;
            float: right;
        }

        .color-block {
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-right: 5px;
        }

        .right-corner-comment {
            position: absolute;
            top: 0;
            right: 0;
            margin-top: 10px;
            margin-right: 10px;
        }
    </style>
</head>

<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <div class="layui-form">
                    <div class="layui-form-item " style="margin:0;">

                        <div class="layui-inline">
                            <input type="text" name="startDate" id="startDate" placeholder="开始日期" autocomplete="off"
                                   class="layui-input" value="">
                        </div>
                        <div class="layui-inline">
                            <input type="text" name="endDate" id="endDate" placeholder="结束日期" autocomplete="off"
                                   class="layui-input" value="">
                        </div>
                        <div class="layui-inline">
                            <input type="number" name="minCount" id="minCount" placeholder="最低频次" autocomplete="off"
                                   class="layui-input" style="width:100px">
                        </div>
                        <div class="layui-inline">
                            <input type="number" name="maxCount" id="maxCount" placeholder="最高频次" autocomplete="off"
                                   class="layui-input" style="width:100px">
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">地点：</label>
                            <div class="layui-input-inline">
                                <div class="xm-select-demo" id="typeSelect"></div>
                            </div>
                        </div>

                        <div class="layui-inline">

                            <select id="type" lay-filter="typeSelect">
                                <option value="">请选择类型</option>
                                <option value="0">普通</option>
                                <option value="1">疑似</option>
                                <option value="2">黑名单</option>
                            </select>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                            <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                            <button class="layui-btn" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                        </div>
                        <div class="layui-inline">
                        <span class="layui-form-mid layui-word-aux" style="margin-left: 5px;">
                                <span class="color-block red-block"></span>：黑名单
                            </span>
                            <span class="layui-form-mid layui-word-aux" style="margin-left: 5px;">
                                <span class="color-block orange-block"></span>：疑似人员
                            </span>
                        </div>
                    </div>

                </div>

            </blockquote>
            <table class="layui-hide" id="portrait-table" lay-filter="portrait-table"></table>

            <script type="text/html" id="topToolbar">

                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="export_layui">
                        <i class="layui-icon">&#xe601;</i>导出
                    </button>
                </div>
            </script>
        </div>
    </div>
</div>

</body>
<script>

    //定时刷新30s
    // setInterval(function () {
    //     window.location.reload();
    // },30000);

    var deptId = [[${deptId}]];
    console.log(deptId);


    layui.use(['form', 'laydate', 'table'], function () {
        var form = layui.form;
        var laydate = layui.laydate;


        var table = layui.table;
        form.render('select'); // 渲染选择框

        // 监听选择框的变化事件
        form.on('select(typeSelect)', function (data) {
            var type = data.value;
            // 在这里处理选择框的变化
        });
        // 渲染日期选择器
        laydate.render({
            elem: '#startDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            done: function (value, date) {
            }
        });

        laydate.render({
            elem: '#endDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            done: function (value, date) {
            }
        });


        // 获取今天的日期
        var today = new Date();
        var year = today.getFullYear();
        var month = ('0' + (today.getMonth() + 1)).slice(-2);
        var day = ('0' + today.getDate()).slice(-2);
        var startDateTime = year + '-' + month + '-' + day + ' 00:00:00';
        var endDateTime = year + '-' + month + '-' + day + ' 23:59:59';

        document.getElementById('startDate').value = startDateTime;
        document.getElementById('endDate').value = endDateTime;

        var tableIns = table.render({
            elem: '#portrait-table',
            height: 'full-50',
            toolbar: '#topToolbar',
            defaultToolbar: [
                ''
                // 'print','filter'
            ],
            url: ctx + '/portraitCollectionImageController/findRecordByCondition',
            cols: [
                [
                    {type: 'checkbox'},
                ]
            ],
            where: {  // 添加默认查询参数
                startDate: startDateTime,
                endDate: endDateTime
            }, request: {
                pageName: 'currentPage', // 页码的参数名称，默认：page
                limitName: 'pageSize' // 每页数据量的参数名，默认：limit
            },
            limit: 24,
            limits: [6, 12, 18, 24, 48], // 可选的每页显示条目数选项
            parseData: function (res) { // res 即为原始返回的数据
                console.log(res.data);
                return {
                    "code": res.code, // 解析接口状态
                    "msg": res.msg, // 解析提示文本
                    "count": res.data.total, // 解析数据长度
                    "totalCount": res.data.totalCount, // 解析数据长度
                    "data": res.data.list // 解析数据列表
                };
            },
            page: true,
            method: 'post', // 设置请求方法为 POST
            contentType: 'application/json', // 设置请求内容类型为 JSON
            done: function (res, curr, count) {
                if (res.data) {
                    $('.layui-form.layui-border-box.layui-table-view').removeAttr('style');
                    // 表格渲染完成后的回调函数
                    // 在这里进行表格内容的替换操作
                    $('.layui-table-box').empty().html(' <div class="layui-row">\n' +
                        '                <div class="layui-col-md12">\n' +
                        '                    <div class="card-container"></div>\n' +
                        '                </div>\n' +
                        '            </div>');
                    // 分页查询结果
                    var portraitList = res.data;

                    // 渲染卡片内容
                    renderCards(portraitList);
                }

            }
        });

        table.on('toolbar(portrait-table)', function(obj){
            switch(obj.event){
                case 'export_layui':
                    // 获取查询条件
                    var checkStatus = table.checkStatus(obj.config.id);
                    var selectedIds = checkStatus.data.map(item => item.code).join(',');
                    var startDate = $("#startDate").val();
                    var endDate = $("#endDate").val();
                    var minCount = $("#minCount").val();
                    var maxCount = $("#maxCount").val();
                    var type = $("#type").val();

                    const carModel = []
                    typeSelect.getValue().forEach(data => {
                        carModel.push(data.ID)
                    })


                    $.ajax({
                        url: ctx + "/portraitCollectionImageController/exportExcel",
                        type: 'POST',
                        data: {
                            startDate: startDate,
                            endDate: endDate,
                            minCount: minCount,
                            maxCount: maxCount,
                            type: type,
                            selectedIds: selectedIds,
                            deptIds: carModel
                        },
                        xhrFields: {
                            responseType: 'blob' // 处理二进制文件
                        },
                        success: function (data, status, xhr) {
                            // 创建 Blob 对象并下载
                            var blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            var url = window.URL.createObjectURL(blob);
                            var a = document.createElement('a'); // 创建一个链接元素
                            a.href = url;
                            a.download = "人像报表信息.xlsx"; // 使用默认文件名
                            a.style.display = 'none'; // 隐藏链接
                            document.body.appendChild(a);
                            a.click(); // 自动点击链接以下载文件
                            document.body.removeChild(a); // 移除链接
                            window.URL.revokeObjectURL(url); // 释放 URL 对象
                        },
                        error: function () {
                            // alert('导出失败，请重试！');

                            var errorMsg = '导出失败，请重试！';

                            layer.alert(errorMsg, {
                                icon: 2,
                                title: '导出错误'
                            });
                        }
                    });
                    break;
            }
        });

        //搜索及重置按钮
        $("#searchBtn").click(function () {

            const carModel = []
            typeSelect.getValue().forEach(data => {
                carModel.push(data.ID)
            })

            tableIns.where = {};

            table.reload('portrait-table', {
                where: {
                    startDate: $("#startDate").val(),
                    endDate: $("#endDate").val(),
                    minCount: $("#minCount").val(),
                    maxCount: $("#maxCount").val(),
                    type: $("#type").val(),
                    deptIds: carModel
                },

                page: {
                    curr: 1 //重新从第 1 页开始
                },
                method: 'post',
                contentType: 'application/json', // 设置请求的内容类型为 JSON
                done: function(res) {

                    // 数据加载完成后的回调
                    console.log('表格重载完成', res);
                    // this.where = {};
                    tableIns = this;

                    $('.layui-form.layui-border-box.layui-table-view').removeAttr('style');
                    // 表格渲染完成后的回调函数
                    // 在这里进行表格内容的替换操作
                    $('.layui-table-box').empty().html(' <div class="layui-row">\n' +
                        '                <div class="layui-col-md12">\n' +
                        '                    <div class="card-container"></div>\n' +
                        '                </div>\n' +
                        '            </div>');
                    // 分页查询结果
                    var portraitList = res.data;

                    // 渲染卡片内容
                    renderCards(portraitList);
                }
            }); //只重载数据
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#startDate").val(null);
            $("#endDate").val(null);
            $("#minCount").val(null);
            $("#maxCount").val(null);
            $("#type").val(null);
            form.render('select'); // 渲染选择框

            table.reload('portrait-table', {
                where: null,
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                method: 'post',
                contentType: 'application/json' // 设置请求的内容类型为 JSON
            }); //只重载数据
        })

        var typeSelect = xmSelect.render({
            el: '#typeSelect',
            radio: false,
            // clickClose: true,
            filterable: true,
            toolbar: {show: true},
            name: 'deptIds',
            layVerify: 'required',
            prop: {name: 'deptName', value: 'ID'},
            data: [],
            // 启用严格模式，确保不会有重复选中项
            strict: true,
            style: {
                paddingLeft: '0px',
                position: 'relative',
                width: '160px',
                height: '38px'
            },
            tree: {
                show: true,
                strict: false, //是否父子结构，父子结构父节点不会被选中
                indent: 30,//间距
                expandedKeys: [-1],
                clickCheck: true,
                clickExpand: true,//点击展开
            },

        });

        $.ajax({
            url: ctx + '/deptController/deptDetail',
            type: 'PUT',
            success: function (res) {

                // 1. 将ID统一转为字符串
                // var data = res.data.map(item => {
                //     return {
                //         deptName: item.deptName,
                //         ID: item.ID.toString() // 确保ID是字符串
                //     };
                // });

                // 3. 更新下拉框数据
                typeSelect.update({
                    data: res.data,
                });

                // 4. 设置初始值（确保deptId转为字符串）
                var initValue = deptId ? deptId.toString() : "";
                if (initValue) {
                    typeSelect.setValue([initValue]);
                }
            }
        });

    });

    // 渲染卡片内容
    function renderCards(portraitList) {
        var cardContainer = $('.card-container');
        var cardHtml = ' <div class="layui-none">无数据</div>';
        cardContainer.empty();

        var colorClass = "green";
        if (portraitList && portraitList.length > 0) {
            portraitList.forEach(function (item) {
                var id = item.code;
                var date = new Date(item.updateTime);
                var formattedDate = date.toLocaleDateString('zh-CN').substring(0, 10);
                console.log(item.type)
                switch (item.type) {
                    case 1:
                        colorClass = "orange";
                        break;
                    case 2:
                        colorClass = "red";
                        break;
                    default:
                        colorClass = "green";
                }

                var cardHtml = '<div class="layui-card" id="' + id + '">' +
                    '<div class="card-content">' +
                    '<div class="image-wrapper">' +
                    '<img src="' + imageBase + item.ossUrl + '" alt="Image" class="card-image" onerror="replaceWithErrorImage(this)">' +
                    '</div>' +
                    '<div class="text-wrapper ' + colorClass + '">' +
                    '<div class="left-column">' +
                    '<p class="card-label">频次：</p>' +
                    '<p class="card-label">总频次：</p>' +
                    '<p class="card-label">最近记录时间：</p>' +
                    '</div>' +
                    '<div class="right-column">' +
                    '<p class="card-text">' + item.count + '</p>' +
                    '<p class="card-text">' + item.totalCount + '</p>' +
                    '<p class="card-text">' + formattedDate + '</p>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>';

                // 给卡片添加点击事件
                $('body').on('click', "#" + id, function () {
                    showRecord(id);
                });

                cardContainer.append(cardHtml);
            });
        } else {
            $('.layui-form.layui-border-box.layui-table-view').css('height', "600px");

            cardContainer.append(cardHtml);
        }
    }

    //跳转到记录页面
    function showRecord(id) {
        var url = ctx + 'portraitCollectionRecordController/toRecordPage/' + id,
            title = '人像采集记录';
        top.layui.index.openTabsPage(url, title);
    }

</script>


</html>
