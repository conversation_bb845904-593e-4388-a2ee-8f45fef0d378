<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
>
	<head>
		<meta charset="UTF-8">
		<title>Insert title here</title>
		<div th:replace="Importfile::html"></div>
		<link rel="stylesheet" th:href="@{/admin/layui/css/admin.css}">
		<script th:src="@{/plugins/formSelects/xm-select.js}"></script>
	</head>
	<style>
		.layui-form-item .layui-input-inline {
			width: 280px;
		}
	</style>
	<body>
		<div class="layui-fluid">
			<div class="layui-card">
				<div class="layui-card-body">
					<fieldset class="layui-elem-field layui-field-title"
							  style="margin-top: 20px;">
						<legend>菜单信息</legend>
					</fieldset>

					<form id="modelAuthForm" lay-filter="modelAuthForm" class="layui-form">
						<div class="layui-form-item">
							<input name="id" id="id" type="hidden" th:value="${menu?.id}">
						</div>
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">上级菜单</label>
								<div class="layui-input-inline">
									<div class="xm-select" id="parentIdSelect">
									</div>
									<input type="hidden" th:value="${menu?.parentId}" id="parentId">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label"><span style="color:red">*</span>菜单名称</label>
								<div class="layui-input-inline">
									<input name="menuName" th:value="${menu?.menuName}" placeholder="请输入菜单名称"
										   type="text" class="layui-input" maxlength="50"
										   lay-verify="required|menuName">
								</div>
							</div>
						</div>

						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label"><span style="color: red">*</span>菜单url</label>
								<div class="layui-input-inline">
									<input type="hidden" id="areaValue" th:value="${menu?.areaName}"/>
									<input type="hidden" id="menuUrl" th:value="${menu?.menuUrl}"/>
									<div class="xm-select" id="areaName">
									</div>
									<div class="xm-select" id="controlValue">
									</div>
									<div class="xm-select" id="actionValue">
									</div>
								</div>
							</div>
							<div class="layui-inline">
                                <label class="layui-form-label"><span style="color:red;">*</span>动作列表</label>
								<div class="layui-input-inline">
									<div class="xm-select" id="actionList">
									</div>
								</div>
							</div>
						</div>

						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">菜单图标</label>
								<div class="layui-input-inline">
									<input type="text" id="iconPicker" lay-filter="iconPicker" class="hide"
										   th:value="${menu?.iconCssClass}">
									<input type="hidden" id="iconCssClass" name="iconCssClass"
										   th:value="${menu?.iconCssClass}">
								</div>
							</div>
						</div>

						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">是否系统级</label>
								<div class="layui-input-inline" th:if="${session.user != null &&session.user.isSys==1}">
									<input type="checkbox" th:attr="checked=${menu?.isSys == true ? true : false}"
										   name="isSys" id="isSys" lay-skin="switch" lay-text="是|否">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">是否显示</label>
								<div class="layui-input-inline">
									<input type="checkbox" th:attr="checked=${menu?.isShow == true ? true : false}"
										   name="isShow" id="isShow" lay-skin="switch" lay-text="显示|隐藏">
								</div>
							</div>
						</div>

						<div class="layui-form-item">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit="" lay-filter="subBtn">保存</button>
								<button class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</body>
	<script type="text/javascript">
		layui.config({
			base: ctx + 'plugins'
		}).extend({
			iconPicker: '/iconPicker/iconPicker'
		})
		layui.use(['iconPicker', 'form', 'layer'], function () {
			var iconPicker = layui.iconPicker,
					form = layui.form,
					layer = layui.layer,
					$ = layui.$;
			form.render();
			form.verify({
				menuName: function (value) {
					if (value.length > 20) {
						return "菜单名称不能超过20个字符";
					}
				}
			});
			form.on('submit(subBtn)', function (data) {
				var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time: 0});
				$.ajax({
					url: 'saveMenu',
					data: data.field,
					success: function (res) {
						if (res.stateType == '0') {
							layer.alert(res.stateMsg, {
										icon: 6, skin: 'layer-ext-moon', closeBtn: 0
									},
									function () {
										window.parent.changTabs(ctx + 'menuController/menuList', '', '菜单管理');
									});
						} else {
							layer.msg(res.stateMsg, {icon: 2});
						}
					},
					error: function () {
						layer.close(loading);
						layer.msg('操作失败', {icon: 1});
					}
				});
				return false;
			});




			iconPicker.render({
				// 选择器，推荐使用input
				elem: '#iconPicker',
				// 数据类型：fontClass/unicode，推荐使用fontClass
				type: 'fontClass',
				// 是否开启搜索：true/false
				search: false,
				// 是否开启分页
				page: true,
				// 每页显示数量，默认12
				limit: 12,
				// 点击回调
				click: function (data) {
					//console.log(data);
					var icon = 'layui-icon ' + data.icon
					$("#iconCssClass").val(icon);
				},
				/*  // 渲染成功后的回调
                 success: function(d) {
                 } */
			});
		});


		$(function () {
			$('#cancelBtn').click(function () {
				window.parent.changTabs(ctx + 'menuController/menuList', '', '菜单管理');
			})
		})

		function reloadXmselect() {
			//上级菜单
			jQuery.get(ctx + 'menuController/menuDialogJson?id=' + $("#parentId").val(), function (res) {
				dialog_menu.update({
					data: res.items[0].children,
				})
			});
			//动作列表
			jQuery.get(ctx + '/menuController/actionTree?id=' + $("#id").val(), function (res) {
				dialog_action.update({
					data: res.data,
				})
			});
		}

		jQuery(function () {
			reloadXmselect();
			reloadUrl();
		});
		//上级菜单 下拉框初始化
		var dialog_menu = xmSelect.render({
			el: '#parentIdSelect',
			filterable: true,
			name: 'parentId',
			tips: '请选择',
			// layVerify: 'required|uniqueDept',
			// layVerify: 'required',
			// layVerType: 'msg',
			model: {label: {type: 'block'}},
			template:function(item) {
				return '<p title="' + item.name + '">' + item.name + '</p>';
			},
			on: function (data) {
				//arr:  当前多选已选中的数据
				var arr = data.arr;
				//change, 此次选择变化的数据,数组
				var change = data.change;
				//isAdd, 此次操作是新增还是删除
				var isAdd = data.isAdd;
			},
			// cascader: {
			//     //是否显示级联模式
			//     show: true,
			//     //间距
			//     indent: 200,
			//     //是否严格遵守父子模式
			//     strict: true,
			// },
			// showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
			// tree //开启树结构
			radio: true,//单选多选
			tree: {
				show: true,
				strict: false, //是否父子结构，父子结构父节点不会被选中
				indent: 30,//间距
				expandedKeys: [-1],
				clickCheck: true,
				clickExpand: true,//点击展开
			},
			clickClose: true,//点击关闭
			autoRow: true,
			style: {
				paddingLeft: '10px',
				position: 'relative',
				width: '268px'
			},
			prop: {
				name: "menuName",
				value: "ID"
			},
			height: '200px',
			empty: '暂无数据',
			data: [],
			direction: 'auto',
		});
		//动作列表 下拉框初始化
		var dialog_action = xmSelect.render({
			el: '#actionList',
			filterable: true,
			name: 'actionList',
			tips: '请选择',
            max: 10,
            maxMethod(seles, item){
                layui.layer.msg(`最多只能选择10个动作`,{icon:2});
            },
			// layVerify: 'required|uniqueDept',
			layVerify: 'required',
			layVerType: 'msg',
            iconfont: {
                parent: 'hidden'
            },
            model: {label: {type: 'block',block: {
                        //最大显示数量, 0:不限制
                        showCount: 3,
                        //是否显示删除图标
                        showIcon: true,
                    }}},
			template:function(item) {
				return '<p title="' + item.name + '">' + item.name + '</p>';
			},
			on: function (data) {
				//arr:  当前多选已选中的数据
				var arr = data.arr;
				//change, 此次选择变化的数据,数组
				var change = data.change;
				//isAdd, 此次操作是新增还是删除
				var isAdd = data.isAdd;
				// if (isAdd){
				//
				//     var value = dialog_dept.getValue();
				//     console.log(value)
				// }

				// alert('已有: ' + arr.length + ' 变化: ' + change.length + ', 状态: ' + isAdd)
			},
			// cascader: {
			//     //是否显示级联模式
			//     show: true,
			//     //间距
			//     indent: 200,
			//     //是否严格遵守父子模式
			//     strict: true,
			// },
			// showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
			// tree //开启树结构
            toolbar: { //开启工具栏
                show: true,
                list: ['ALL', 'CLEAR'],
            },
			radio: false,//单选多选
			tree: {
				show: true,
				strict: true, //是否父子结构，父子结构父节点不会被选中
				indent: 30,//间距
				expandedKeys: [-1],
				clickCheck: true,
				clickExpand: true,//点击展开
			},
			clickClose: false,//点击关闭
			autoRow: true,
			style: {
				paddingLeft: '10px',
				position: 'relative',
				width: '268px'
			},
			prop: {
				name: "display",
				value: "controllerAction"
			},
			height: '200px',
			empty: '暂无数据',
			data: [],
			direction: 'auto',
		});

		function reloadUrl() {
			var str = $("#menuUrl").val().split('/');
			var controlValue = str[0];
			var actionValue = str[1];
			//菜单url渲染
			jQuery.get(ctx + "menuController/areaJson?areaName=" + $("#areaValue").val(), function (res) {
				dialog_areaName.update({
					data: res,
				})
			})

			//controller渲染
			jQuery.get(ctx + "menuController/controlJson?areaValue=" + $("#areaValue").val() + "&controlValue=" + controlValue, function (res) {
				dialog_controller.update({
					data: res,
				})
			})

			//action渲染
			jQuery.get(ctx + "menuController/actionJson?controlValue=" + controlValue + "&actionValue=" + actionValue, function (res) {
				dialog_action1.update({
					data: res,
				})
			})

		}
		//动作选择器
		var dialog_action1 = xmSelect.render({
			el: '#actionValue',
			filterable: true,
			name: 'actionValue_txt_val',
			tips: '请选择动作列表',
			// layVerify: 'required|uniqueDept',
			layVerify: 'required',
			layVerType: 'msg',
			model: {label: {type: 'block'}},
			template:function(item) {
				return '<p title="' + item.name + '">' + item.name + '</p>';
			},
			on: function (data) {
				//arr:  当前多选已选中的数据
				var arr = data.arr;
				//change, 此次选择变化的数据,数组
				var change = data.change;
				//isAdd, 此次操作是新增还是删除
				var isAdd = data.isAdd;
				// if (isAdd){
				//
				//     var value = dialog_dept.getValue();
				//     console.log(value)
				// }
			},
			// cascader: {
			//     //是否显示级联模式
			//     show: true,
			//     //间距
			//     indent: 200,
			//     //是否严格遵守父子模式
			//     strict: true,
			// },
			// showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
			// tree //开启树结构
			radio: true,//单选多选
			tree: {
				show: true,
				strict: false, //是否父子结构，父子结构父节点不会被选中
				indent: 30,//间距
				expandedKeys: [-1],
				clickCheck: true,
				clickExpand: true,//点击展开
			},
			clickClose: true,//点击关闭
			autoRow: true,
			style: {
				paddingLeft: '10px',
				position: 'relative',
				width: '268px'
			},
			prop: {
				name: "text",
				value: "value"
			},
			height: '200px',
			empty: '暂无数据',
			data: [],
			direction: 'auto',
		});
		//controller选择器
		var dialog_controller = xmSelect.render({
			el: '#controlValue',
			filterable: true,
			name: 'controlValue_txt_val',
			tips: '请选择控制器',
			// layVerify: 'required|uniqueDept',
			// layVerify: 'required',
			// layVerType: 'msg',
			model: {label: {type: 'block'}},
			template:function(item) {
				return '<p title="' + item.name + '">' + item.name + '</p>';
			},
			on: function (data) {
				//arr:  当前多选已选中的数据
				var arr = data.arr;
				//change, 此次选择变化的数据,数组
				var change = data.change;
				//isAdd, 此次操作是新增还是删除
				var isAdd = data.isAdd;
				if (isAdd){
					//action渲染
					jQuery.get(ctx + "menuController/actionJson?controlValue=" + arr[0].value , function (res) {
						dialog_action1.update({
							data: res,
						})
					})
				}else {
					dialog_action1.update({
						data: [],
					})
				}
			},
			// cascader: {
			//     //是否显示级联模式
			//     show: true,
			//     //间距
			//     indent: 200,
			//     //是否严格遵守父子模式
			//     strict: true,
			// },
			// showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
			// tree //开启树结构
			radio: true,//单选多选
			tree: {
				show: true,
				strict: false, //是否父子结构，父子结构父节点不会被选中
				indent: 30,//间距
				expandedKeys: [-1],
				clickCheck: true,
				clickExpand: true,//点击展开
			},
			clickClose: true,//点击关闭
			autoRow: true,
			style: {
				paddingLeft: '10px',
				position: 'relative',
				width: '268px'
			},
			prop: {
				name: "text",
				value: "value"
			},
			height: '200px',
			empty: '暂无数据',
			data: [],
			direction: 'auto',
		});
		//包名选择器
		var dialog_areaName = xmSelect.render({
			el: '#areaName',
			filterable: true,
			name: 'areaName',
			tips: '请选择区域',
			// layVerify: 'required|uniqueDept',
			// layVerify: 'required',
			// layVerType: 'msg',
			model: {label: {type: 'block'}},
			template:function(item) {
				return '<p title="' + item.name + '">' + item.name + '</p>';
			},
			on: function (data) {
				//arr:  当前多选已选中的数据
				var arr = data.arr;
				//change, 此次选择变化的数据,数组
				var change = data.change;
				//isAdd, 此次操作是新增还是删除
				var isAdd = data.isAdd;
				if (isAdd) {
					//controller渲染
					jQuery.get(ctx + "menuController/controlJson?areaValue=" + arr[0].value, function (res) {
						dialog_controller.update({
							data: res,
						})
					})
				}else {
					dialog_controller.update({
						data: [],
					})
				}
                dialog_action1.update({
                    data: [],
                })
			},
			// cascader: {
			//     //是否显示级联模式
			//     show: true,
			//     //间距
			//     indent: 200,
			//     //是否严格遵守父子模式
			//     strict: true,
			// },
			// showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
			// tree //开启树结构
			radio: true,//单选多选
			tree: {
				show: true,
				strict: false, //是否父子结构，父子结构父节点不会被选中
				indent: 30,//间距
				expandedKeys: [-1],
				clickCheck: true,
				clickExpand: true,//点击展开
			},
			clickClose: true,//点击关闭
			autoRow: true,
			style: {
				paddingLeft: '10px',
				position: 'relative',
				width: '268px'
			},
			prop: {
				name: "text",
				value: "value"
			},
			height: '200px',
			empty: '暂无数据',
			data: [],
			direction: 'auto',
		});

	</script>
	<script type="text/javascript">

	</script>
</html>