<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>安全日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}">
    <link>
    <script type="text/javascript" th:src="@{/admin/layui/layui.js}"></script>
    <link rel="stylesheet" th:href="@{/plugins/dtree/dtree.css}">
    <link rel="stylesheet" th:href="@{/plugins/dtree/font/dtreefont.css}">
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="search_form">
                <blockquote class="layui-elem-quote quoteBox" id="search">

                    <div class="layui-inline">
                        <label class="layui-form-label w-auto" style="width: unset;padding: 9px 15px 9px 0px;">操作者：</label>
                        <div class="layui-input-inline mr0">
                            <input id="username" name="username" class="layui-input" type="text" placeholder="请输入操作者">
                        </div>
                    </div>
                    <div class="layui-inline layui-form">
                        <label class="layui-form-label" >日志小类：</label>
                        <div class="layui-input-inline" style="width: 180px">
                            <select name="actionName" id="actionName" lay-verify="">
                                <option value="">全部</option>
                                <option th:each="safelist:${safelist}" th:value="${safelist.actionName }"
                                        th:text="${safelist.controDisplay}"></option>
                            </select>
                        </div>
                    </div>



                    <div class="layui-inline" >
                        <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                        <button class="layui-btn icon-btn " id="searchBtn"><i
                                class="layui-icon">&#xe615;</i>查询
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary " id="seachreset"><i
                                class="layui-icon">&#xe669;</i>重置
                        </button>

                    </div>

                </blockquote>
            </form>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md2">
                    <div style="margin-top: 10px;">
                        <ul id="dataTree" class="dtree" th:data-id="${deptId}"></ul>
                    </div>
                </div>
                <div class="layui-col-md10">
                    <table class="layui-hide" id="log-table" lay-filter="log-table"></table>
                </div>
            </div>
            <script type="text/html" id="test-table-toolbar-toolbarDemo">
                <button class="layui-btn  layui-btn-sm" lay-event="print"><i class="layui-icon">&#xe629;</i>报表</button>
            </script>
            <script type="text/html" id="test-table-toolbar-barDemo">
                <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
            </script>
        </div>
    </div>
</div>
<script>
    //回车搜索
    $("blockquote").on('keyup','input,select',function (e) {
        if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.config({
        base: ctx + '/plugins/soulTable/'
    }).extend({
        dtree: 'dtree',
        soulTable: 'soulTable'
    });
    layui.use(['table', 'layer', 'form', 'jquery', 'laydate', 'dtree'], function () {
        var admin = layui.admin;
        var $ = layui.jquery;
        var dtree = layui.dtree;
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var table = layui.table;
        table.render({
            elem: '#log-table'
            ,defaultToolbar: ['filter']
            ,height:'full-50'
            , url: ctx + 'safeLogController/safeLoginJson'
            , toolbar: '#test-table-toolbar-toolbarDemo'
            , title: '操作日志表'
            , cols: [
                [
                    {title: '编号', width: 50, type: 'id', hide: true}
                    , {field: 'fullName', width: 180, title: '操作者',align:'center',templet:function(data){
                        if (data.user == null || data.user.fullName == null) {
                            return "";
                        }
                        return data.user.fullName
                    }}
                    , {field: 'deptName', width: 180, title: '所属部门',align:'center',templet:function(data){
                        if (data.user == null || data.user.dept == null || data.user.dept.deptName == null) {
                            return '系统部门';
                        } else {
                            return data.user.dept.deptName;
                        }
                    }}
                    , {field: 'controDisplay', width: 180, title: '动作',align:'center'}
                    , {field: 'actionDisplay', width: 180, title: '动作描述',align:'center'}
                    , {field: 'recordTime', width: 180, title: '操作时间',align:'center'}
                    , {field: 'recordIp', width: 180, title: '来源',align:'center'}
                    // , {field: 'logType', title: '日志类型', width: 180, templet: '#verifyTpl',align:'center'},
                    ,{field: 'jkcs', width: 80, title: '操作',fixed: 'right', toolbar: '#test-table-toolbar-barDemo',align:'center'}
                ]
            ]
            , request: {
                pageName: 'page' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            ,limit:20
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true,
            done: function (res, page, count) {
                var that = this.elem.next();
                res.data.forEach(function (item, index) {
                    //console.log(item.jyws);
                    if (item.jyws == '1') {
                        var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']").css("background-color", "yellow");
                    }
                });
            }
        });
        //初始化树
        var DTreeNode = dtree.render({
            elem: "#dataTree",
            url: ctx + "deptController/depTreeJson", // 使用url加载
            dataStyle: "layuiStyle",  //使用layui风格的数据格式
            dataFormat: "list",  //配置data的风格为list
            response: {message: "msg", statusCode: 0},  //修改response中返回数据的定义
            checkbar: true,//开启复选框
            checkbarType: "no-all",
            initLevel: 2,
            line: true, // 有树线
            ficon: "2",  // 设定一级图标样式。0表示方形加减图标，7表示文件图标
            icon: "-1", // 不设定二级图标样式。
            skin: "laySimple"
        });
        // 绑定节点点击事件
        dtree.on("node(dataTree)", function (obj) {
            var username = $("#username").val();
            var actionName = $("#actionName").val();
            var logType = $("#logType").val();
            var nodeId = obj.param.nodeId;
            var nodeIds = [];
            DTreeNode.clickNodeCheckbar(nodeId);// 点击节点选中复选框
            var checkbarNodes = dtree.getCheckbarNodesParam("dataTree");
            for (var i = 0; i < checkbarNodes.length; i++) {
                nodeIds[i] = checkbarNodes[i].nodeId;
            }

            table.reload('log-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    nodeIds: JSON.stringify(nodeIds)
                }
            });
        });

        // 绑定用复选框点击事件
        dtree.on("chooseDone(dataTree)", function (obj) {
            var username = $("#username").val();
            var actionName = $("#actionName").val();
            var nodeId = obj.checkbarParams;
            var nodeIds = [];
            for (var i = 0; i < nodeId.length; i++) {
                nodeIds[i] = nodeId[i].nodeId;
            }
            table.reload('log-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    nodeIds: JSON.stringify(nodeIds)
                }
            });
        });

        //头工具栏事件
        table.on('toolbar(log-table)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'print':
                    layer.open({
                        type: 2,
                        title: "日志数据统计",
                        shadeClose: true,
                        area: ['700px', '500px'],
                        offset: [ //为了演示，随机坐标
                            0.1 * ($(window).height() - 400)
                        ]
                        , content: ctx + 'safeLogController/logSafeDialog'
                    });
                    break;
            };
        });
        //监听行工具事件
        table.on('tool(log-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.open({
                    type: 2,
                    title: "查看安全日志详情",
                    shadeClose: true,
                    area: ['99%', '90%'],
                    btn:"返回",
                    offset: "10px"
                    , content: ctx + 'logController/logDetial?ID=' + data.id
                });
            }
        });

        //搜索及重置按钮
        $("#searchBtn").click(function () {
            var username = $("#username").val();
            var actionName = $("#actionName").val();
            var logType = $("#logType").val();
            table.reload('log-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    username: username,
                    actionName: actionName,
                    logType: logType,
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
            return false;
        })
        //搜索及重置按钮
        $("#seachreset").click(function () {
            $("#actionDisplay").val("");
            $("#actionName").val("");
            $("#logType").val("");
            table.reload('log-table', {
                where: null
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            }); //只重载数据
        })

    });
</script>
<script type="text/html" id="verifyTpl">
    <!--    不知道有什么用  好像之前都没有这个字段-->
    <!--    {{#  if(d.logType === 0){ }}-->
    <!--    <span style="color: #98AFC7;">{{'核心功能操作'}}</span>-->
    <!--    {{#  }  else if(d.logType === 1){ }}-->
    <!--    <span style="color: #FFC125;">{{'常规操作'}}</span>-->
    <!--    {{#  }  else if(d.logType === 2){ }}-->
    <!--    <span style="color: #FF2400;">{{'非常规操作'}}</span>-->
    <!--    {{#  }  else if(d.logType === -1){ }}-->
    <!--    <span style="color: #52D017;">{{'非安全日志'}}</span>-->
    <!--    {{#  } }}-->
</script>
</body>
</html>