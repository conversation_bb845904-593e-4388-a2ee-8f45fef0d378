<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sttjzm" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4a3a11e1-9b3e-4ab4-8310-4aebc0e7d8ac">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="image1" class="java.lang.String">
		<parameterDescription><![CDATA[image1]]></parameterDescription>
	</parameter>
	<parameter name="image2" class="java.lang.String">
		<parameterDescription><![CDATA[image2]]></parameterDescription>
	</parameter>
	<parameter name="a" class="java.lang.String">
		<parameterDescription><![CDATA[a]]></parameterDescription>
	</parameter>
	<parameter name="b" class="java.lang.String">
		<parameterDescription><![CDATA[b]]></parameterDescription>
	</parameter>
	<parameter name="c" class="java.lang.String">
		<parameterDescription><![CDATA[c]]></parameterDescription>
	</parameter>
	<parameter name="d" class="java.lang.String">
		<parameterDescription><![CDATA[d]]></parameterDescription>
	</parameter>
	<parameter name="e" class="java.lang.String">
		<parameterDescription><![CDATA[e]]></parameterDescription>
	</parameter>
	<parameter name="f" class="java.lang.String">
		<parameterDescription><![CDATA[f]]></parameterDescription>
	</parameter>
	<parameter name="g" class="java.lang.String">
		<parameterDescription><![CDATA[g]]></parameterDescription>
	</parameter>
	<parameter name="h" class="java.lang.String">
		<parameterDescription><![CDATA[h]]></parameterDescription>
	</parameter>
	<parameter name="i" class="java.lang.String">
		<parameterDescription><![CDATA[i]]></parameterDescription>
	</parameter>
	<parameter name="j" class="java.lang.String">
		<parameterDescription><![CDATA[j]]></parameterDescription>
	</parameter>
	<parameter name="k" class="java.lang.String">
		<parameterDescription><![CDATA[k]]></parameterDescription>
	</parameter>
	<parameter name="l" class="java.lang.String">
		<parameterDescription><![CDATA[l]]></parameterDescription>
	</parameter>
	<parameter name="m" class="java.lang.String">
		<parameterDescription><![CDATA[m]]></parameterDescription>
	</parameter>
	<parameter name="n" class="java.lang.String">
		<parameterDescription><![CDATA[n]]></parameterDescription>
	</parameter>
	<parameter name="o" class="java.lang.String">
		<parameterDescription><![CDATA[o]]></parameterDescription>
	</parameter>
	<parameter name="p" class="java.lang.String">
		<parameterDescription><![CDATA[p]]></parameterDescription>
	</parameter>
	<parameter name="q" class="java.lang.String">
		<parameterDescription><![CDATA[q]]></parameterDescription>
	</parameter>
	<parameter name="r" class="java.lang.String">
		<parameterDescription><![CDATA[r]]></parameterDescription>
	</parameter>
	<parameter name="title" class="java.lang.String"/>
	<parameter name="qzzp" class="java.lang.String"/>
	<parameter name="gzzp" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="xb" class="java.lang.String">
		<fieldDescription><![CDATA[xb]]></fieldDescription>
	</field>
	<field name="xm" class="java.lang.String">
		<fieldDescription><![CDATA[xm]]></fieldDescription>
	</field>
	<field name="csrq" class="java.lang.String">
		<fieldDescription><![CDATA[csrq]]></fieldDescription>
	</field>
	<field name="sjhm" class="java.lang.String">
		<fieldDescription><![CDATA[sjhm]]></fieldDescription>
	</field>
	<field name="zjcx" class="java.lang.String">
		<fieldDescription><![CDATA[zjcx]]></fieldDescription>
	</field>
	<field name="sg" class="java.lang.Integer">
		<fieldDescription><![CDATA[sg]]></fieldDescription>
	</field>
	<field name="bsl" class="java.lang.Integer">
		<fieldDescription><![CDATA[bsl]]></fieldDescription>
	</field>
	<field name="zyjz" class="java.lang.Integer">
		<fieldDescription><![CDATA[zyjz]]></fieldDescription>
	</field>
	<field name="zysl" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[zysl]]></fieldDescription>
	</field>
	<field name="yyjz" class="java.lang.Integer">
		<fieldDescription><![CDATA[yyjz]]></fieldDescription>
	</field>
	<field name="yysl" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[yysl]]></fieldDescription>
	</field>
	<field name="tlztq" class="java.lang.Integer">
		<fieldDescription><![CDATA[tlztq]]></fieldDescription>
	</field>
	<field name="tlze" class="java.lang.Integer">
		<fieldDescription><![CDATA[tlze]]></fieldDescription>
	</field>
	<field name="tlye" class="java.lang.Integer">
		<fieldDescription><![CDATA[tlye]]></fieldDescription>
	</field>
	<field name="qgjbydza" class="java.lang.Integer">
		<fieldDescription><![CDATA[qgjbydza]]></fieldDescription>
	</field>
	<field name="zsz" class="java.lang.Integer">
		<fieldDescription><![CDATA[zsz]]></fieldDescription>
	</field>
	<field name="ysz" class="java.lang.Integer">
		<fieldDescription><![CDATA[ysz]]></fieldDescription>
	</field>
	<field name="zxz" class="java.lang.Integer">
		<fieldDescription><![CDATA[zxz]]></fieldDescription>
	</field>
	<field name="yxz" class="java.lang.Integer">
		<fieldDescription><![CDATA[yxz]]></fieldDescription>
	</field>
	<field name="zzzl" class="java.lang.Integer">
		<fieldDescription><![CDATA[zzzl]]></fieldDescription>
	</field>
	<field name="zjyl" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[zjyl]]></fieldDescription>
	</field>
	<field name="yjyl" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[yjyl]]></fieldDescription>
	</field>
	<field name="sfzdz" class="java.lang.String">
		<fieldDescription><![CDATA[sfzdz]]></fieldDescription>
	</field>
	<field name="a" class="java.lang.String">
		<fieldDescription><![CDATA[a]]></fieldDescription>
	</field>
	<field name="b" class="java.lang.String">
		<fieldDescription><![CDATA[b]]></fieldDescription>
	</field>
	<field name="c" class="java.lang.String">
		<fieldDescription><![CDATA[c]]></fieldDescription>
	</field>
	<field name="d" class="java.lang.String">
		<fieldDescription><![CDATA[d]]></fieldDescription>
	</field>
	<field name="e" class="java.lang.String">
		<fieldDescription><![CDATA[e]]></fieldDescription>
	</field>
	<field name="f" class="java.lang.String">
		<fieldDescription><![CDATA[f]]></fieldDescription>
	</field>
	<field name="g" class="java.lang.String">
		<fieldDescription><![CDATA[g]]></fieldDescription>
	</field>
	<field name="h" class="java.lang.String">
		<fieldDescription><![CDATA[h]]></fieldDescription>
	</field>
	<field name="i" class="java.lang.String">
		<fieldDescription><![CDATA[i]]></fieldDescription>
	</field>
	<field name="j" class="java.lang.String">
		<fieldDescription><![CDATA[j]]></fieldDescription>
	</field>
	<field name="k" class="java.lang.String">
		<fieldDescription><![CDATA[k]]></fieldDescription>
	</field>
	<field name="l" class="java.lang.String">
		<fieldDescription><![CDATA[l]]></fieldDescription>
	</field>
	<field name="m" class="java.lang.String">
		<fieldDescription><![CDATA[m]]></fieldDescription>
	</field>
	<field name="n" class="java.lang.String">
		<fieldDescription><![CDATA[n]]></fieldDescription>
	</field>
	<field name="o" class="java.lang.String">
		<fieldDescription><![CDATA[o]]></fieldDescription>
	</field>
	<field name="p" class="java.lang.String">
		<fieldDescription><![CDATA[p]]></fieldDescription>
	</field>
	<field name="q" class="java.lang.String">
		<fieldDescription><![CDATA[q]]></fieldDescription>
	</field>
	<field name="r" class="java.lang.String">
		<fieldDescription><![CDATA[r]]></fieldDescription>
	</field>
	<field name="xzb" class="java.lang.Integer">
		<fieldDescription><![CDATA[xzb]]></fieldDescription>
	</field>
	<field name="dx" class="java.lang.Integer">
		<fieldDescription><![CDATA[dx]]></fieldDescription>
	</field>
	<field name="yb" class="java.lang.Integer">
		<fieldDescription><![CDATA[yb]]></fieldDescription>
	</field>
	<field name="zcmb" class="java.lang.Integer">
		<fieldDescription><![CDATA[zcmb]]></fieldDescription>
	</field>
	<field name="mnrsz" class="java.lang.Integer">
		<fieldDescription><![CDATA[mnrsz]]></fieldDescription>
	</field>
	<field name="xy" class="java.lang.Integer">
		<fieldDescription><![CDATA[xy]]></fieldDescription>
	</field>
	<field name="jsb" class="java.lang.Integer">
		<fieldDescription><![CDATA[jsb]]></fieldDescription>
	</field>
	<field name="cd" class="java.lang.Integer">
		<fieldDescription><![CDATA[cd]]></fieldDescription>
	</field>
	<field name="zt" class="java.lang.Integer">
		<fieldDescription><![CDATA[zt]]></fieldDescription>
	</field>
	<field name="yw" class="java.lang.Integer">
		<fieldDescription><![CDATA[yw]]></fieldDescription>
	</field>
	<field name="xczp" class="java.lang.String">
		<fieldDescription><![CDATA[xczp]]></fieldDescription>
	</field>
	<field name="jb" class="java.lang.Integer">
		<fieldDescription><![CDATA[jb]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="802" splitType="Stretch">
			<image hAlign="Center" isLazy="true">
				<reportElement stretchType="RelativeToBandHeight" x="359" y="242" width="182" height="198" uuid="35ea637f-3374-483d-a4ad-45ef22dca8f7"/>
				<imageExpression><![CDATA["C:/image/gzzp.png"]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="219" y="720" width="160" height="70" uuid="e815f60d-56ef-435b-a44c-b271c3b0015b"/>
				<imageExpression><![CDATA["C:/image/qzzp.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="12" y="46" width="530" height="694" uuid="1dd20147-a4a5-4e7a-84ea-4fd567ccfc6f"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
			</staticText>
			<staticText>
				<reportElement x="12" y="46" width="58" height="564" uuid="aaa17339-5e42-43c8-ab45-a9a7eb090de8"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="45" width="472" height="60" uuid="c11e9b5c-74d5-4826-8208-0bb41e105b79"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="45" width="472" height="30" uuid="c94e5142-a20d-4630-b987-c6a21192e4d5"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="45" width="219" height="30" uuid="a777dd99-506f-4199-922f-eb3683d97177"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="75" width="219" height="30" uuid="5bd52bef-b684-4c97-a359-9eb3f3bfadaf"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="45" width="40" height="30" uuid="0ee6f1ba-8d09-4eba-827d-0585be3e9bf8"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[姓名]]></text>
			</staticText>
			<staticText>
				<reportElement x="198" y="45" width="40" height="30" uuid="3337f835-8b21-41bc-a070-1a743f3f13b5"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[性别]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="45" width="55" height="30" uuid="d27b579e-b3ff-43c2-b50d-77155e745367"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[出生日期]]></text>
			</staticText>
			<staticText>
				<reportElement x="443" y="45" width="42" height="30" uuid="46939363-68cd-47b2-8b8a-4a121ef16705"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[国籍]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="75" width="40" height="30" uuid="65dfa788-161d-4764-a633-c8c3aefa5b34"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[身份证
明名称]]></text>
			</staticText>
			<staticText>
				<reportElement x="238" y="75" width="50" height="30" uuid="28309eec-085f-4dfc-a7ee-6bd6ebb4ad4b"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[号码]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="106" width="387" height="60" uuid="d378dc5a-6e2b-4b77-9ae2-a06baefecb2d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="69" y="105" width="388" height="30" uuid="589e8d9a-e278-4c47-9619-35ebb386d3f9"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="457" y="105" width="85" height="204" uuid="46c03126-a444-4d91-b59f-60f5d11e59fa"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="168" width="387" height="141" uuid="fb86515e-5371-4b3c-b0ff-069b13e88408"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="457" y="310" width="85" height="130" uuid="e83eef3b-df07-4ae8-b21b-d053e11d7a5f"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="312" width="387" height="128" uuid="260e2df7-81c2-4cd0-9397-0d26c8f11106"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="71" y="311" width="386" height="64" uuid="6e5e36a9-9cb5-4564-8e48-cba7fc94474c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="440" width="472" height="64" uuid="37075dd9-ec57-4938-8e09-3aa1b766ee4c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="504" width="472" height="106" uuid="bd1d2410-c042-440d-bf5f-0dfaf6a5a840"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="13" y="610" width="529" height="50" uuid="5fa045c2-6576-4f06-b05f-c7f1ae361be4"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="105" width="90" height="30" uuid="064ec2ff-7b75-452b-ab63-c25c3cf536e9"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[申 请/已 具 有 的
准 驾 车 型 代 号]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="135" width="40" height="30" uuid="184da1b2-fbb3-47e2-9dd1-8c844711ff28"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[邮寄地址]]></text>
			</staticText>
			<staticText>
				<reportElement x="289" y="105" width="56" height="30" uuid="fde8936f-5a72-41cc-955f-09955dbfb358"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[档案编号]]></text>
			</staticText>
			<staticText>
				<reportElement x="289" y="135" width="56" height="30" uuid="c95c6f77-2ff0-4087-88ff-8df524a05737"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[联系电话]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="167" width="387" height="20" uuid="4b7f6ad0-5c3c-4a5b-a58e-c9c8fd53aec5"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[本人如实申告]]></text>
			</staticText>
			<staticText>
				<reportElement x="71" y="311" width="100" height="64" uuid="28986bac-aedf-496c-9f4a-6aca503a129c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[身高(cm)]]></text>
			</staticText>
			<staticText>
				<reportElement x="246" y="311" width="79" height="64" uuid="89bd3761-1fad-48a7-85fe-5d6966eb1257"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[辨色力]]></text>
			</staticText>
			<staticText>
				<reportElement x="71" y="375" width="100" height="64" uuid="113dc891-182f-40e5-844d-519d8d68c2fd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[视  力]]></text>
			</staticText>
			<staticText>
				<reportElement x="246" y="375" width="79" height="64" uuid="242b13af-4dd9-4254-833e-f353f38f1e76"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[是否矫正]]></text>
			</staticText>
			<staticText>
				<reportElement x="171" y="375" width="75" height="32" uuid="fa735217-d76d-4604-94e7-cde568946758"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ 左眼]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="375" width="132" height="32" uuid="1adf8886-9a0e-4b39-bec1-c54b1f108179"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="71" y="440" width="100" height="64" uuid="7f2d96a6-322c-4f45-aa38-63ab730f1439"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="71" y="440" width="29" height="64" uuid="f9b565be-74af-46df-9f26-e1917cc48933"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[听
力]]></text>
			</staticText>
			<staticText>
				<reportElement x="246" y="440" width="79" height="64" uuid="7e46f8b4-98d0-4b78-8cb3-46bc672324ca"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[躯干和颈部]]></text>
			</staticText>
			<staticText>
				<reportElement x="171" y="440" width="75" height="32" uuid="ec1052ae-fab0-4035-b36f-f74d5aac2223"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ 左耳]]></text>
			</staticText>
			<staticText>
				<reportElement x="71" y="504" width="29" height="106" uuid="352b7795-3872-4187-b807-1bdbf642f808"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[上
肢]]></text>
			</staticText>
			<staticText>
				<reportElement x="246" y="504" width="79" height="106" uuid="41ccf93a-d668-4566-bd23-430fd94f0d6a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[下肢]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="504" width="217" height="32" uuid="ca937849-2ca8-453c-a124-f59fc5bf2569"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="537" width="217" height="32" uuid="e74b1807-b86b-4d5d-8816-0220e45bce1a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="504" width="55" height="32" uuid="cc676966-3014-406b-9c10-5dad8bf1ddb0"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[左下肢]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="537" width="55" height="32" uuid="03e7ee17-b30f-4f2e-9c5a-b8b1e337b23e"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[右下肢]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="610" width="88" height="50" uuid="70ffe50e-905f-4434-8f98-bcbe39dbfd7e"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[申请方式]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="660" width="88" height="80" uuid="0bf3b38c-43e8-4e52-a8a9-f3204c3669f1"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[委托代理人信息]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="661" width="442" height="39" uuid="0cb25064-ea85-40e2-8e64-0a4de30103cd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="101" y="660" width="70" height="40" uuid="80b91f9a-40c9-4e03-abff-fe5fac7a4120"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[姓名]]></text>
			</staticText>
			<staticText>
				<reportElement x="101" y="700" width="70" height="40" uuid="d332ef64-2336-4376-9bd0-f61dca93504c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[联系地址]]></text>
			</staticText>
			<staticText>
				<reportElement x="242" y="661" width="52" height="38" uuid="247a07c7-7f9f-4638-810a-491c2ca49dc7"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[身份证
明名称]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="661" width="40" height="38" uuid="38fdc7f9-b615-4959-8e15-fecdc62c3caf"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[号码]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="701" width="40" height="38" uuid="816890bf-9792-4a95-9571-d77515d4770a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[电话]]></text>
			</staticText>
			<staticText>
				<reportElement x="527" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="513" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="499" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="485" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="471" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="457" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="443" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="429" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="415" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="387" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="373" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="359" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="345" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="331" y="75" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="317" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="303" y="75" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="289" y="76" width="14" height="30" uuid="3a979d6e-3614-475b-8472-da77630727bd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="46" width="58" height="263" uuid="0699940e-a071-4df0-a3e2-8388f50b1c32"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="46" width="29" height="263" uuid="c4b6640a-3616-4c20-acf8-6caf546118b1"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[申
请
人
填
报
事
项]]></text>
			</staticText>
			<staticText>
				<reportElement x="42" y="46" width="29" height="120" uuid="1c68dd3c-7469-4164-a8c1-059f952e2cf9"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[申
请
人
填
报
事
项]]></text>
			</staticText>
			<staticText>
				<reportElement x="42" y="166" width="29" height="143" uuid="d2cce766-c6c2-4be7-834b-c4b3c1caee7e"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[申
告
事
项]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="187" width="387" height="122" uuid="2e1e360f-5283-4a52-8743-919b9a4d64e4"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="309" width="58" height="301" uuid="e73f6bc4-583f-448c-8e64-1b46c077ffd3"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[医
疗
机
构
填
写
事
项]]></text>
			</staticText>
			<staticText>
				<reportElement x="485" y="45" width="57" height="30" uuid="38b998a3-71db-4b14-8447-7c14cab18672"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[中国]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="75" width="128" height="30" uuid="0f27e1cd-08c0-44cd-9a47-30a676515a1a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[居民身份证]]></text>
			</staticText>
			<staticText>
				<reportElement x="171" y="407" width="75" height="32" uuid="7f801c30-5cad-4804-8f46-9e58b7a32484"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ 右眼]]></text>
			</staticText>
			<staticText>
				<reportElement x="171" y="472" width="76" height="32" uuid="1137bf1a-4664-4eed-aa6e-1f12cb192ba7"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ 右耳]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="151" y="173" width="15" height="10" uuid="8e932558-0657-4f4f-9c3a-9d8e95b9d975">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{jb}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="151" y="173" width="15" height="10" uuid="8e932558-0657-4f4f-9c3a-9d8e95b9d975">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{jb}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="169" y="167" width="36" height="20" uuid="3c423f23-2c1e-4341-9d2e-e59aa195fd9c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[具有]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="219" y="173" width="15" height="10" uuid="2cf5ae60-b8fa-48ac-9ad1-7d394c788044">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{jb}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="219" y="173" width="15" height="10" uuid="2cf5ae60-b8fa-48ac-9ad1-7d394c788044">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{jb}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="237" y="167" width="36" height="20" uuid="bd4c0c4f-469d-40f2-9fd1-deb95797d5fc"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[不具有]]></text>
			</staticText>
			<staticText>
				<reportElement x="301" y="167" width="46" height="20" uuid="8f442b8b-0b6c-4449-943a-0e4e5328d967"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[下列疾病]]></text>
			</staticText>
			<image hAlign="Center" isLazy="true">
				<reportElement stretchType="RelativeToBandHeight" x="459" y="160" width="82" height="82" uuid="b96533e4-29a3-4636-991a-d9e63077f4d7"/>
				<imageExpression><![CDATA[$F{xczp}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="13" y="740" width="87" height="40" uuid="1813cd58-db6f-4aef-bb3b-2a343292c322"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[申请人签字：]]></text>
			</staticText>
			<staticText>
				<reportElement x="183" y="740" width="67" height="40" uuid="5fb19b71-2de8-4834-94ad-d0c873e311e5"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[医生签字：]]></text>
			</staticText>
			<staticText>
				<reportElement x="358" y="740" width="87" height="40" uuid="1f293ab8-90b4-4b3f-9047-09ffc9b6188a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[代理人签字：]]></text>
			</staticText>
			<textField>
				<reportElement x="116" y="14" width="293" height="30" uuid="ab51d3a4-f096-4620-bfca-d7a1089e7d7d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="22" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{title}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="45" width="88" height="30" uuid="dca4650b-a930-4eb4-a3fe-f023ef4e6317"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{xm}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="46" width="50" height="29" uuid="e0b24f4f-0e9c-4165-b1b1-46efe16d9953"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{xb}.equals("1"))?"男":"女"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="345" y="44" width="98" height="30" uuid="ced984f5-7ff3-4e59-98d2-1b0352761475"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{csrq}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="289" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{a}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="303" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{b}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="317" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{c}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="331" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{d}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="345" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{e}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="359" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{f}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="373" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{g}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="387" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{h}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="401" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{i}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="415" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{j}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="429" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{k}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="443" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{l}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="457" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{m}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="471" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{n}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="485" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{o}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="499" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{p}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="513" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{q}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="527" y="76" width="14" height="30" uuid="b3b3223e-69d7-4357-b14d-fc2d2fd06bc1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{r}]]></textFieldExpression>
			</textField>
			<image isLazy="true">
				<reportElement x="74" y="196" width="15" height="10" uuid="eb5ee3cc-af99-425e-8236-6c5d9d87b4ad">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{xzb}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="74" y="196" width="15" height="10" uuid="eb5ee3cc-af99-425e-8236-6c5d9d87b4ad">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{xzb}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="92" y="190" width="59" height="20" uuid="868194a1-2e5b-4259-8aa6-a92fb72469cd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[器质性心脏病]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="171" y="196" width="15" height="10" uuid="9088bd92-396c-4a77-824a-e396027b1fa1">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{dx}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="171" y="196" width="15" height="10" uuid="9088bd92-396c-4a77-824a-e396027b1fa1">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{dx}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="189" y="190" width="59" height="20" uuid="cf24d3cd-cab1-41ac-951e-b49e3600c209"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[癫 痫]]></text>
			</staticText>
			<staticText>
				<reportElement x="286" y="190" width="59" height="20" uuid="68aa492b-e309-408a-98e2-2ac1e7c9d06a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[美尼尔氏症]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="268" y="196" width="15" height="10" uuid="803e54fc-a4be-4103-9bb2-004502a78581">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{mnrsz}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="268" y="196" width="15" height="10" uuid="803e54fc-a4be-4103-9bb2-004502a78581">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{mnrsz}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="379" y="190" width="59" height="20" uuid="d1732a43-8f35-4703-93fb-5e88eb8e4534"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[眩 晕]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="361" y="196" width="15" height="10" uuid="565d933e-d4d6-49a6-9809-737c89460e33">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{xy}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="361" y="196" width="15" height="10" uuid="565d933e-d4d6-49a6-9809-737c89460e33">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{xy}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="74" y="215" width="15" height="10" uuid="1cb64c2e-dbdb-48c3-bdaa-965cac722fce">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yb}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="74" y="215" width="15" height="10" uuid="1cb64c2e-dbdb-48c3-bdaa-965cac722fce">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yb}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="92" y="209" width="59" height="20" uuid="f0caf2d2-71f1-4b86-a8b5-2354713a3ff7"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[癔 病]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="171" y="215" width="15" height="10" uuid="83e05963-3a94-48c1-9727-e9ed93847ad0">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zcmb}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="171" y="215" width="15" height="10" uuid="83e05963-3a94-48c1-9727-e9ed93847ad0">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zcmb}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="189" y="209" width="59" height="20" uuid="dcc7a8a0-658b-4f26-b130-955d6f325288"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[震颤麻痹]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="268" y="215" width="15" height="10" uuid="e3091535-2b47-4810-aa96-6a4086d3e010">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{jsb}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="268" y="215" width="15" height="10" uuid="e3091535-2b47-4810-aa96-6a4086d3e010">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{jsb}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="286" y="209" width="59" height="20" uuid="aab7491e-a867-49aa-a358-ca971e66f542"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[精神病]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="361" y="215" width="15" height="10" uuid="2dd7487a-d475-4423-bf2e-0cd91f21f980">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{cd}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="361" y="215" width="15" height="10" uuid="2dd7487a-d475-4423-bf2e-0cd91f21f980">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{cd}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="379" y="209" width="59" height="20" uuid="777d5a41-3910-42e9-897c-5ecb55c8ce15"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[痴 呆]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="74" y="234" width="15" height="10" uuid="7cefa88f-5dd0-406d-b4f8-88a67062c583">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zt}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="74" y="234" width="15" height="10" uuid="7cefa88f-5dd0-406d-b4f8-88a67062c583">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zt}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="92" y="228" width="346" height="20" uuid="4d6d13d5-9174-4cf1-a575-ac49f992ad83"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[影响肢体活动的神经系统疾病等妨碍安全驾驶疾病]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="74" y="253" width="15" height="10" uuid="bb11feb6-1d79-45f2-bba8-f4a04c40f4ca">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yw}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="74" y="253" width="15" height="10" uuid="bb11feb6-1d79-45f2-bba8-f4a04c40f4ca">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yw}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="92" y="247" width="365" height="32" uuid="7f0f738a-3f65-4e7e-ae6b-400cd0148d15"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[三年内有吸食、注射毒品行为或者解除强制隔离戒毒措施未满三年，或者长期服用依赖性精神药品成瘾尚未戒除

]]></text>
			</staticText>
			<staticText>
				<reportElement x="71" y="285" width="386" height="20" uuid="9e12bee0-a774-4302-88c0-c31a1540d2ae"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[上述申告为本人真实情况和真实意思表示，如果不属实本人自愿承担相应的法律责任。]]></text>
			</staticText>
			<textField>
				<reportElement x="171" y="312" width="75" height="63" uuid="bc64f3bf-a92f-415b-91cc-5e8b6227b23f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{sg}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="198" y="377" width="48" height="30" uuid="af4524b9-24e1-42f3-bde2-2aa60dd9d579"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{zysl}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="198" y="408" width="46" height="30" uuid="e8ae6976-6360-4999-8e83-b392a2041184"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{yysl}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="189" y="440" width="57" height="32" uuid="518acdff-2c79-4204-ad94-737b34660dc4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{tlze}==1)?"正常":"不正常"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="189" y="472" width="57" height="32" uuid="128c6a7b-4fb2-4258-92ff-a7adf55d8fe0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{tlye}==1)?"正常":"不正常"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="100" y="505" width="71" height="32" uuid="e3e504a4-8059-46a2-9be5-696fae4e2e35"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[左上肢]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="537" width="71" height="74" uuid="52a8297b-87c3-4006-8756-dd12e5667960"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[右上肢]]></text>
			</staticText>
			<textField>
				<reportElement x="171" y="505" width="75" height="32" uuid="23ece763-e925-48b1-bc24-4a6f7f3a605c"/>
				<box>
					<bottomPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{zsz}==1)?"正常":"不正常"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="171" y="537" width="73" height="72" uuid="edcce05d-5ab5-473d-8f60-5e64701c73cb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{ysz}==1)?"正常":"不正常"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="504" width="161" height="32" uuid="b8e87e2f-60e1-4053-afb3-31ebe2fe4743"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{zxz}==1)?"正常":"不正常"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="536" width="161" height="33" uuid="97cdb6e7-ac6d-418c-90a2-c56b3845f8c7"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{yxz}==1)?"正常":"不正常"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="160" y="105" width="129" height="30" uuid="f4a141c8-9716-477c-bd90-29440e4dae25"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{zjcx}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="345" y="136" width="112" height="29" uuid="8ae5f36f-ae67-46d0-83cc-ed58270a2014"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{sjhm}]]></textFieldExpression>
			</textField>
			<image isLazy="true">
				<reportElement x="338" y="387" width="15" height="10" uuid="11f877f6-7379-481d-9d96-dba6b34ca78d">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zyjz}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="338" y="387" width="15" height="10" uuid="11f877f6-7379-481d-9d96-dba6b34ca78d">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zyjz}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="338" y="387" width="15" height="10" uuid="11f877f6-7379-481d-9d96-dba6b34ca78d">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zyjz}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="356" y="381" width="28" height="20" uuid="4c2ff7cd-615e-4cef-9676-98ca96cf8fde"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[是]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="395" y="387" width="15" height="10" uuid="669b5940-a97c-4a8e-a501-0f500b89421e">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zyjz}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="395" y="387" width="15" height="10" uuid="669b5940-a97c-4a8e-a501-0f500b89421e">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zyjz}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="395" y="387" width="15" height="10" uuid="669b5940-a97c-4a8e-a501-0f500b89421e">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zyjz}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="413" y="381" width="28" height="20" uuid="1dde6b32-7d74-4658-92b7-598a9cc9309f"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[否]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="338" y="419" width="15" height="10" uuid="4c34d90b-b2ca-465d-b271-b4eafe9db855">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yyjz}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="338" y="419" width="15" height="10" uuid="4c34d90b-b2ca-465d-b271-b4eafe9db855">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yyjz}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="338" y="419" width="15" height="10" uuid="4c34d90b-b2ca-465d-b271-b4eafe9db855">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yyjz}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="356" y="413" width="28" height="20" uuid="fda72eb0-8340-42c0-be02-f385a2660400"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[是]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="395" y="419" width="15" height="10" uuid="a0a5a7c8-4b5a-49f5-9369-5d3a88e51f78">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yyjz}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="395" y="419" width="15" height="10" uuid="a0a5a7c8-4b5a-49f5-9369-5d3a88e51f78">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yyjz}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="395" y="419" width="15" height="10" uuid="a0a5a7c8-4b5a-49f5-9369-5d3a88e51f78">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{yyjz}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="413" y="413" width="28" height="20" uuid="06675255-72f2-4eea-9c21-5af054696dcc"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[否]]></text>
			</staticText>
			<staticText>
				<reportElement x="330" y="321" width="124" height="20" uuid="b1ccbcc1-a09c-4313-9121-031bd7876026"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[红 绿 色 盲]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="340" y="350" width="15" height="10" uuid="853105df-fb02-438f-9554-c01cc07fd07a">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{bsl}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="340" y="350" width="15" height="10" uuid="853105df-fb02-438f-9554-c01cc07fd07a">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{bsl}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="340" y="350" width="15" height="10" uuid="853105df-fb02-438f-9554-c01cc07fd07a">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{bsl}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="358" y="344" width="28" height="20" uuid="86093cb0-d5d1-4e95-8679-c9332617cd92"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[有]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="397" y="350" width="15" height="10" uuid="4ec2290e-1798-4d0a-acc5-36494db497ff">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{bsl}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="397" y="350" width="15" height="10" uuid="4ec2290e-1798-4d0a-acc5-36494db497ff">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{bsl}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="397" y="350" width="15" height="10" uuid="4ec2290e-1798-4d0a-acc5-36494db497ff">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{bsl}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="415" y="344" width="28" height="20" uuid="97e231b0-1221-4571-a647-2170b99a797f"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[无]]></text>
			</staticText>
			<staticText>
				<reportElement x="373" y="450" width="124" height="20" uuid="6cf89034-277f-421d-b04e-af428be5a202"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[运 动 功 能 障 碍]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="383" y="479" width="15" height="10" uuid="0bad2750-ccff-46a7-b029-2d5e032848ac">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{qgjbydza}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="383" y="479" width="15" height="10" uuid="0bad2750-ccff-46a7-b029-2d5e032848ac">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{qgjbydza}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="383" y="479" width="15" height="10" uuid="0bad2750-ccff-46a7-b029-2d5e032848ac">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{qgjbydza}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="401" y="473" width="28" height="20" uuid="9e2d8845-8e03-4a80-94b7-49c6ac81ee00"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[有]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="440" y="479" width="15" height="10" uuid="94aabf75-9d2e-45f8-a737-b855f43af918">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{qgjbydza}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="440" y="479" width="15" height="10" uuid="94aabf75-9d2e-45f8-a737-b855f43af918">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{qgjbydza}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="440" y="479" width="15" height="10" uuid="94aabf75-9d2e-45f8-a737-b855f43af918">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{qgjbydza}==2)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="458" y="473" width="28" height="20" uuid="ceb0a8dd-8078-4c4b-9e6a-29f16c2802f7"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[无]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="453" width="71" height="20" uuid="ba88709b-97d7-4858-9439-b79059305cad"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[佩戴助听装置
]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="104" y="486" width="15" height="10" uuid="616b317a-926b-48d6-a5c0-5e56cc4d22d1">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{tlztq}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="104" y="486" width="15" height="10" uuid="616b317a-926b-48d6-a5c0-5e56cc4d22d1">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{tlztq}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="118" y="480" width="15" height="20" uuid="4db562fd-6130-4e52-9173-d6a5c60411ca"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[是]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="139" y="486" width="15" height="10" uuid="836bdca4-d058-41d7-8e89-39bcf0e730e2">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{tlztq}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="139" y="486" width="15" height="10" uuid="836bdca4-d058-41d7-8e89-39bcf0e730e2">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{tlztq}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="152" y="480" width="15" height="20" uuid="9c363df7-7764-43f7-88c0-cd58e49a4c94"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[否]]></text>
			</staticText>
			<staticText>
				<reportElement x="349" y="568" width="174" height="39" uuid="1cb75262-d668-4baf-b331-5eed1e85d145"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[双下肢缺失或者丧失运动功能障碍是
否能够自主坐立]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="428" y="591" width="15" height="10" uuid="8f4427ed-3605-443b-bc38-2149ea6cca56">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zzzl}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="428" y="591" width="15" height="10" uuid="8f4427ed-3605-443b-bc38-2149ea6cca56">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zzzl}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="443" y="584" width="15" height="20" uuid="d66a683f-10ca-4ec7-babf-4c62fab43532"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[是]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="466" y="591" width="15" height="10" uuid="e6de666b-6dd1-4afc-af53-734369b62396">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zzzl}==0)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/true.png")]]></imageExpression>
			</image>
			<image isLazy="true">
				<reportElement x="466" y="591" width="15" height="10" uuid="e6de666b-6dd1-4afc-af53-734369b62396">
					<printWhenExpression><![CDATA[new java.lang.Boolean($F{zzzl}==1)]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="482" y="584" width="15" height="20" uuid="0d090d1a-2c6c-4f44-8113-74837b4df915"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[否]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="144" y="630" width="15" height="10" uuid="648763f5-1438-4c5b-859f-b387b58b4c7f"/>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="162" y="624" width="48" height="20" uuid="39a680ad-4e21-4414-b704-3df24022d7f4"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[本人申请 ]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="286" y="630" width="15" height="10" uuid="3e4f3564-6293-4687-84df-f8d14d1667c7"/>
				<imageExpression><![CDATA[this.getClass().getResourceAsStream("/static/report/false.png")]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="304" y="624" width="24" height="20" uuid="af8d38ce-7240-4c1a-875c-bff936260738"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[委托]]></text>
			</staticText>
			<staticText>
				<reportElement x="331" y="624" width="82" height="20" uuid="ace08bf7-b3a3-4ec5-bcae-e87b44db3e8d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="413" y="624" width="59" height="20" uuid="a58b81c4-96de-4577-aedd-168e4bda1c0c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="黑体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[代理申请]]></text>
			</staticText>
		</band>
		<band height="802">
			<staticText>
				<reportElement x="39" y="294" width="473" height="496" uuid="9417d3c4-9cba-4f8b-b0c6-7974d397c959"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[按照《机动车驾驶证申领和使用规定》（公安部令第123号）规定， 申请机动车驾驶证的

人，应当符合下列身体条件：

　　1、身高：申请大型客车、牵引车、城市公交车、大型货车、无轨电车准驾车型的，

身高为155厘米以上。申请中型客车准驾车型的，身高为150厘米以上；

　　2、视力：申请大型客车、牵引车、城市公交车、中型客车、大型货车、无轨电车或

者有轨电车准驾车型的，两眼裸视力或者矫正视力达到对数视力表5.0以上。申请其他准

驾车型的，两眼裸视力或者矫正视力达到对数视力表4.9以上；

　　3、辨色力：无红绿色盲；

　　4、听力：两耳分别距音叉50厘米能辨别声源方向。有听力障碍但佩戴助听设备能够

达到以上条件的，可以申请小型汽车、小型自动挡汽车准驾车型的机动车驾驶证；

　　5、上肢：双手拇指健全，每只手其他手指必须有三指健全，肢体和手指运动功能正

常。但手指末节残缺或者右手拇指缺失的，可以申请小型汽车、小型自动挡汽车、低速载

货汽车、三轮汽车准驾车型的机动车驾驶证；

　　6、下肢：双下肢健全且运动功能正常，不等长度不得大于5厘米。但左下肢缺失或者

丧失运动功能的，可以申请小型自动挡汽车准驾车型的机动车驾驶证。右下肢、双下肢缺

失或者丧失运动功能但能够自主坐立的，可以申请残疾人专用小型自动挡载客汽车准驾车

型的机动车驾驶证；

　　7、躯干、颈部：无运动功能障碍。]]></text>
			</staticText>
			<staticText>
				<reportElement x="39" y="52" width="473" height="40" uuid="5815c62b-f45b-4775-b4ab-8a6a5032e3cf"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="18" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[填  表  说  明]]></text>
			</staticText>
			<staticText>
				<reportElement x="39" y="94" width="474" height="160" uuid="137f0a29-57f2-4320-924d-6b8cb7273629"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="宋体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[    一、使用黑色、蓝色墨水笔，用中文填写，字体工整，不得涂改。

    二、标注有“□”符号的为选择项目，选择后在“□”中划“√”。

    三、本表所设各栏均应认真填写，不得空项。其中，“本人申告事项”栏和“本人签

字”必须由本人填写；“医疗机构填写事项”栏和医生签字必须由经办的医生填写并签字

，对于肢体不健全的，应当写明肢体缺失的部位和程度。“委托代理人信息”和“代理人

签字”必须由代理人填写。]]></text>
			</staticText>
			<staticText>
				<reportElement x="39" y="254" width="473" height="40" uuid="00699c1e-02e9-46e9-8828-567fda5671d0"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="16" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[申请机动车驾驶证的身体条件]]></text>
			</staticText>
			<staticText>
				<reportElement x="232" y="8" width="67" height="40" uuid="329fa0fd-6e57-49c3-b220-c8b7f25a24b0"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
