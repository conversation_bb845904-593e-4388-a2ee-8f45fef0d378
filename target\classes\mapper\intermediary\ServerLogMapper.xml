<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.intermediary.dao.IServerLogDao">
    <resultMap id="serverLogCut" type="com.fwy.intermediary.entity.ServerLog">
        <result column="code" property="code"/>
        <result column="server_name" property="serverName"/>
        <result column="result" property="result"/>
        <result column="state_id" property="stateId"/>
        <result column="server_desc" property="serverDesc"/>
        <result column="exception_desc" property="exceptionDesc"/>
        <result column="create_time" property="createTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="findByCondition" parameterType="ServerLogCondition" resultMap="serverLogCut">
        select * from system_server_log where state_id != 3
        <if test="stateId != null">
            and state_id = #{stateId}
        </if>
        <if test="serverName != null">
            and server_name like concat('%', #{serverName}, '%')
        </if>
        <if test="serverDesc != null">
            and server_desc like concat('%', #{serverDesc}, '%')
        </if>
        <if test="exceptionDesc != null &amp;&amp; exceptionDesc !=''">
            and exception_desc like concat('%', #{exceptionDesc}, '%')
        </if>

        <if test="startDate != null">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            <!--&lt;小于-->
            and create_time &lt;= #{endDate}
        </if>

    </select>
</mapper>