<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fwy.intermediary.dao.CarCollectionImageMapper" >
  <resultMap id="BaseResultMap" type="com.fwy.intermediary.entity.CarCollectionImage" >
    <id column="code" property="code" jdbcType="VARCHAR" />
    <result column="car_num" property="carNum" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="state_id" property="stateId" jdbcType="INTEGER" />
    <result column="oss_url" property="ossUrl" jdbcType="VARCHAR" />
    <result column="count" property="count" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />

<!--    标记时间（新增字段）-->
    <result column="mark_time" property="markTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    code, car_num, oss_url, count, create_time
  </sql>

  <resultMap id="ListMap" type="com.fwy.intermediary.entity.show.CarList">
    <result column="count" property="count" />
    <result column="car_num" property="carNum" />
    <result column="recent" property="recent" />
  </resultMap>

  <!--删除状态为3的数据-->
  <delete id="deleteDataByState">
        delete from car_collection_image
        where state_id=3
  </delete>

  <!--查询频次列表展示数据-->
  <select id="findRecordByPage" parameterType="com.fwy.intermediary.entity.CarCollectionImage" resultMap="BaseResultMap">
    SELECT i.code,r.car_num,i.type,i.state_id,i.oss_url,count(1)as count,MAX(r.create_time)as create_time
    FROM car_collection_record r right join car_collection_image i
    on r.car_num = i.car_num
    <where>
      r.car_num in(select car_num from car_collection_image where state_id!=3 and type!=-1)
      <if test="startDate != null">
        and r.create_time >= #{startDate}
      </if>
      <if test="deptIds != null and deptIds.size() != 0">
        and r.dept_id in
        <foreach collection="deptIds" item="deptId" index="index"
                 open="(" close=")" separator=",">
          #{deptId}
        </foreach>
      </if>
      <if test="endDate != null">
        <!--&lt;小于-->
        and r.create_time &lt;= #{endDate}
      </if>
      <if test="carNum!=null and carNum!='' ">
        and r.car_num like '%${carNum}%'
      </if>
    </where>
    GROUP BY
    i.CODE,
    r.car_num,
    i.type,
    i.state_id,
    i.oss_url
    order by
    count desc,
    create_time DESC
  </select>

  <!--判断是否存在-->
  <select id="ifExists" resultType="java.lang.Integer" parameterType="java.lang.String">
    select exists
    (select count from car_collection_image
    where car_num = #{carNum,jdbcType=VARCHAR})
  </select>

<!--  @Update("update car_collection_image set count=count+1 where car_num=#{carNum}")-->
  <!--更新频次-->
  <update id="updateCount" parameterType="java.lang.String">
    update car_collection_image
    set count = count+1
    where car_num = #{carNum,jdbcType=VARCHAR}
  </update>


<!--  @Select("select code from car_collection_image where car_num=#{carNum}")-->
  <!--获取主键-->
  <select id="getCode" resultType="java.lang.String" parameterType="java.lang.String">
    select code
    from car_collection_image
    where car_num = #{carNum,jdbcType=VARCHAR}
  </select>

<!--  @Select("SELECT * FROM car_collection_image WHERE create_time BETWEEN 'xxx' AND 'xxx'")-->
  <!--通过时间查找列表-->
  <select id="getListByTime" resultMap="BaseResultMap">
    select car_num,count
    from car_collection_image
    where create_time
    between #{t1,jdbcType=TIMESTAMP} and #{t2,jdbcType=TIMESTAMP}
  </select>

  <!--通过车牌！删除！车辆信息-->
  <delete id="deleteByCarNum" parameterType="java.lang.String" >
    delete from car_collection_image
    where car_num = #{carNum,jdbcType=VARCHAR}
  </delete>

  <!--插入一条数据  分析服务插入-->
  <insert id="save" parameterType="com.fwy.intermediary.entity.CarCollectionImage" >
    insert into car_collection_image (code, car_num, type, state_id, oss_url, count, create_time,dept_id,dept_name)
    values (#{code,jdbcType=VARCHAR}, #{carNum,jdbcType=VARCHAR},#{type,jdbcType=INTEGER}, #{stateId,jdbcType=INTEGER},#{ossUrl,jdbcType=VARCHAR},
      #{count,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},#{deptId},#{deptName})
  </insert>

  <!--通过主键查询车辆信息-->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select *
    from car_collection_image
    where code = #{code,jdbcType=VARCHAR}
  </select>


  <select id="selectOneByCarNum" resultMap="BaseResultMap">
    select * from car_collection_image where car_num = #{carNum} and state_id != 3
  </select>
    <select id="findRecordByCarList" resultType="com.fwy.intermediary.entity.CarCollectionImage">
      SELECT i.code,r.car_num AS carNum,i.type,i.state_id AS stateId,i.oss_url AS ossUrl,count(1)as count,MAX(r.create_time)as create_time
      FROM car_collection_record r right join car_collection_image i
      on r.car_num = i.car_num
      <where>
        r.car_num in(select car_num from car_collection_image where state_id!=3 and type!=-1)
        <if test="startDate != null">
          and r.create_time >= #{startDate}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
          and r.dept_id in
          <foreach collection="deptIds" item="deptId" index="index"
                   open="(" close=")" separator=",">
            #{deptId}
          </foreach>
        </if>
        <if test="endDate != null">
          <!--&lt;小于-->
          and r.create_time &lt;= #{endDate}
        </if>
        <if test="carNum!=null and carNum!='' ">
          and r.car_num like '%${carNum}%'
        </if>
      </where>
      GROUP BY
      i.CODE,
      r.car_num,
      i.type,
      i.state_id,
      i.oss_url
      order by
      count desc,
      create_time DESC
    </select>
  <select id="ifExistsByDept" resultType="java.lang.Integer">
    select exists
             (select count from car_collection_image
              where car_num = #{carNum,jdbcType=VARCHAR} AND dept_id = #{deptId})
  </select>

  <insert id="insertSelective" parameterType="com.fwy.intermediary.entity.CarCollectionImage" >
    insert into car_collection_image
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        code,
      </if>
      <if test="carNum != null" >
        car_num,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="stateId != null" >
        state_id,
      </if>
      <if test="ossUrl != null" >
        oss_url,
      </if>
      <if test="count != null" >
        count,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null" >
        #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="stateId != null" >
        #{stateId,jdbcType=INTEGER},
      </if>
      <if test="ossUrl != null" >
        #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="count != null" >
        #{count,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fwy.intermediary.entity.CarCollectionImage" >
    update car_collection_image
    <set >
      <if test="carNum != null" >
        car_num = #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{count,jdbcType=INTEGER},
      </if>
      <if test="stateId != null" >
        state_id = #{stateId,jdbcType=INTEGER},
      </if>
      <if test="ossUrl != null" >
        oss_url = #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="count != null" >
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where code = #{code,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fwy.intermediary.entity.CarCollectionImage" >
    update car_collection_image
    set car_num = #{carNum,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      state_id = #{stateId,jdbcType=INTEGER},
      oss_url = #{ossUrl,jdbcType=VARCHAR},
      count = #{count,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where code = #{code,jdbcType=VARCHAR}
  </update>

  <update id="addCount">
    update car_collection_image set count = count+1 where code = #{carId}
  </update>

</mapper>