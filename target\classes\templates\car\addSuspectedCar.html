<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>添加可疑车辆</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .input-wrapper .layui-input,
        .layui-form-select {
            width: 250px;
        }

        .input-wrapper img {
            height: 150px;
        }

        .layui-form-item .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-block {
            margin-left: 140px;
        }

        .layui-form-item .layui-form-text {
            line-height: 38px;
            display: inline-block;
            vertical-align: middle;
        }

    </style>
</head>
<body>
<form class="layui-form" lay-filter="edit-form">
    <input type="hidden" name="code" autocomplete="off"
           th:value="${suspectedCarRecord.code}">
    <div class="layui-form-item">
        <label class="layui-form-label">车牌号</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" name="carNum" autocomplete="off" placeholder="请输入车牌号"
                   class="layui-input" lay-verify="required" th:value="${suspectedCarRecord.carNum}">
        </div>
    </div>
    <input type="hidden" name="carId" th:if="${suspectedCarRecord.code != null}" lay-verify="required"
           autocomplete="off"
           th:value="${suspectedCarRecord.carId}">

    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block input-wrapper">
            <select name="stateId" lay-verify="required">
                <option value="">请选择状态</option>
                <option value="1" th:selected="${suspectedCarRecord.stateId == 1}">有效</option>
                <option value="2" th:selected="${suspectedCarRecord.stateId == 2}">失效</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">图像</label>
        <div class="layui-input-block">
            <input type="file" name="file" id="file" autocomplete="off">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block input-wrapper" onclick="show_img()" id="layer-photos-demo">
            <input type="hidden" name="ossUrl" id="ossUrl" autocomplete="off"
                   th:value="${suspectedCarRecord.ossUrl}">
            <img id="image-preview" class="card-image" style="display: none;"
                 onerror="replaceWithErrorImage(this)">
        </div>
    </div>

    <!-- 添加其他表单项 -->

    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="submit" id="subBtn">保存</button>
            <button type="reset" class="layui-btn layui-btn-primary" onclick="resetForm()">重置</button>
        </div>
    </div>
</form>
<script th:inline="javascript">
    let ossUrl = /*[[${suspectedCarRecord.ossUrl}]]*/ null;
    if (ossUrl != null) {
        $("#image-preview").attr("src", imageBase + ossUrl);
        $("#image-preview").show();
    }else{
        $("#image-preview").attr("src");
        $("#image-preview").hide();
    }
</script>
<script>
    layui.use(['form', 'layer'], function () {
        var form = layui.form;
        var layer = layui.layer;
        var formModified = false;
        // 图像预览
        function previewImage(file) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $('#image-preview').attr('src', e.target.result);
                $("#image-preview").show();
            };
            reader.readAsDataURL(file);
            // 更新ossUrl隐藏字段的值
            var ossUrl = URL.createObjectURL(file);
            $('#ossUrl').val(ossUrl);
        }

        // 监听文件选择框变化事件
        $('#file').on('change', function () {
            var file = this.files[0];
            var file = this.files[0];
            if (file) {
                var ext = file.name.split('.').pop().toLowerCase();
                if (ext !== 'jpg' && ext !== 'jpeg' && ext !== 'png') {
                    layer.msg('只能上传jpg、jpeg或png格式的图片');
                    this.value = ''; // 清空文件选择框
                    return false;
                }
                previewImage(file);
            }
        });
        // 表单提交监听
        form.on('submit(submit)', function (data) {
            var formData = new FormData();
            if (!data.field.ossUrl) {
                layer.msg('未选择图片');
                return false;
            }
            // 将其他字段数据也添加到formData中
            for (var key in data.field) {
                if(key == "file"){
                    continue;
                }
                formData.append(key, data.field[key]);
            }
            file = $("#file")[0].files[0];
            if(file){
                formData.set('file', file ? file : null);
            }
            $.ajax({
                url: ctx + 'suspectedCarController/save',
                type: 'POST',
                data: formData,
                processData: false, // 不处理formData
                contentType: false, // 不设置Content-Type请求头
                success: function (res) {
                    if (res.code === 200) {
                        layer.msg('保存成功');
                        // 关闭弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        // 刷新父页面数据
                        parent.location.reload();
                    } else {
                        layer.msg(res.msg);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Ajax 请求发生错误:', error);
                }
            });

            return false; // 阻止表单提交
        });

        form.render();
    });

    // 重置表单及图像
    function resetForm() {
        var form = layui.form;
        var $ = layui.jquery;

        // 重置表单项
        form.val("edit-form", {});
        $("#ossUrl").val(ossUrl);
        // 重置图像
        if (ossUrl != null) {
            $("#image-preview").attr("src", ctx + ossUrl);
            $("#image-preview").show();
        }else{
            $("#image-preview").attr("src");
            $("#image-preview").hide();
        }
        formModified = false;
    }

    function show_img() {
        var $ = layui.jquery;
        layer.photos(
            {
                photos: '#layer-photos-demo',
                anim: 5,
                tab: function (pic, layero) {
                    $(document).on("mousewheel", ".layui-layer-photos", function (ev) {
                        var oImg = this;
                        var ev = event || window.event;//返回WheelEvent
                        //ev.preventDefault();
                        var delta = ev.detail ? ev.detail > 0 : ev.wheelDelta < 0;
                        var ratioL = (ev.clientX - oImg.offsetLeft) / oImg.offsetWidth,
                            ratioT = (ev.clientY - oImg.offsetTop) / oImg.offsetHeight,
                            ratioDelta = !delta ? 1 + 0.1 : 1 - 0.1,
                            w = parseInt(oImg.offsetWidth * ratioDelta),
                            h = parseInt(oImg.offsetHeight * ratioDelta),
                            l = Math.round(ev.clientX - (w * ratioL)),
                            t = Math.round(ev.clientY - (h * ratioT));
                        $(".layui-layer-photos").css({
                            width: w, height: h
                            , left: l, top: t
                        });
                        $("#layui-layer-photos").css({width: w, height: h});
                        $("#layui-layer-photos>img").css({width: w, height: h});
                    });
                }
            });
    }
</script>
</body>
</html>