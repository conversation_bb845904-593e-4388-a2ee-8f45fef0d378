<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.ILogDao">
    <resultMap type="com.fwy.corebasic.entity.Core_Log" id="logMap">
        <result property="id" column="ID"/>
        <result property="userId" column="USERID"/>
        <result property="areaName" column="AREANAME"/>
        <result property="controName" column="CONTRONAME"/>
        <result property="actionName" column="ACTIONNAME"/>
        <result property="parameterJson" column="PARAMETERJSON"/>
        <result property="recordTime" column="RECORDTIME"/>
        <result property="recordIp" column="RECORDIP"/>
        <result property="controDisplay" column="CONTRODISPLAY"/>
        <result property="actionDisplay" column="ACTIONDISPLAY"/>

        <association property="user" javaType="com.fwy.corebasic.entity.Core_User">
            <id property="id" column="USERID" javaType="Long" jdbcType="INTEGER"/>
            <result property="fullName" column="fullname" javaType="java.lang.String"
                    jdbcType="VARCHAR"/>
            <association property="dept" javaType="com.fwy.corebasic.entity.Core_Dept">
                <result property="deptName" column="deptName" javaType="java.lang.String"
                        jdbcType="VARCHAR"/>
            </association>
        </association>
    </resultMap>
    <resultMap type="com.fwy.corebasic.entity.Log_Count" id="logCount">
        <result property="actionname" column="actionname"/>
        <result property="countLog" column="countLog"/>
    </resultMap>
    <!-- 基本列 -->
    <sql id="baseColumn">
        ID,
        USERID,
        AREANAME,
        CONTRONAME,
        ACTIONNAME,
        PARAMETERJSON,
        RECORDTIME,
        RECORDIP,
        CONTRODISPLAY,
        ACTIONDISPLAY,
        LOGTYPE
    </sql>
    <insert id="save" useGeneratedKeys="true">
        insert into core_log (<include refid="baseColumn"/>)
        VALUES (#{id},
        #{user.id},
        #{areaName},
        #{controName},
        #{actionName},
        #{parameterJson},
        #{recordTime},
        #{recordIp},
        #{controDisplay},
        #{actionDisplay},
        #{logType})
    </insert>
    <!--获取规定时间内登录次数-->
    <select id="getLoginCount" parameterType="String" resultType="Integer">
        select count(*)
        from core_log l
                 left join core_user u on l.USERID = u.ID
        where u.USERNAME = #{username}
          and l.RECORDTIME &gt;= date_format(#{begintime}, '%Y-%m-%d %H:%i:%s')
          and l.CONTRONAME = 'login'
    </select>
    <!-- 列表 -->
    <select id="list" resultMap="logMap">
        select l.*,u.FULLNAME,d.DEPTNAME from core_log l left join
        core_user u on l.USERID=u.ID left join Core_Dept d on u.DEPTID=d.ID
        ${whereSql}
        <if test="begintime !=null and begintime!=''">
            and date_format(date_format(l.RECORDTIME,'%Y-%m-%d'),'%Y-%m-%d') &gt;= date_format(#{begintime},'%Y-%m-%d')
        </if>
        <if test="endtime !=null and endtime!=''">
            and date_format(date_format(l.RECORDTIME,'%Y-%m-%d'),'%Y-%m-%d') &lt;= date_format(#{endtime},'%Y-%m-%d')
        </if>
        order by l.RECORDTIME DESC
    </select>
    <!-- 报表 -->
    <select id="countLogNoAdmin" resultMap="logCount">
        select count(*) countLog, l.ACTIONDISPLAY actionname
        from core_log l
        join core_user u on l.USERID = u.ID
        left join core_dept d on u.DEPTID = d.ID
        where u.DEPTID != 0
        and u.ID = #{id}
        and l.CONTRONAME in
        <foreach collection="controname" item="employeeId" index="index"
                 open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        group by l.ACTIONDISPLAY
    </select>
    <!-- 报表 -->
    <select id="countLogNoAdminAq" resultMap="logCount">
        select count(*) countLog, l.CONTRODISPLAY actionname
        from core_log l
        join core_user u on l.USERID = u.ID
        left join core_dept d on u.DEPTID = d.ID
        where u.DEPTID != 0
        and l.CONTRONAME in
        <foreach collection="controname" item="employeeId" index="index"
                 open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        group by l.CONTRODISPLAY
    </select>

    <!-- 列表 -->
    <select id="listByCondition" resultMap="logMap">
        select l.*, u.FULLNAME, d.DEPTNAME
        from core_log l
        left join
        core_user u on l.USERID = u.ID
        left join core_dept d on u.DEPTID = d.ID
        <where>
            <if test="deptName != null and deptName != ''">
                and d.DEPTNAME like concat('%',#{deptName},'%')
            </if>
            <if test="fullName != null and fullName != ''">
                and u.FULLNAME like concat('%',#{fullName},'%')
            </if>
            <if test="actionDisplay != null and actionDisplay != ''">
                and l.ACTIONDISPLAY like concat('%',#{actionDisplay},'%')
            </if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(date_format(l.RECORDTIME, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d %H:%i:%s') &gt;=
                date_format(#{beginTime}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(date_format(l.RECORDTIME, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d %H:%i:%s') &lt;=
                date_format(#{endTime}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="depts != null and depts.size() != 0">
                and u.DEPTID in
                <foreach collection="depts" item="deptId" index="index"
                         open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="logType!=null">
                and l.LOGTYPE=#{logType}
            </if>
        </where>
        order by l.RECORDTIME desc
    </select>
    <!-- 根据主键获取单个对象 -->
    <select id="getObjectById" parameterType="long" resultMap="logMap">
        select *
        from core_log l
                 left join core_user u on l.USERID = u.ID
                 left join core_dept d on u.DEPTID = d.ID
                 left join core_user_check ck on l.USERID = ck.ID
        where l.ID = #{id}
    </select>
    <!-- 查询小类 -->
    <select id="getXllist" resultType="com.fwy.corebasic.entity.Core_Log">
        select distinct CONTRODISPLAY, ACTIONDISPLAY, ACTIONNAME
        from core_log where
        CONTRONAME in
        <foreach collection="controname" item="employeeId" index="index"
                 open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
    </select>
    <select id="safeLoginlistNoAdmin" resultMap="logMap">
        select l.*,
        u.FULLNAME,
        d.DEPTNAME
        from core_log l
        join core_user u on l.USERID = u.ID
        left join core_dept d on u.DEPTID = d.ID
        where u.DEPTID != 0
        and u.DEPTID in (SELECT ID
        FROM (
        SELECT t1.ID,
        IF(FIND_IN_SET(PARENTID, @pids) > 0,
        @pids := CONCAT(@pids, ',', ID), 0) AS ischild
        FROM (SELECT ID, PARENTID
        FROM core_dept t
        ORDER BY ID, PARENTID
        ) t1,
        (SELECT @pids := #{deptid}) t2
        ) t3
        WHERE ischild != 0
        OR ID = #{deptid})
        and (l.CONTRONAME in
        <foreach collection="controname" item="employeeId" index="index"
                 open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        or (l.LOGTYPE != -1 and l.LOGTYPE is not null)
        )
        <if test="logDateLimit != null and logDateLimit != ''">
            and l.RECORDTIME &gt;= now() - #{logDateLimit}
        </if>
        <if test="logDateLimit == null or logDateLimit == ''">
            and l.RECORDTIME &gt;= now() - 7
        </if>
        <if test="username != null and username != ''">
            and u.FULLNAME like concat('%',#{username},'%')
        </if>
        <if test="actionname != null and actionname != ''">
            and l.ACTIONNAME = #{actionname}
        </if>
        <if test="deptname != null and deptname != ''">
            and d.DEPTNAME = #{deptname}
        </if>
        <if test="logType != null and logType != ''">
            and l.LOGTYPE = #{logType}
        </if>
        <if test="controlDisplay != null and controlDisplay != ''">
            and l.CONTRODISPLAY like concat('%',#{controlDisplay},'%')
        </if>

        <if test="nodeId != null">
            and u.DEPTID in
            (select cd.ID
            from core_dept cd where cd.ID in
            <foreach collection="nodeId" item="deptids" index="index"
                     open="(" close=")" separator=",">
                #{deptids}
            </foreach>)
        </if>
        order by l.RECORDTIME DESC
    </select>

    <select id="getLoginLogList" resultMap="logMap">
        select l.*,
        u.FULLNAME,
        d.DEPTNAME
        from core_log l
        join core_user u on l.USERID = u.ID
        left join core_dept d on u.DEPTID = d.ID
        <where>
            <if test="depts != null and depts.size() != 0">
                and u.DEPTID in
                <foreach collection="depts" item="deptId" index="index"
                         open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="controname != null and controname != ''">
                and l.CONTRONAME = #{controname}
            </if>
            <if test="actionName != null and actionName != ''">
                and l.ACTIONNAME = #{actionName}
            </if>
            <if test="actionDisplay != null and actionDisplay != ''">
                and l.ACTIONDISPLAY like concat('%',#{actionDisplay},'%')
            </if>
            <if test="fullName != null and fullName != ''">
                and u.FULLNAME like concat('%',#{fullName},'%')
            </if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(date_format(l.RECORDTIME, '%Y-%m-%d %h:%i:%s'), '%Y-%m-%d %h:%i:%s') &gt;=
                date_format(#{beginTime}, '%Y-%m-%d %h:%i:%s')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(date_format(l.RECORDTIME, '%Y-%m-%d %h:%i:%s'), '%Y-%m-%d %h:%i:%s') &lt;=
                date_format(#{endTime}, '%Y-%m-%d %h:%i:%s')
            </if>
            <if test="logType !=null">
                and l.LOGTYPE = #{logType}
            </if>
        </where>
        order by l.RECORDTIME desc
    </select>

    <!-- 查询 安全日志 -->
    <select id="safeLoginlist" resultMap="logMap">
        select l.*,
        u.FULLNAME,
        d.DEPTNAME
        from core_log l
        join core_user u on l.USERID = u.ID
        left join core_dept d on u.DEPTID = d.ID
        <where>
            <if test="logDateLimit != null and logDateLimit != ''">
                and l.RECORDTIME &gt;= date_add(now(),INTERVAL -#{logDateLimit} DAY)
            </if>
            <if test="logType!=null">
                and l.LOGTYPE=#{logType}
            </if>
            <if test="logDateLimit == null or logDateLimit == ''">
                and l.RECORDTIME &gt;= date_add(now(),INTERVAL -7 DAY)
            </if>
            <if test="username != null and username != ''">
                and u.FULLNAME like concat('%',#{username},'%')
            </if>
            <if test="deptname != null and deptname != ''">
                and d.DEPTNAME = #{deptname}
            </if>
            <if test="actionname != null and actionname != ''">
                and l.ACTIONNAME = #{actionname}
            </if>
            <if test="controlDisplay != null and controlDisplay != ''">
                and l.CONTRODISPLAY like concat('%',#{controlDisplay},'%')
            </if>

            <if test="depts != null and depts.size() != 0">
                and u.DEPTID in
                <foreach collection="depts" item="deptId" index="index"
                         open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
        </where>
        order by l.RECORDTIME desc
    </select>
    <!-- 报表 -->
    <select id="countSafeLogAq" resultMap="logCount">
        select count(*) countLog,l.CONTRODISPLAY actionname from core_log l
        join core_user u on l.USERID=u.ID
        left join core_dept d on u.DEPTID=d.ID
        <where>
            <if test="logType != null">
                l.LOGTYPE = #{logType}
            </if>
            <if test="depts != null and depts.size() != 0">
                and u.DEPTID in
                <foreach collection="depts" item="deptId" index="index"
                         open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
        </where>
        group by l.CONTRODISPLAY
    </select>
    <!-- 报表 -->
    <select id="countSafeLogNoAdminAq" resultMap="logCount">
        select count(*) countLog, l.CONTRODISPLAY actionname
        from core_log l
        join core_user u on l.USERID = u.ID
        left join core_dept d on u.DEPTID = d.ID
        where u.DEPTID in
        (
        SELECT ID
        FROM (
        SELECT t1.ID,
        IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID),
        0) AS ischild
        FROM (SELECT ID, PARENTID
        FROM core_dept t
        ORDER BY ID, PARENTID
        ) t1,
        (SELECT @pids := #{id}) t2
        ) t3
        WHERE ischild != 0
        OR ID = #{id})
        and (l.CONTRONAME in
        <foreach collection="controname" item="employeeId" index="index"
                 open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        )
        group by l.CONTRODISPLAY
    </select>
    <!-- 报表 -->
    <select id="countLog" resultMap="logCount">
        select count(l.ID) countLog,l.ACTIONDISPLAY actionname from core_log l
        join core_user u on l.USERID=u.ID
        left join core_dept d on u.DEPTID=d.ID
        <where>
            <if test="logType != null">
                l.LOGTYPE = #{logType}
            </if>
            <if test="depts != null and depts.size() != 0">
                and u.DEPTID in
                <foreach collection="depts" item="deptId" index="index"
                         open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
        </where>
        group by l.ACTIONDISPLAY
    </select>

    <select id="getNewLoginLog" resultType="com.fwy.corebasic.entity.Core_Log">
        select RECORDTIME,RECORDIP from core_log
        where LOGTYPE=#{type} and USERID=#{userid} and ACTIONNAME='success'
        order by RECORDTIME desc
    </select>
    <select id="getLoginFailLog" resultType="com.fwy.corebasic.entity.Core_Log">
        select RECORDTIME,RECORDIP,ACTIONDISPLAY from core_log
        where LOGTYPE=#{type} and USERID=#{userid} and ACTIONNAME='fail'
        <if test="startTime !=null">
            and RECORDTIME &gt;=#{startTime}
        </if>
        <if test="startTime !=null">
            and RECORDTIME &lt;=#{endTime}
        </if>
        order by RECORDTIME desc
    </select>
</mapper>