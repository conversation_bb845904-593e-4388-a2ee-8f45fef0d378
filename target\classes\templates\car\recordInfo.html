<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>详细记录信息</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>

    <style>
        .input-wrapper .layui-input,
        .layui-form-select {
            width: 250px;
        }

        .input-wrapper img {
            height: 250px;
        }

        .layui-form-item .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-block {
            margin-left: 140px;
        }

        .layui-form-item .layui-form-text {
            line-height: 38px;
            display: inline-block;
            vertical-align: middle;
        }
        .layui-form{
            padding-top:30px;
        }
    </style>

</head>
<body>
<form class="layui-form" lay-filter="edit-form">

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label">图像</label>-->
<!--        <div class="layui-input-block input-wrapper">-->
<!--            <img id="image-preview" class="card-image" style="display: none;"-->
<!--                 onerror="replaceWithErrorImage(this)">-->
<!--        </div>-->
<!--    </div>-->
    <div class="layui-form-item">
        <label class="layui-form-label ">图像：</label>
        <div class="layadmin-shortcut">
            <ul class="layui-row" style="margin-left: 140px;padding: 0px" id="layer-photos-demo"
                onclick="show_img()">
                <li>
                    <div class="img-div">
                        <input type="hidden" name="ossUrl" id="ossUrl" autocomplete="off"
                               th:value="${carRecord.ossUrl}">
                        <img id="image-preview" class="image-preview" style="display: none;margin-top: -20px;max-height: 45%;max-width: 40%"
                             onclick="previewImg()" onerror="replaceWithErrorImage(this)">
                    </div>
                </li>
            </ul>
        </div>
    </div>



    <div class="layui-form-item">
        <label class="layui-form-label">车牌号</label>
        <div class="layui-input-block input-wrapper">
            <span class="layui-form-text">[[${carRecord.carNum}]]</span>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">记录时间</label>
        <div class="layui-input-block">
            <span class="layui-form-text">[[${#dates.format(carRecord.createTime, 'yyyy-MM-dd HH:mm:ss')}]]</span>
        </div>
    </div>

</form>
<script th:inline="javascript">
    // let ossUrl = /*[[${carRecord.ossUrl}]]*/ null;
    let ossUrl = [[${carRecord.ossUrl}]] /*null*/;

    $("#image-preview").attr("src", imageBase + ossUrl);
    $("#image-preview").show();

    //显示大图片
    window.onload = function () {
        show_img()
    }

    function show_img() {
        var $ = layui.jquery;
        layer.photos(
            {
                photos: '#layer-photos-demo',
                anim: 5,
                tab: function (pic, layero) {
                    $(document).on("mousewheel", ".layui-layer-photos", function (ev) {
                        var oImg = this;
                        var ev = event || window.event;//返回WheelEvent
                        //ev.preventDefault();
                        var delta = ev.detail ? ev.detail > 0 : ev.wheelDelta < 0;
                        var ratioL = (ev.clientX - oImg.offsetLeft) / oImg.offsetWidth,
                            ratioT = (ev.clientY - oImg.offsetTop) / oImg.offsetHeight,
                            ratioDelta = !delta ? 1 + 0.1 : 1 - 0.1,
                            w = parseInt(oImg.offsetWidth * ratioDelta),
                            h = parseInt(oImg.offsetHeight * ratioDelta),
                            l = Math.round(ev.clientX - (w * ratioL)),
                            t = Math.round(ev.clientY - (h * ratioT));
                        $(".layui-layer-photos").css({
                            width: w, height: h
                            , left: l, top: t
                        });
                        $("#layui-layer-photos").css({width: w, height: h});
                        $("#layui-layer-photos>img").css({width: w, height: h});
                    });
                }
            });
    }

</script>
</body>
</html>