spring:
  #设置session缓存类型为redis
  #    session:
  #      store-type: redis
  #设置缓存类型为redis
#  profiles:
#    active: prod
  cache:
    type: redis
  redis:
    database: 3
#    host: *************** # Redis server host.
    host: 127.0.0.1 # Redis server host.
#    host: ***************
    #password: jxkth123456 # Login password of the redis server.
    port: 6379 # Redis server port.
    password: 123456
    ssl: false # Whether to enable SSL support.
    timeout: 5000 # Connection timeout
    expire: 3600 # 过期时间

# 服务地址,定时获取心跳，检测服务存活状态
sysServer:
  refreshTaskUrl: http://localhost:8806/kth/server/taskController/refreshTask
  savePicUrl: http://localhost:8806/kth/server/taskController/savePic
com:
  api:
    #前端加密后端解密
    privateKey1: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMKxz0oQpDsLobrCQHIo2cy4z0GbwCzC1XRZwTUlPc/LWWqQcqWYO4yCJjoUPuoGb2VZSOj6i23v1OJA1z+kra344ReBjOpuImb8kzdUx6k08ID/zi9XJZ+0x9O324SyVn2sqhTHGbFmHo/MmixXh4QaIDDuo2ldjR3e5iJdlvvRAgMBAAECgYEApfhmOUFOf3c++7I0thzU7oxtQgTAImhFBtasud1xf/5dz1YsJbZMrU8Vv1Ua/O1XcRJX8SQEUEevNuEHnZ2I39R8HKzMFGbq97boBQuuxtIA1Zo/MJ5StORIGyuE1Nt6uuB/wNKRo0qr05dBWwxcXBUEMvUYUP1iylE+pa7l7EECQQDtXm8qx28NdqpgXeZRvxRgriViiNOCcaDBUoKgsx5ijW54zIl2lKaE9ZG1EOsTUv7aa93EZK/0optxJ2aWA6PNAkEA0fnmc5S9ZymEHKPlIRNPhh/mvCt7xXAuuIa2omcQgzfdf4PfgcbMUk7/EUzLrbWJJYeHc5UUoAOHokol7wC8FQJAOAjV92RTG/uKZutjxqJz/gq+Oo+QVrfaOijvO15tath2Xr/yJ3MaCEMsXXEb/VjXXtqd5JHcU8TbKxAhEILVdQJARgjnH5f10IndYbwcuXp6ZM8RI6QRRVpUiwGLuwFLVaj1fQmcK+oFzlJA6yFFMixBkT3yJNANsA1dWPkIS/5fvQJALxSy+y9q1//0H2OWjHyYOVnotypMc1GQNnXnRTHreXgm024bSVo9VYft3t0Vm4e9HCcXmbuaFvqYw9GbfNXjgA==
    publicKey1: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCsc9KEKQ7C6G6wkByKNnMuM9Bm8AswtV0WcE1JT3Py1lqkHKlmDuMgiY6FD7qBm9lWUjo+ott79TiQNc/pK2t+OEXgYzqbiJm/JM3VMepNPCA/84vVyWftMfTt9uEslZ9rKoUxxmxZh6PzJosV4eEGiAw7qNpXY0d3uYiXZb70QIDAQAB
    #前端解密后端加密
    privateKey2: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJLRLZEej+LRmipC7aG2nVEwhpZHqbd661eze3Zkm3IYAPD4fVODjCgz2JbD9wDI7xr5oTKOmr+Ybv2LWovkPx2tBjdWl+uWAxIyUFJaJVkqRky4BvnXtrbrJhP87E2P5bkfeOkTM+f/XOmNDJwA/8/Aq5+r6J2DjTDc4DQvwzhBAgMBAAECgYEAkj+Cl5ONGxFV85E166fgL9cZyitauWXdd0BX1QjE+7NDhMbS+NUKUaXLs+tDamm+HO3bkGQTbJWowjIFoWJdJ2i7TVRpkj4NPKFe+kwiNKFlVsvQ2Jubz6a5VZm7C7UpOovQ49uz+det2O9JmqaBwcNkbJuw76tJPn8T7joAcUECQQDiEnC2jEf4kidNdgdHOBx6Q/IE1oM9gavgEbOYLEqM2IJVkTOEMKKAzh3BGQU5dBE9qiUzf46L+js9rm7/aXjZAkEApkDLrlPaU+O/so35+6zBMl02TQ/j/Nsd6TDmQfsA37tfMsvvb55pRUJ6wrrv2ncIxj+/AgeFNjXy6zN1jbdZqQJAbiTB2Y67OfYrQ+gvX5mM9lzIO2cwBfV5ge/d6lQZ9kk+JuZZjRwM8Gh7wqqoCvfrtnmBKngJQVWnK0QjPDls8QJAEb6qvsGedQPDCzjSLMWknU6LpDAFApEUoTYh7Xrv/fFO2OohdUVwMP9GgRBY13WLDzXwmjPZSQ77DZWhf23JeQJBAN9v/oFxIdZBWRZHhrXCUdYiZlSpEGmxCu4ud0dNDtzf1REBlKfpgGbwwZeU0mxrLCGJBAllgok3CXrvAmXtabo=
    publicKey2: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCS0S2RHo/i0ZoqQu2htp1RMIaWR6m3eutXs3t2ZJtyGADw+H1Tg4woM9iWw/cAyO8a+aEyjpq/mG79i1qL5D8drQY3VpfrlgMSMlBSWiVZKkZMuAb517a26yYT/OxNj+W5H3jpEzPn/1zpjQycAP/PwKufq+idg40w3OA0L8M4QQIDAQAB
    #token加密
    token: 8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92
# 上传文件路径配置
file:
  rootPath: D://intermediary/files/ # 上传文件根路径
  imgFilePath: /imgFile  #上传照片文件路径
  wxWebImg: /wxWebImg #前端微信接口图片路径
  wxAdminImg: /wxAdminImg #后台管理系统
  otherFilePath: /otherFile
  wxWebFile: /wxWebFile
  portraitCollectionImage: /portraitCollectionImage
  carCollectionImage: /CarCollectionImage

accessFile:
  resourceHandler: /intermediary/images/
  location: D://intermediary/images/  #本地图片文件夹

server:
  port: 8888

quartz:
  isStart: false #是否启动quartz框架 启动为true  开发及测试环境可配置为false
ctm:
  isOpen: false #闸机是否对接昌通码  开启对接为true 关闭为false

#阿里云OSS参数配置具体说明请参照文档 https://help.aliyun.com/document_detail/32010.html?spm=a2c4g.11186623.2.11.7deb26fddRhs34
OSS:
  endPoint: http://oss-cn-hangzhou.aliyuncs.com
  accessKeyId: LTAI5tEJAwjAeNQRu3cT3MNZ
  accessKeySecret: ******************************
  bucketName: xiayz-image
  #OSS默认访问地址,默认下载方式访问
  defaultUrl: http://xiayz-image.oss-cn-hangzhou.aliyuncs.com
  #自有域名前缀,用来网站访问图片
  ownUrl: http://test1.protest.jxjd626.com
jasypt:
  encryptor:
    password: jxkth
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

#默认模板不允许删除 配置默认的id
model:
  appointModelId: 23 #预约参数模板
  timeModelId: 0 #时间默认模板id
  holidayModelId: 466 #节假日默认模板id
  configModelId: 0 #额外参数配置模板id
wx:
  appId: wxbc57e39068862499 #微信appId
  appSecret: 44afe0a5706f0e7972d571d83f87106e  #微信appSecret
  webUrl: testjj.jxjd626.com  #微信Url

suspectPersonnel:
  expiration: 7  #疑似人员过期时间，默认七天,使用int整数
  counts: 20  #疑似人员的判定规则，出现多少次,使用int整数
  dateRange: 30 #判定次数的时间范围,使用int整数