<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	  xmlns:http="http://www.w3.org/1999/xhtml"
	  xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
	<meta charset="utf-8">
	<title>编辑字典汇总项</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<div th:replace="Importfile::html"></div>

</head>
<body>
<div class="layui-fluid">
	<div class="layui-card">
		<div class="layui-card-body">
			<form class="layui-form" action="" align="center">
				<input type="hidden" name="id" id="id" th:value="${dic?.id}">
				<div class="layui-form-item" style="padding-bottom: 30px">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>代码</label>
						<div class="layui-input-inline" style="min-width:350px">
							<input type="text" name="itemCode" id="itemCode" th:value="${dic?.itemCode}" lay-verify="required|dm" autocomplete="off" class="layui-input" maxlength="50">
						</div>
					</div>
				</div>
				<div class="layui-form-item" style="padding-bottom: 30px">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>名称</label>
						<div class="layui-input-inline" style="min-width:350px">
							<input type="text" name="itemName" id="itemName" th:value="${dic?.itemName}" lay-verify="required" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-form-item" style="padding-bottom: 30px">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>字典表名</label>
						<div class="layui-input-inline" style="min-width:350px">
							<input type="text" name="dicName" id="dicName" th:value="${dic?.dicName}" lay-verify="required|dicname" autocomplete="off" class="layui-input">
							<input type="hidden" name="dicNameOld" id="dicNameOld" th:value="${dic?.dicName}">
						</div>
					</div>
				</div>

				<button style="display:none" lay-submit lay-filter="subBtn" id="subBtn"></button>
			</form>
		</div>
	</div>
</div>
<script>
	layui.use(['table','layer','jquery', 'form'], function(){
		var table = layui.table;
		var layer = layui.layer;
		var $ = layui.jquery;
		var form = layui.form;
		form.verify({
			dm: [
				/^[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]*$/
				,'代码不能包含中文'
			],
			dicname: [
				/^[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]*$/
				,'字典表名不能包含中文'
			]
		});
		//监听提交
		form.on('submit(subBtn)', function(data){
			var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
			var data=data.field;
			$.ajax({
				method:"POST",
				url:ctx + 'dicCollectController/editCollectJson',       //提交表单的地址
				data:data,      //提交表单的数据
				success:function(res){
					layer.close(loading);
					if(res.stateType == 0){
						var index = parent.layer.getFrameIndex(window.name);
						parent.layer.close(index);
						parent.layer.msg(res.stateMsg, {icon: 1});
						//父级页面表格重载
						parent.layui.table.reload("collect-table");

					}else{
						layer.msg(res.stateMsg,{icon: 2});
					}
				},
				error:function(){
					layer.close(loading);
					layer.msg(res.stateMsg,{icon: 2});
				}
			});
			return false;
		});
	});
</script>

</body>
</html>