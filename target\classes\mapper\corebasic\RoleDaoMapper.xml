<?xml version="1.0" encoding="UTF-8" ?>   
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IRoleDao">
	<resultMap type="com.fwy.corebasic.entity.Core_Role" id="roleMap">
		<id property="id" column="id" javaType="Long" jdbcType="INTEGER" />
		<result property="roleName" column="rolename" javaType="String"
			jdbcType="VARCHAR" />
		<result property="iconCssClass" column="iconcssclass" javaType="String"
			jdbcType="VARCHAR" />
		<result property="isSys" column="issys" javaType="Integer"
			jdbcType="INTEGER" />
		<result property="parentId" column="parentId" />
		<result property="roleRange" column="roleRange" />
		<collection property="auths" ofType="com.fwy.corebasic.entity.Core_Auth"
			javaType="ArrayList">
			<result property="id" column="authid" javaType="Long"
				jdbcType="INTEGER" />
			<result property="authName" column="authname" javaType="String"
				jdbcType="VARCHAR" />
		</collection>
		<collection property="appointDepts" ofType="Long"
					javaType="ArrayList">
			<result property="appointDepts" column="DEPTID" />
		</collection>
	</resultMap>
	<select id="list" resultType="com.fwy.corebasic.entity.Core_Role">
		select * from core_role 
		<where>
			<if test="roleName !=null and roleName !=''">
             and ROLENAME like concat('%',#{roleName} ,'%')
            </if>
        </where>
	</select>
	<select id="getObjectById" parameterType="Long" resultMap="roleMap">
        select *
        from core_role
        left join core_authforrole on core_role.ID = core_authforrole.ROLEID
        left join core_rolefordept CR on core_role.ID = CR.ROLEID
        where ID = #{id}
	</select>
	<select id="getRoleListByIds" resultMap="roleMap">
		select * from core_role
		${ids}
	</select>
	<delete id="removeAuth" parameterType="Long">
		delete from core_authforrole
		where ROLEID=#{id}
	</delete>

	<insert id="addAuth">
		insert into
		core_authforrole(ROLEID,AUTHID,AUTHNAME)
		values(#{roleId,jdbcType=INTEGER},#{authId,jdbcType=VARCHAR},#{authName,jdbcType=VARCHAR})
	</insert>
	<insert id="add" parameterType="com.fwy.corebasic.entity.Core_Role" useGeneratedKeys="true" keyProperty="role.id">
        insert into core_role(ID, ROLENAME,PARENTID,ROLERANGE,
                              ICONCSSCLASS,
                              ISSYS)
        values (#{role.id}, #{role.roleName},#{role.parentId},#{role.roleRange},
                #{role.iconCssClass,jdbcType=VARCHAR},
                #{role.isSys})
	</insert>
	<update id="update" parameterType="com.fwy.corebasic.entity.Core_Role">
		update core_role
		<set>
			<if test="roleName!=null and roleName!='' ">
				ROLENAME = #{roleName,jdbcType=VARCHAR},
			</if>
			<if test="iconCssClass!=null and iconCssClass!='' ">
				ICONCSSCLASS = #{iconCssClass,jdbcType=VARCHAR},
			</if>
				PARENTID = #{parentId},
				ROLERANGE = #{roleRange},
			ISSYS = #{isSys,jdbcType=INTEGER}
		</set>
		where ID = #{id,jdbcType=INTEGER}
	</update>
	<delete id="delete" parameterType="Long">
		delete from core_role
		where ID
		= #{id}
	</delete>
		
	<delete id="batchDeleteRole" parameterType="String">
	delete from core_role
		where ID in
		<foreach collection="ids" item="id" separator="," open="(" close=")">
		   #{id}
		</foreach>
	</delete>
	
	<delete id="batchRemoveAuth" parameterType="String">
	delete from core_authforrole
		where ROLEID in
		<foreach collection="ids" item="id" separator="," open="(" close=")">
		   #{id}
		</foreach>
	</delete>

	<select id="getRoleListByIds2" resultMap="roleMap">
		SELECT c.* FROM core_role c
		LEFT JOIN core_authforrole t ON t.ROLEID = c.ID
		LEFT JOIN core_auth ca on c.ID = t.AUTHID
		WHERE ca.SFJY = #{authType}
		AND c.ID in
		<foreach collection="ids" item="id" separator="," open="(" close=")">
			#{id}
		</foreach>
	</select>
	<select id="roleFormSelect" resultType="com.fwy.corebasic.entity.Core_Role">
        select *
        from core_role
        <where>
            <if test="permiss != 0">
				ROLERANGE > #{permiss}
            </if>
        </where>
    </select>
	<resultMap type="com.fwy.corebasic.entity.Core_Role" id="treeMap">
		<id property="id" column="id" javaType="Long" jdbcType="INTEGER" />
		<result property="roleName" column="rolename" javaType="String"
				jdbcType="VARCHAR" />
		<result property="iconCssClass" column="iconcssclass" javaType="String"
				jdbcType="VARCHAR" />
		<result property="isSys" column="issys" javaType="Integer"
				jdbcType="INTEGER" />

		<result property="parentId" column="PARENTID" />
		<collection column="ID" property="children" ofType="com.fwy.corebasic.entity.Core_Role"
					javaType="ArrayList" select="getChildrenList" />
	</resultMap>
	<select id="getChildrenList" resultMap="treeMap">
		select * from core_role
		where PARENTID = #{parentId}
	</select>
	<select id="getTree" resultMap="treeMap">
		select * from core_role
		<where>
			<if test="roleId !=null">
				and ID = #{roleId}
			</if>
			<if test="roleId ==null">
				and PARENTID = 0
			</if>
		</where>
	</select>

	<delete id="removeDept">
		delete from core_rolefordept where ROLEID = #{roleId}
	</delete>

	<insert id="addDept">
		insert into core_rolefordept(ROLEID,DEPTID) values (#{roleId},#{deptId})
	</insert>
</mapper>