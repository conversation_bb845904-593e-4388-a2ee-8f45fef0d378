<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>处理预警记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <link rel="stylesheet" th:href="@{/homePage/css/home.css}">
    <style>
        /*.layui-layer-content {*/
        /*    background: transparent !important;*/
        /*    padding-top: 0 !important;*/
        /*}*/
        body {
            /*background: transparent !important;*/
            /*background-color: #101A39;*/
            background: #072879;
            border-radius: 0px 0px 0px 0px;
            margin: 0;
            padding: 0;
            /*color: #fff;*/
            font-family: "Microsoft YaHei", sans-serif;
            overflow-x: hidden;
            /*background-image: radial-gradient(circle at 50% 50%, rgba(0, 40, 80, 0.3) 0%, rgba(0, 23, 52, 1) 80%);*/
            background-size: cover;
            /*background-position: center;*/
        }

        /*!* 修改必填项提示文字颜色为黑色 *!*/
        /*.layui-form-verror .layui-form-msg {*/
        /*    color: #0C0C0C !important;*/
        /*    !* 强制覆盖默认颜色 *!*/
        /*}*/

        .feedback-content {
            background: #070a12;
            border-radius: 16px;
            margin: 10px auto 0 auto;
            max-width: 650px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .feedback-img-wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 10px 0 10px 0;
        }
        .feedback-img-wrapper img {
            max-width: 320px;
            max-height: 130px;
            border-radius: 8px;
            /*box-shadow: 0 0 12px #00a0e9;*/
            background: #222;
            object-fit: contain;
        }
        .feedback-form {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .feedback-row {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            width: 100%;
        }
        .feedback-row label {
            width: 110px;
            text-align: right;
            font-size: 16px;
            color: #fff;
            /*margin-right: 18px;*/
            font-weight: 500;
        }
        .feedback-row > div {
            flex: 1;
            text-align: left;
            font-size: 16px;
            color: #fff;
            max-width: 160px;
        }
        .feedback-actions {
            display: flex;
            justify-content: center;
            gap: 32px;
            margin: 10px 0;
            /*margin-top: 32px;*/
        }

        .tom-close-btn {
            position: absolute;
            right: 18px;
            top: 10px;
            z-index: 3;
            background: rgba(0,0,0,0.2);
            color: #fff;
            border: none !important;
            /*font-size: 28px;*/
            /*line-height: 28px;*/
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.2s;
            /*box-shadow: 0 0 8px #00a0e9;*/
        }
        /*.tom-close-btn:hover {*/
        /*    background: #00a0e9;*/
        /*    color: #fff;*/
        /*}*/
        .input-wrapper .layui-input,
        .layui-form-select {
            width: 150px;
        }

        .input-wrapper img {
            height: 150px;
        }

        .layui-form-item .layui-form-label {
            width: 100px;
        }

        /*.layui-form-item .layui-input-block {*/
        /*    margin-left: 140px;*/
        /*}*/

        .layui-form-item .layui-form-text {
            line-height: 38px;
            display: inline-block;
            vertical-align: middle;
        }
        /* 让 layui 渲染的下拉列表字体为黑色 */
        .layui-form-select dl dd {
            color: #0C0C0C !important;
            background: #fff !important;
        }
        .layui-form-select dl dd.layui-this {
            color: #fff !important;
            background: #00a0e9 !important;
        }
        /* 让原生 select 选中项字体为黑色 */
        select, select option {
            color: #0C0C0C !important;
            background: #fff !important;
        }

        .select-color{
            color: #0C0C0C;
        }
        .btn-p {
            background: #ffeedd;
            color: #0C0C0C;
        }

    </style>
</head>
<body>
    <div class="header_title">
<!--        <h1>处理预警记录</h1>-->
        <div  class="video-text" >告警视频</div>
        <button class="tom-close-btn" onclick="closeDialog()">×</button>
    </div>
    <div class="feedback-content">
        <div class="feedback-img-wrapper">
            <img th:src="${alertRecord.ossUrl}" alt="预警图片" />
        </div>
        <form class="layui-form feedback-form" id = "form" lay-filter="edit-form">
            <input type="hidden" name="code" autocomplete="off"
                   th:value="${alertRecord.code}">
<!--            <input type="hidden" name="alertType" autocomplete="off"-->
<!--                   th:value="${alertRecord.alertType}">-->
            <input type="hidden" name="alertObject" autocomplete="off"
                   th:value="${alertRecord.alertObject}">
            <input type="hidden" name="carNum" autocomplete="off"
                   th:value="${alertRecord.carNum}">
            <input type="hidden" name="personCode" autocomplete="off"
                   th:value="${alertRecord.personCode}">
            <input type="hidden" name="ossUrl" autocomplete="off"
                   th:value="${alertRecord.ossUrl}">

            <div class="layui-form-item feedback-row">
                <label class="layui-form-label">预警类型</label>
                <div class="layui-input-block input-wrapper">
                    <span class="layui-form-text">[[${alertRecord.alertType == 1 ? "疑似" : "黑名单"}]]</span>
                </div>
            </div>
            <div class="layui-form-item feedback-row">
                <label class="layui-form-label">预警对象</label>
                <div class="layui-input-block input-wrapper">
                    <span class="layui-form-text">[[${alertRecord.alertObject == 1 ? "人员" : "车辆"}]]</span>
                </div>
            </div>


            <div class="layui-form-item feedback-row" th:if="${alertRecord.code != null}">
                <label class="layui-form-label">预警时间</label>
                <div class="layui-input-block">
                    <span class="layui-form-text">[[${#dates.format(alertRecord.createTime, 'yyyy-MM-dd HH:mm:ss')}]]</span>
                </div>
            </div>


            <div class="layui-form-item feedback-row" >
                <label class="layui-form-label">处理状态</label>
                <div class="layui-input-block input-wrapper">
                    <select name="alertType" lay-reqText="请选择处理状态" lay-verify="required" lay-filter="alertType" id="alertType" class="select-color">
                        <option value="" >请选择处理状态</option>
                        <option value="4"  >暂不处理</option>
                        <option value="1" th:if="${alertRecord.alertType != 1}" >转疑似</option>
                        <option value="2" th:if="${alertRecord.alertType != 2}" >转黑名单</option>
                        <option value="-1"  >转白名单</option>
                        <option value="5"  >处理（预警类型不变）</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item feedback-row" th:if="${alertMarks != null}">
                <label class="layui-form-label">预警频次</label>
                <div class="layui-input-block input-wrapper">
                    <select name="markId"  id="markId" class="select-color">
                        <option value="">请选择频次</option>
<!--                        <option value="30,0">半小时</option>-->
<!--                        <option value="6,1">6小时</option>-->
<!--                        <option value="1,3">24小时</option>-->
                    </select>
                </div>
            </div>

            <div class="layui-form-item feedback-row layui-hide" id="operateFeedbackDiv">
                <label class="layui-form-label">处理备注</label>
                <div class="layui-input-block input-wrapper">
                    <input type="text" name="operateFeedback" id="operateFeedback" autocomplete="off" placeholder="请输入处理备注" class="layui-input select-color">
                </div>
            </div>


            <div class="feedback-actions">
                <button  class="layui-btn  btn-p"  lay-submit lay-filter="submit" id="subBtn">保存</button>
                <button  class="layui-btn btn-p" onclick="closeDialog()">关闭</button>
            </div>
        </form>

    </div>

<script th:inline="javascript">
    let ossUrl = /*[[${alertRecord.ossUrl}]]*/ null;
    let code = [[${alertRecord.code}]];
    let alertObject = [[${alertRecord.alertObject}]];
    let alertMarks = [[${alertMarks}]];
    console.log(alertMarks)
    if (ossUrl != null) {
        $("#image-preview").attr("src", imageBase + ossUrl);
        $("#image-preview").show();
    }else{
        $("#image-preview").attr("src");
        $("#image-preview").hide();
    }
</script>
<script>
    var formModified = false;

    layui.use(['form', 'layer'], function () {
        var form = layui.form;
        var layer = layui.layer;

        var index = parent.layer.getFrameIndex(window.name);

        // 初始化处理备注字段的显示状态
        var currentAlertType = $('#alertType').val();
        if(currentAlertType === '2') {
            $('#operateFeedbackDiv').removeClass('layui-hide');
            $('#operateFeedback').attr('lay-verify', 'required').attr('lay-reqText', '请输入处理备注');
        }

        $("body").on('change', function () {
            formModified = true; // 表单被修改
        });
        form.on('change', function () {
            formModified = true; // 表单被修改
        });

        // 表单提交监听
        form.on('submit(submit)', function (data) {
            debugger
            if(!formModified){
                // layer.msg("数据未修改")
                // return false;
            }
            var formData = new FormData();

            // 将其他字段数据也添加到formData中
            for (var key in data.field) {
                formData.append(key, data.field[key]);
            }

            $.ajax({
                // url: ctx + 'intermediary/carAlertRecordController/feedback',
                url: ctx + 'homeController/handleCarStates',
                type: 'POST',
                data: JSON.stringify(data.field),
                contentType: 'application/json',
                success: function (res) {
                    if (res.code === 200) {
                        layer.msg('处理成功');
                        // 关闭弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        // parent.loadWarningList();
                        parent.loadProcessedData();
                        parent.layer.close(index);
                        // 刷新父页面数据
                    } else {
                        layer.msg(res.msg);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Ajax 请求发生错误:', error);
                }
            });

            return false; // 阻止表单提交
        });

        form.render();

        $("#markId").empty().append('<option value="">请选择</option>');

        // 遍历后端返回的 list 集合数据，动态添加 option
        $.each(alertMarks, function(index, item){
            console.log(item.dicValue);
            console.log(item.disCription);
            $('#markId').append(new Option(item.disCription , item.dicValue,));
        });

        // 重新渲染 select，使 layui 的样式和功能生效
        form.render('select');

        // 此弹窗将在60秒后自动关闭
        setTimeout(function() {
            parent.loadProcessedData();
            parent.layer.close(index);
        }, 60000);

        // 监听处理状态的选择
        form.on('select(alertType)', function(data){
            var value = data.value;
            if(value === '2') {
                $('#operateFeedbackDiv').removeClass('layui-hide');
                $('#operateFeedback').attr('lay-verify', 'required').attr('lay-reqText', '请输入处理备注');
            } else {
                $('#operateFeedbackDiv').addClass('layui-hide');
                $('#operateFeedback').removeAttr('lay-verify').removeAttr('lay-reqText');
            }
            form.render();
        });

    });

    // 异步更新状态
    $.ajax({
        url: ctx + 'homeController/loadCarStates?code='+code,
        type: 'GET',
        dataType: 'json',
        success: function (res) {
            if (res.code === 200) {
                console.log('数据更新成功');
            } else {
                console.log('数据跟新失败:', res.msg);
            }
        },
        error: function (xhr, status, error) {
            console.log('Ajax 请求发生错误:', error);
        }
    });

    // 重置表单及图像
    function resetForm() {
        var form = layui.form;
        var $ = layui.jquery;

        // 重置表单项
        form.val("edit-form", {});
        $("#ossUrl").val(ossUrl);
        // 重置图像
        if (ossUrl != null) {
            $("#image-preview").attr("src", ctx + ossUrl);
            $("#image-preview").show();
        }else{
            $("#image-preview").attr("src");
            $("#image-preview").hide();
        }
        formModified = false;
    }

    function renderSelect(alertMarks){

        $("#markId").empty().append('<option value="">请选择</option>');

        // 遍历后端返回的 list 集合数据，动态添加 option
        $.each(alertMarks, function(index, item){
            console.log(item.dicValue);
            console.log(item.disCription);
            $('#markId').append(new Option(item.dicValue, item.disCription));
        });

        // 重新渲染 select，使 layui 的样式和功能生效
        form.render('select');
    }

    //显示大图片
    window.onload = function () {
        show_img()
    }

    function show_img() {
        var $ = layui.jquery;
        layer.photos(
            {
                photos: '#layer-photos-demo',
                anim: 5,
                tab: function (pic, layero) {
                    $(document).on("mousewheel", ".layui-layer-photos", function (ev) {
                        var oImg = this;
                        var ev = event || window.event;//返回WheelEvent
                        //ev.preventDefault();
                        var delta = ev.detail ? ev.detail > 0 : ev.wheelDelta < 0;
                        var ratioL = (ev.clientX - oImg.offsetLeft) / oImg.offsetWidth,
                            ratioT = (ev.clientY - oImg.offsetTop) / oImg.offsetHeight,
                            ratioDelta = !delta ? 1 + 0.1 : 1 - 0.1,
                            w = parseInt(oImg.offsetWidth * ratioDelta),
                            h = parseInt(oImg.offsetHeight * ratioDelta),
                            l = Math.round(ev.clientX - (w * ratioL)),
                            t = Math.round(ev.clientY - (h * ratioT));
                        $(".layui-layer-photos").css({
                            width: w, height: h
                            , left: l, top: t
                        });
                        $("#layui-layer-photos").css({width: w, height: h});
                        $("#layui-layer-photos>img").css({width: w, height: h});
                    });
                }
            });
    }

    function closeDialog() {
        if (parent && parent.layer) {
            var index = parent.layer.getFrameIndex(window.name);

            // 先关闭弹窗，再刷新数据
            parent.layer.close(index);

            // 刷新父页面数据
            if (parent.loadProcessedData) {
                parent.loadProcessedData();
            }
            if (parent.loadWarningList) {
                parent.loadWarningList(alertObject);
            }
            if (parent.loadWarningData) {
                parent.loadWarningData();
            }

            // 恢复父页面的定时任务
            if (parent.resumeRefreshTimer) {
                parent.resumeRefreshTimer();
            }

        } else {
            window.close();
        }
    }

</script>
</body>
</html>