<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IApiLogDao">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fwy.corebasic.entity.ApiLog">
        <result column="ID" property="id"/>
        <result column="MODULE" property="module"/>
        <result column="TYPE" property="type"/>
        <result column="DESCRIPTION" property="description"/>
        <result column="REQU_PARAM" property="requParam"/>
        <result column="RESP_PARAM" property="respParam"/>
        <result column="USER_ID" property="userId"/>
        <result column="USER_NAME" property="userName"/>
        <result column="METHOD" property="method"/>
        <result column="URI" property="uri"/>
        <result column="IP" property="ip"/>
        <result column="CREATE_TIME" property="createTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result column="VER" property="ver"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID,
        MODULE,
        TYPE,
        DESCRIPTION,
        REQU_PARAM,
        RESP_PARAM,
        USER_ID,
        USER_NAME,
        METHOD,
        URI,
        IP,
        CREATE_TIME,
        VER
    </sql>
    <insert id="save">
        insert into api_log
        VALUES (#{id},
                #{module},
                #{type},
                #{description},
                #{requParam},
                #{respParam},
                #{userId},
                #{userName},
                #{method},
                #{uri},
                #{ip},
                #{createTime},
                #{ver})
    </insert>
    <select id="getApiList" resultMap="BaseResultMap">
        select ID, MODULE, TYPE, DESCRIPTION, USER_ID, USER_NAME, METHOD, URI, IP, CREATE_TIME, VER
        from api_log l
        <where>
            <if test="beginTime != null and beginTime != ''">
                DATE_FORMAT(l.CREATE_TIME, '%Y-%m-%d') &gt;= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and DATE_FORMAT(l.CREATE_TIME, '%Y-%m-%d') &lt;= #{endTime}
            </if>
        </where>

        order by l.CREATE_TIME desc
    </select>
    <!-- 根据主键获取单个对象 -->
    <select id="getObjectById" parameterType="String" resultMap="BaseResultMap">
        select *
        from api_log l
        where l.ID = #{id}
    </select>
</mapper>
