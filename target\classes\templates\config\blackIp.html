<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>黑名单管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
           <form class="layui-form" action="">

                    <div class="layui-inline">
                        <label class="layui-form-label w-auto" style="width: unset;padding: 9px 1px 9px 0px;">受限ip：</label>
                        <div class="layui-input-inline mr0">
                            <input id="ip" class="layui-input" type="text" placeholder="请输入受限ip">
                        </div>
                    </div>
               <div class="layui-inline">
                   <label class="layui-form-label">开始日期：</label>
                   <div class="layui-input-inline">
                       <input class="layui-input" id="startTime" name="startTime" autocomplete="off"
                              placeholder="请输入开始日期" readonly>
                   </div>
               </div>
               <div class="layui-inline">
                   <label class="layui-form-label">结束日期：</label>
                   <div class="layui-input-inline">
                       <input class="layui-input" id="endTime" name="endTime" autocomplete="off"
                              placeholder="请输入结束日期" readonly>
                   </div>
               </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                        <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                        <button type="reset" class="layui-btn layui-btn-primary" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                    </div>
            </form>
            </blockquote>
            <table class="layui-hide" id="blackIp-table" lay-filter="blackIp-table"></table>
            <script type="text/html" id="table-toolbar-top">
                <div class="layui-btn-container">
                    <!--<button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe608;</i>增加</button>
                    <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="batchDel"><i class="layui-icon">&#xe640;</i>删除</button>-->
                </div>
            </script>

            <script type="text/html" id="table-toolbar">
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
            </script>
        </div>
    </div>
</div>
<script type="text/javascript">
    function timeRender(attrName, value, minOrMax, type) {
        //每当日期被选择完的时候重新渲染另外一个日期组件  限制最大或者是最小值  就不会出现日期重叠的情况。
        var date = new Date();
        layui.use(['laydate'], function () {
            var laydate = layui.laydate;
            $('input[name$=' + attrName + ']').each(function () {
                $(this).removeAttr('lay-key');
                var clone = $(this).clone().appendTo($(this).parent().empty())[0];//解决最大和最少值无法重新渲染问题
                if (type == 1) {
                    //表示开始日期
                    var secDate = laydate.render({
                        elem: clone,
                        value: value,
                        btns: ['clear', 'confirm'],
                        max: minOrMax == '' || minOrMax == null ? date.getFullYear() + 1 + '-12-31' : minOrMax,  //如果为空或者为null会限制当天选择  给一个初始值
                        // type: 'datetime',
                        done: function (value, date, endDate) {
                            this.value=value
                            this.elem.val(value)
                            // if (value!=null&&value!=''){
                            //     value=new Date(value)
                            //     value.setDate(value.getDate()+1)
                            // }
                            timeRender("endTime", $("#endTime").val(), value, 0)
                            $("#endTime").focus();
                        },
                        change: function (value, date) { //监听日期被切换
                            var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            start = new Date(Date.parse(start));
                            var end = $("#endTime").val().replace("-", "/");
                            end = new Date(Date.parse(end));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                secDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                } else {
                    var secDate = laydate.render({
                        elem: clone,
                        value: value,
                        min: minOrMax == '' || minOrMax == null ? '2010-12-31' : minOrMax, //初始最小限制时间
                        // type: 'datetime',
                        btns: ['clear', 'confirm'],
                        done: function (value, date, endDate) {
                            this.value=value;
                            this.elem.val(value);
                            timeRender('startTime', $("#startTime").val(), value, 1)
                        },
                        change: function (value, date) { //监听日期被切换
                            var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            end = new Date(Date.parse(end));
                            var start = $("#startTime").val().replace("-", "/");
                            start = new Date(Date.parse(start));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                secDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                }

            })
        });
    }

    $(function(){
        layui.use(['table','layer','laydate','jquery', 'form'] , function(){
            var admin = layui.admin;
            var $ = layui.jquery;
            var layer = layui.layer;
            var form = layui.form;
            var table = layui.table;
            var laydate = layui.laydate;

            var ends1 = laydate.render({
                elem: '#endTime',
                // type: 'datetime',
                btns: ['clear', 'confirm'],
                done: function (value, date, endDate) {
                    this.value=value
                    this.elem.val(value)
                    // if (value!=null&&value!=''){
                    //     value=new Date(value)
                    //     value.setDate(value.getDate()-1)
                    // }
                    timeRender('startTime', $("#startTime").val(), value, 1)
                },
                change: function (value, date) { //监听日期被切换
                    var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds;
                    end = new Date(Date.parse(end));
                    var start = $("#startTime").val().replace("-", "/");
                    start = new Date(Date.parse(start));
                    if (start > end) { //点击2017年8月15日，弹出提示语
                        ends1.hint('开始时间不得大于结束时间');
                    }
                    return false;
                }
            });
            var starts1 = laydate.render({
                elem: '#startTime',
                // type: 'datetime',
                // max: 'null',
                btns: ['clear', 'confirm'],
                done: function (value, date, endDate) {
                    this.value=value;
                    this.elem.val(value);
                    timeRender('endTime', $("#endTime").val(), value, 0);
                    $("#endTime").focus();
                },
                change: function (value, date) { //监听日期被切换
                    var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                    start = new Date(Date.parse(start));
                    var end = $("#endTime").val().replace("-", "/");
                    end = new Date(Date.parse(end));
                    if (start > end) {
                        starts1.hint('开始时间不得大于结束时间');
                    }
                    return false;
                }
            });

            table.render({
                elem: '#blackIp-table'
                ,url: ctx + 'blackIpController/findPage'
                ,toolbar: '#table-toolbar-top'
                ,defaultToolbar: ['filter']
                ,title: '黑名单管理'
                ,cols: [
                    [
                       // {type: 'checkbox'}
                        // ,{title:'操作',width:80,toolbar: '#table-toolbar'}
                       {field: "ip", title: '受限ip',align:'center'}
                        ,{field:'cjsj', title:'受限开始时间',align:'center'}
                        ,{field:'jssj', title:'受限结束时间',align:'center'}
                        ,{field:'reason', title:'受限原因',align:'center'}
                    ]
                ]
                ,request: {
                    pageName: 'pageNum' //页码的参数名称，默认：page
                    ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
                }
                ,parseData: function(res){ //res 即为原始返回的数据
                    return {
                        "code": res.code, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.data.total, //解析数据长度
                        "data": res.data.list //解析数据列表
                    }
                }
                ,page: true
            });
            form.render();

            //搜索及重置按钮
            $("#searchBtn").click(function(){
                table.reload('blackIp-table',{
                    where: { //设定异步数据接口的额外参数，任意设
                        ip: $("#ip").val(),
                        startTime:$("#startTime").val(),
                        endTime:$("#endTime").val()
                    }
                    ,page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            })
            //搜索及重置按钮
            $("#unsetBtn").click(function(){
                location.reload();
                table.reload('blackIp-table', {
                    where: null
                    ,page: {
                        curr: 1 //重新从第 1 页开始
                    }
                }); //只重载数据
                return false;
            })
            //监听行工具事件
            table.on('tool(blackIp-table)', function(obj){
                var data = obj.data;
                if(obj.event === 'delete'){
                    layer.confirm('是否删除',{btn:['确定','取消'],title:'提示'},function(){
                        var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
                        var id = data.id;
                        $.ajax({
                            method:"POST",
                            url : ctx + 'blackIpController/delete',
                            data : {"id":id},
                            async : true,
                            cache : false,
                            success : function(res){
                                layer.close(loading);
                                if(res.stateType == 0){
                                    table.reload("blackIp-table");
                                    layer.msg(res.stateMsg,{icon: 1});
                                }else{
                                    layer.msg(res.stateMsg,{icon: 2});
                                }
                            },
                            error : function(res){
                                layer.close(loading);
                                layer.msg(res.stateMsg,{icon: 2});
                            }
                        })
                    })
                }
            });

            //头工具栏事件
            table.on('toolbar(blackIp-table)', function(obj){
                var checkStatus = table.checkStatus(obj.config.id);
                var data = JSON.stringify(checkStatus.data);
                switch(obj.event){
                    case 'add':
                        layer.open({
                            type: 2,
                            title: "添加黑名单",
                            shadeClose: true,
                            area: ['500px', '300px'],
                            btn: ['保存','关闭'],
                            content: ctx + 'blackIpController/toAddPage',
                            yes: function(index, layero){ //当前层索引、当前层DOM对象
                                //提交表单
                                var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                                submit.click();// 触发提交监听
                                return false;
                            },
                            offset: [
                                0.1*($(window).height()-400)
                            ]
                        });
                        break;
                    case 'batchDel':
                        var data = checkStatus.data;
                        if (data.length < 1) {
                            layer.msg('请至少选中一行数据', {
                                icon: 2
                            });
                            return;
                        }
                        var ids = [];
                        for (var i = 0; i < data.length; i++) {
                            ids.push(data[i].id);
                        }
                        layer.confirm('您确定要删除吗？', function (index) {
                            var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                            $.ajax({
                                url: ctx + 'blackIpController/delBatch',
                                method: 'post',
                                data: {"ids": ids},
                                dataType: 'JSON',
                                success: function (res) {
                                    layer.close(loading);
                                    if (res.stateType != 0) {
                                        layer.alert(res.stateMsg, {icon: 2});
                                    } else {
                                        table.reload('blackIp-table'); //只重载数据
                                        layer.msg(res.stateMsg, {icon: 1});
                                    }
                                },
                                error: function (res) {
                                    layer.close(loading);
                                    layer.alert(res.stateMsg, {icon: 2, anim: 6});
                                }
                            });
                            layer.close(index);
                        });
                        break;
                };
            });

        });

    });
    
</script>
</body>
</html>