<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.ITaskDao">
    <select id="queryScheduleTriggerLst" parameterType="com.fwy.corebasic.entity.ScheduleTrigger"
            resultType="com.fwy.corebasic.entity.ScheduleTrigger">
        select ID,
               NAME,
               CRON,
               STATUS,
               JOB_NAME,
               JOB_GROUP,
               T.DESCRIBE,
               CREATEBY,
               CREATETIME
        from t_schedule_trigger T
        <where>
            <if test="name != null and name != ''">
                and NAME like CONCAT(CONCAT('%', trim(#{name})), '%')
            </if>
            <if test="job_name != null and job_name != ''">
                and JOB_NAME like CONCAT(CONCAT('%', trim(#{job_name})), '%')
            </if>
        </where>
    </select>
    <select id="getObjectByQuery" parameterType="com.fwy.corebasic.entity.ScheduleTrigger"
            resultType="com.fwy.corebasic.entity.ScheduleTrigger">
        select ID,
               NAME,
               CRON,
               STATUS,
               JOB_NAME,
               JOB_GROUP,
                T.DESCRIBE,
               CREATEBY,
               CREATETIME
        from t_schedule_trigger T
        <where>
            <if test="name != null and name != ''">
                NAME = #{name}
            </if>
            <if test="job_name != null and job_name != ''">
                and JOB_NAME = #{job_name}
            </if>
        </where>
    </select>
    <!-- 任务显示状态 -->
    <update id="show">
        update t_schedule_trigger
        set STATUS=#{status}
        where ID = #{id}
    </update>
    <!--根据id获取任务-->
    <select id="getObjectById" resultType="com.fwy.corebasic.entity.ScheduleTrigger">
        select ID,
               NAME,
               CRON,
               STATUS,
               JOB_NAME,
               JOB_GROUP,
               T.DESCRIBE,
               CREATEBY,
               CREATETIME
        from t_schedule_trigger T
        where ID = #{id}
    </select>
    <insert id="save" parameterType="com.fwy.corebasic.entity.ScheduleTrigger" useGeneratedKeys="true">
        insert into t_schedule_trigger(ID, NAME, CRON, STATUS, JOB_NAME, JOB_GROUP, `DESCRIBE`, CREATEBY, CREATETIME)
        values (#{id,jdbcType=INTEGER},
                #{name,jdbcType=VARCHAR},
                #{cron,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                #{job_name,jdbcType=VARCHAR},
                #{job_group,jdbcType=VARCHAR},
                #{describe,jdbcType=VARCHAR},
                #{createby,jdbcType=VARCHAR},
                sysdate())
    </insert>
    <!--修改 -->
    <update id="update" parameterType="com.fwy.corebasic.entity.ScheduleTrigger">
        update t_schedule_trigger
        set NAME=#{name,jdbcType=VARCHAR},
            CRON=#{cron,jdbcType=VARCHAR},
            STATUS=#{status,jdbcType=VARCHAR},
            JOB_NAME=#{job_name,jdbcType=VARCHAR},
            JOB_GROUP=#{job_group,jdbcType=INTEGER},
            `DESCRIBE`=#{describe,jdbcType=INTEGER}
        where ID =
              #{id}
    </update>
    <!--删除-->
    <delete id="delete" parameterType="long">
        delete
        from t_schedule_trigger
        where ID = #{id}
    </delete>
    <!--批量删除-->
    <delete id="batchDeleteRole" parameterType="string">
        delete
        from t_schedule_trigger
                where ID in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>
</mapper>