<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title id="sysTitle1">[[${application.sysBaseInfo.sysName}]]</title>
    <input type="hidden" id="logo" th:value="${application.sysBaseInfo?.imgSrc}"/>

    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">

    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}"
          media="all">
    <link rel="stylesheet" th:href="@{/admin/layui/css/admin.css}"
          media="all">
    <link rel="stylesheet" th:href="@{/admin/layui/css/message.css}"
          media="all">
    <link rel="shortcut icon" type="image/x-icon"
          th:href="@{/favicon.ico}" media="screen"/>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script th:src="@{/admin/layui/layui.js}" type="text/javascript"></script>
    <script th:src="@{/plugins/jquery/jquery-3.4.1.min.js}"></script>
    <script th:inline="javascript" th:src="@{/scripts/main/index.js}"></script>
    <style type="text/css">
        .layui-header .layui-layout-right .layui-badge-dot {
            margin-left: -7px;
            margin-top: -11px;
        }
        .warning{
            color: #0a84ff;
        }
    </style>
    <script th:inline="javascript">
        var codes = [];
        isClick = false, isCanPlay = false;
        var audio;
        (function ($) {
            $(function () {  //session失效的时候 点击按钮 会在iframe里面再开一个新的窗口  这里判断index是不是顶级窗口  否则  将顶级窗口替换成index
                if (top.location !== self.location) {
                    top.location = self.location;
                }
            });
            var imgurl = 'url(' + $('#logo').val() + ')';

            if ($('#logo').val != "") {
                $("#lay-logo").css({"width": "60px", "background-image": imgurl, "background-color": "#0C0C0C"});
            }
        })(jQuery);
        $(function () {
            var body = document.getElementsByTagName('body')[0];
            var url = ctx + '/audio/auditSound.mp3';
            audio = new Audio(url);
            body.addEventListener('click', function () {
                isClick = true;

            });
            //这里获取个人消息  当用户点开之后 认为用户已经读取了消息  更新消息的状态
            getAlertList()
            //setTimeout(getAlertList, 5000);
            setInterval(getAlertList, 30000);
        });

        function getAlertList() {
            $.get(ctx + "/reportController/getAlertList", function (data) {
                var json = data.data;
                var $notice = $("#notice");
                var $messageItem = $("#messageItem");
                var msg = data.msg;
                if (data.code == 0 && json && json.length > 0) {
                    var newCodes = [];
                    var i = 0;
                    var oldCode = "";
                    var count = json.length
                    if (count > 0) {
                        $("#redNotice").append('<span id="red-dot" class="layui-badge-dot"></span>')
                    } else {
                        $("#red-dot").remove();
                    }
                    isCanPlay = false;
                    isClick = true;
                    var people = 0;
                    var cars = 0;
                    debugger;
                    for (var i=0;i<count;i++){
                        var type = json[i].alertObject;
                        if (!codes.includes(json[i].code)){
                                isCanPlay = true;
                        }
                        newCodes.push(json[i].code);
                        if (type==1){
                            people++;
                        }
                        else {
                            cars++;
                        }
                    }
                    codes = newCodes;
                    /*people = people==0?'':people;
                    cars = cars==0?'':cars;*/
                    remindTypeNew(people,cars);
                } else {
                    $messageItem.html('<div className="pear-notice-item">\n' +
                        '<div style="display:inline-block;padding-left: 60px">暂无通知消息。。。。</div>\n' +
                        ' </div>');
/*
                    setTimeout(getAlertList, 5000);
*/
                    $("#red-dot").remove();
                }

            })
        }

        function remindTypeNew(msg,msg1) {
            //右下角弹框
            layui.use(['layedit', 'laydate'], function () {
                var layer = layui.layer;
                var str = '';
                if(msg>0){
                    str += '<a onclick="showPeopleWarns()" class="warning" href="#">当前待处理人像预警数:'+msg + ',点击前往处理！'+'</a><br/>';
                }
                if (msg1>0){
                    str +='<a onclick="showCarWarns()" class="warning" href="#">当前待处理车辆预警数:'+msg1 + ',点击前往处理！'+'</a>'
                }
                layer.msg(str,
                    {icon: 3, time: 30000, shift: 5, offset: "rb", area: ['350px', '100px']}, function () {
                    });
            });
            try {
                if (isCanPlay) {
                    audio.play();
                    isCanPlay = false;
                }
            } catch (err) {
                console.log(err)
                console.log(err.message);
            }

        }

        function showCarWarns(){
            window.parent.changTabs( ctx +'carAlertRecordController/alertRecordList','','预警车辆列表');
        }

        function showPeopleWarns(){
            window.parent.changTabs( ctx +'alertRecordController/alertRecordList','','人像预警管理');
        }

        /*function getAlertList() {
            $.get(ctx + "/reportController/getAlertList", function (data) {
                var json = data.data;
                var $notice = $("#notice");
                var $messageItem = $("#messageItem");
                var msg = data.msg;

                if (data.code == 0 && json && json.length > 0) {
                    var i = 0;
                    var oldCode = "";
                    var count = json.length
                    if (count > 0) {
                        $("#redNotice").append('<span id="red-dot" class="layui-badge-dot"></span>')
                    } else {
                        $("#red-dot").remove();
                    }
                    isCanPlay = true
                    isClick = true;

                    function handleNextAlert() {
                        if (i >= 0 && i < json.length) {
                            if (json[i].code != oldCode) {
                                isCanPlay = true;
                                remindType(msg + count);
                                layui.use(['form', 'layedit', 'laydate', 'jquery', 'element'], function () {
                                    layui.element.render();
                                });
                                $messageItem.html('<div className="pear-notice-item">\n' +
                                    '<div style="display:inline-block;padding-left: 60px">待处理预警：' + count + '</div>\n' +
                                    ' </div>')
                                if (json[i].alertObject == 2) {
                                    var alertUrl = "carAlertRecordController/toFeedbackPage/" + json[i].code;
                                } else {
                                    var alertUrl = "alertRecordController/toFeedbackPage/" + json[i].code;
                                }
                                oldCode = json[i].code;
                                layer.open({
                                    type: 2,
                                    title: "预警处理",
                                    shadeClose: false,
                                    area: ['700px', '500px'],
                                    btn: ['保存', '取消','一键处理'],
                                    content: ctx + alertUrl,
                                    yes: function (index, layero) {
                                        var submit = layero.find('iframe').contents().find("#subBtn");
                                        submit.click();
                                        var body = layer.getChildFrame('body', index);
                                        var stateId = body.find("#stateId").val();
                                        var operateFeedback = body.find("#operateFeedback").val();
                                        if (stateId == "" || operateFeedback == ""){
                                            return false;
                                        }else{
                                            i++;
                                            count--;
                                            isCanPlay = false;
                                            handleNextAlert();
                                            try {
                                                audio.pause()
                                            } catch (err) {
                                                console.log(err)
                                                console.log(err.message);
                                            }
                                        }
                                    },
                                    cancel: function (index) {
                                        // 弹窗关闭事件的处理逻辑
                                        i++;
                                        isCanPlay = false;
                                        try {
                                            audio.pause()
                                        } catch (err) {
                                            console.log(err)
                                            console.log(err.message);
                                        }
                                        handleNextAlert();
                                    },
                                    btn2: function (index, layero) {
                                        // 取消按钮的回调函数
                                        i++;
                                        isCanPlay = false;
                                        try {
                                            audio.pause()
                                        } catch (err) {
                                            console.log(err)
                                            console.log(err.message);
                                        }
                                        handleNextAlert();
                                        // return false; // 阻止弹窗关闭
                                    },
                                    btn3: function (index, layero) {
                                        $.ajax({
                                            url: ctx + 'carAlertRecordController/updateFeedback',
                                            method: 'post',
                                            data: JSON.stringify({"stateId": "0"}),
                                            contentType: 'application/json',
                                            success: function (res) {
                                                if (res.code != 500) {
                                                    layer.alert(res.msg,{icon: 1});
                                                    layer.close(index);
                                                } else {
                                                    layer.msg(res.msg, {icon: 1});
                                                    return false;
                                                }
                                            },
                                            error: function (res) {
                                                layer.msg(res.msg, {icon: 1});
                                                return false;
                                            }
                                        });
                                        // return false; // 阻止弹窗关闭
                                    }
                                });

                            } else {
                                handleNextAlert(); // 继续处理下一个数据
                            }
                        } else {
                            if (count <= 0) {
                                $messageItem.html('<div className="pear-notice-item">\n' +
                                    '<div style="display:inline-block;padding-left: 60px">暂无通知消息。。。。</div>\n' +
                                    ' </div>');
                                $("#red-dot").remove();
                            }
                            setTimeout(getAlertList, 5000);
                        }
                    }

                    handleNextAlert(); // 开始处理第一个数据
                } else {
                    $messageItem.html('<div className="pear-notice-item">\n' +
                        '<div style="display:inline-block;padding-left: 60px">暂无通知消息。。。。</div>\n' +
                        ' </div>');
                    setTimeout(getAlertList, 5000);
                    $("#red-dot").remove();
                }

            })
        }*/

        function remindType(msg) {
            //右下角弹框
            layui.use(['layedit', 'laydate'], function () {
                var layer = layui.layer;
                layer.msg(msg + '请您尽快处理！',
                    {icon: 3, time: 10000, shift: 5, offset: "rb", area: ['350px', '100px']}, function () {
                    });
            });
            try {
                if (isCanPlay) {
                    audio.play();
                }
            } catch (err) {
                console.log(err)
                console.log(err.message);
            }

        }

        function openPage(obj) {
            //点击后认为消息已经发生读过了
            var url = $(obj).attr("url");
            $("#red-dot").remove();
            $(obj).remove();
            console.log($(obj))
            top.layui.index.openTabsPage(ctx + url, '预警处理');
        }
    </script>
    <script th:inline="javascript">
        var ctx = [[${#servletContext.contextPath}]] + '/';
    </script>
    <script>
        //JavaScript代码区域
        /* layui.use('element', function() {
            var element = layui.element;
        });  */

        layui.config({
            base: 'admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        });
        layui.use('index', function () {
            var index = layui.index;
        });

    </script>
</head>
<body class="layui-layout-body">
<input id="aaaaaa" value="759780115" type="hidden">
<div id="LAY_app">
    <div class="layui-layout layui-layout-admin">
        <div class="layui-header">
            <!-- 头部区域 -->
            <ul class="layui-nav layui-layout-left">
                <li class="layui-nav-item layadmin-flexible" lay-unselect><a
                        href="javascript:;" layadmin-event="flexible" title="侧边伸缩"> <i
                        class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
                </a></li>
                <!-- <li class="layui-nav-item layui-hide-xs" lay-unselect><a
                    href="http://www.layui.com/admin/" target="_blank" title="前台">
                        <i class="layui-icon layui-icon-website"></i>
                </a></li> -->
                <li class="layui-nav-item" lay-unselect><a href="javascript:;"
                                                           layadmin-event="refresh" title="刷新"> <i
                        class="layui-icon layui-icon-refresh-3"></i>
                </a></li>
                <!--<li class="layui-nav-item layui-hide-xs" lay-unselect><input
                    type="text" placeholder="搜索..." autocomplete="off"
                    class="layui-input layui-input-search" layadmin-event="serach"
                    lay-action="template/search.html?keywords="></li>-->
            </ul>
            <ul class="layui-nav layui-layout-right"
                lay-filter="layadmin-layout-right">
                <li class="layui-nav-item layui-hide-xs message">
                <li class="layui-nav-item pear-this" lay-unselect="">

                    <a id="redNotice" href="#" class="notice layui-icon layui-icon-notice">

                    </a>

                    <div id="notice" class="layui-nav-child layui-tab pear-notice layui-anim layui-anim-upbit"
                         style="margin-top: 0px;;left: -200px;">
                        <ul class="layui-tab-title">
                            <li class="">通知</li>
                        </ul>
                        <div class="layui-tab-content" style="height:250px;overflow-x: hidden;">
                            <div id="messageItem" class="layui-tab-item layui-show">


                            </div>
                        </div>
                    </div>
                </li>
                </li>
                <li class="layui-nav-item layui-hide-xs" lay-unselect><a
                        href="javascript:;" layadmin-event="theme"> <i
                        class="layui-icon layui-icon-theme"></i>
                </a></li>
                <li class="layui-nav-item layui-hide-xs" lay-unselect><a
                        href="javascript:;" layadmin-event="note"> <i
                        class="layui-icon layui-icon-note"></i>
                </a></li>
                <li class="layui-nav-item layui-hide-xs" lay-unselect><a
                        href="javascript:;" layadmin-event="fullscreen"> <i
                        class="layui-icon layui-icon-screen-full"></i>
                </a></li>
                <li class="layui-nav-item" lay-unselect><a href="javascript:;">
                    <cite>[[${session.user.userName}]]</cite>
                </a>
                    <dl class="layui-nav-child">
                        <dd style="text-align: center">
                            <a lay-href="userController/showInfo">个人信息</a>
                        </dd>
                        <dd>
                            <a lay-href="pwController/pwinfo">修改密码</a>
                        </dd>
                        <dd style="text-align: center;">
                            <a th:href="@{/logout}">退出</a>
                        </dd>
                    </dl>
                </li>

                <li class="layui-nav-item layui-hide-xs" lay-unselect><a
                        href="javascript:;" layadmin-event="about"><i
                        class="layui-icon layui-icon-more-vertical"></i></a></li>
                <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm"
                    lay-unselect><a href="javascript:;" layadmin-event="more"><i
                        class="layui-icon layui-icon-more-vertical"></i></a></li>
            </ul>
        </div>

        <!-- 侧边菜单 -->
        <div class="layui-side layui-side-menu">
            <div class="layui-side-scroll">
                <div class="layui-logo" id="lay-logo" lay-href="home/console.html">
                    <span id="sysTitle">[[${application.sysBaseInfo.sysName}]]</span>
                </div>

                <ul class="layui-nav layui-nav-tree" lay-shrink="all"
                    id="nav" lay-filter="layadmin-system-side-menu">

                </ul>
            </div>
        </div>

        <!-- 页面标签 -->
        <div class="layadmin-pagetabs" id="LAY_app_tabs">
            <div class="layui-icon layadmin-tabs-control layui-icon-prev"
                 layadmin-event="leftPage"></div>
            <div class="layui-icon layadmin-tabs-control layui-icon-next"
                 layadmin-event="rightPage"></div>
            <div class="layui-icon layadmin-tabs-control layui-icon-down">
                <ul class="layui-nav layadmin-tabs-select"
                    lay-filter="layadmin-pagetabs-nav">
                    <li class="layui-nav-item" lay-unselect><a
                            href="javascript:;"></a>
                        <dl class="layui-nav-child layui-anim-fadein">
                            <dd layadmin-event="closeThisTabs">
                                <a href="javascript:;">关闭当前标签页</a>
                            </dd>
                            <dd layadmin-event="closeOtherTabs">
                                <a href="javascript:;">关闭其它标签页</a>
                            </dd>
                            <dd layadmin-event="closeAllTabs">
                                <a href="javascript:;">关闭全部标签页</a>
                            </dd>
                        </dl>
                    </li>
                </ul>
            </div>
            <div class="layui-tab" lay-unauto lay-allowClose="true"
                 lay-filter="layadmin-layout-tabs">
                <ul class="layui-tab-title" id="LAY_app_tabsheader">
                    <li lay-id="home/console.html" lay-attr="home/console.html"
                        class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
                </ul>
            </div>
        </div>


        <!-- 主体内容 -->
        <div class="layui-body" id="LAY_app_body">
            <div class="layadmin-tabsbody-item layui-show">
                <iframe src="" frameborder="0"
                        class="layadmin-iframe" id="firstFrame"></iframe>
            </div>
        </div>
        <!-- <div class="layui-footer">
            底部固定区域
            © layui.com - 底部固定区域
        </div> -->
        <!-- 辅助元素，一般用于移动设备下遮罩 -->
        <div class="layadmin-body-shade" layadmin-event="shade"></div>
    </div>
</div>


<style id="LAY_layadmin_theme">
    .layui-side-menu, .layadmin-pagetabs .layui-tab-title li:after,
    .layadmin-pagetabs .layui-tab-title li.layui-this:after,
    .layui-layer-admin .layui-layer-title, .layadmin-side-shrink .layui-side-menu .layui-nav > .layui-nav-item > .layui-nav-child {
        background-color: #344058 !important;
    }

    .layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a,
    .layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a {
        background-color: #1E9FFF !important;
    }

    .layui-layout-admin .layui-logo {
        background-color: #0085E8 !important;
    }

    .layui-layout-admin .layui-header {
        background-color: #1E9FFF;
    }

    .layui-layout-admin .layui-header a, .layui-layout-admin .layui-header a cite {
        color: #f8f8f8;
    }

    .layui-layout-admin .layui-header a:hover {
        color: #fff;
    }

    .layui-layout-admin .layui-header .layui-nav .layui-nav-more {
        border-top-color: #fbfbfb;
    }

    .layui-layout-admin .layui-header .layui-nav .layui-nav-mored {
        border-color: transparent;
        border-bottom-color: #fbfbfb;
    }

    .layui-layout-admin .layui-header .layui-nav .layui-this:after,
    .layui-layout-admin .layui-header .layui-nav-bar {
        background-color: #fff;
        background-color: rgba(255, 255, 255, .5);
    }

    .layadmin-pagetabs .layui-tab-title li:after {
        display: none;
    }
</style>
<script th:inline="javascript">
    var user = [[${session.user}]]
    layui.use(['form', 'layedit', 'laydate', 'jquery', 'element'], function () {
        var menuData = JSON.parse([[${session.menuList}]]);
        var ctx = [[${#servletContext.contextPath}]];
        menu.init(menuData, ctx);
        var form = layui.form, element = layui.element, layer = layui.layer, $ = jQuery = layui.$;
        element.render();
        //获取中文的字节长度  假设修改oracle字符集 这里可以修改成 return this.length + (cArr == null ? 0 : cArr.length*2);
        window.getBytes = function (value) {
            var cArr = value.match(/[^\x00-\xff]/ig);
            return value.length + (cArr == null ? 0 : cArr.length);
        };
        window.changTabs = function (refreshurl, closeurl, title) {
            var tabs = window.parent.document.getElementById("LAY_app_tabsheader").children;
            parent.layui.admin.events.closeThisTabs();
            //refreshurl = '/coreBasic/deptController/deptList';
            //closeurl = 'deptController/addinfo';http://localhost:8885/jmt/userController/userJson?pageNum=1&pageSize=10
            // layui.index.openTabsPage("http://localhost:8885/jmt/photos/loading.html", title);
            // parent.layui.admin.events.closeThisTabs();
            layui.index.openTabsPage(refreshurl, title);
            parent.layui.admin.events.refresh()
        };

        // if (user.userInfo.sfxgmm == null || user.userInfo.sfxgmm != 1) {
        //     //ctx + 'apiLogController/apiLogDetial?ID='
        //     //弹出一个窗口提示登录信息
        //     layui.layer.open({
        //         type: 2,
        //         title: "登录信息",
        //         shadeClose: true,
        //         btn: "确定",
        //         area: ['50%', '70%'],
        //         offset: ["10%", "40%"]
        //         , content: ctx + '/demoIndexController/lastLogin'
        //     });
        // } else {
        //     var index = layer.alert('您的密码可能不安全，请修改密码后再进行登录', {
        //         icon: 0,
        //         shadeClose: true,
        //         title: '提示'
        //     });
        // }

    });


    // 跳转版
    $(function(){
        debugger;
        var url = getUrlParam('openTabUrl');
        var title = getUrlParam('openTabTitle');

        // 确保layui完全加载并初始化
        layui.use(['index'], function(){
            if(url) {
                // 轮询检测菜单是否渲染完成
                var tryCount = 0;
                var maxTry = 30; // 增加尝试次数到30次（3秒）
                var menuData = JSON.parse([[${session.menuList}]]);
                var ctx = [[${#servletContext.contextPath}]];

                function findMenuByTitle(menus, targetTitle) {
                    for(var i = 0; i < menus.length; i++) {
                        var menu = menus[i];
                        if(menu.menuName === targetTitle) {
                            return menu;
                        }
                        if(menu.children) {
                            var found = findMenuByTitle(menu.children, targetTitle);
                            if(found) return found;
                        }
                    }
                    return null;
                }

    //             function trySelectMenu() {
    //                 tryCount++;
    //                 var targetMenu = findMenuByTitle(menuData, title);
    //
    //                 if(targetMenu && $("#nav [data-id]").length > 0) {
    //                     // 查找菜单DOM元素
    //                     var menuItem = $("#nav [data-id='" + targetMenu.ID + "']");
    //                     console.log("找到的菜单项:", menuItem.length ? "是" : "否");
    //
    //                     if(menuItem.length) {
    //                         // 先展开父菜单（如果有）
    //                         var parentItem = menuItem.closest(".layui-nav-item");
    //                         if(parentItem.length && !parentItem.hasClass("layui-nav-itemed")) {
    //                             parentItem.addClass("layui-nav-itemed");
    //                             // 给layui一点时间来展开菜单
    //                             setTimeout(function() {
    //                                 // 查找菜单项中的链接元素并触发点击
    //                                 var link = menuItem.find("a[lay-href]");
    //                                 if(link.length) {
    //                                     // 清除URL参数
    //                                     var cleanUrl = window.location.origin + window.location.pathname;
    //                                     history.replaceState({}, document.title, cleanUrl);
    //
    //                                     // 模拟点击
    //                                     link[0].click();
    //                                 } else {
    //                                     // 直接触发layui的事件
    //                                     layui.event.call(link[0], 'layadmin', 'clickSideMenu', {
    //                                         title: title,
    //                                         href: url
    //                                     });
    //                                 }
    //                             }, 100);
    //                         } else {
    //                             // 直接点击菜单项
    //                             var link = menuItem.find("a[lay-href]");
    //                             if(link.length) {
    //                                 // 清除URL参数
    //                                 var cleanUrl = window.location.origin + window.location.pathname;
    //                                 history.replaceState({}, document.title, cleanUrl);
    //
    //                                 // 模拟点击
    //                                 link[0].click();
    //                             } else {
    //                                 // 回退到默认打开方式
    //                                 layui.index.openTabsPage(url, title || '新页面');
    //
    //                                 // 清除URL参数
    //                                 setTimeout(function() {
    //                                     var cleanUrl = window.location.origin + window.location.pathname;
    //                                     history.replaceState({}, document.title, cleanUrl);
    //                                 }, 100);
    //                             }
    //                         }
    //                     } else {
    //                         // 没找到对应菜单项，使用默认方式打开
    //                         layui.index.openTabsPage(url, title || '新页面');
    //
    //                         // 清除URL参数
    //                         setTimeout(function() {
    //                             var cleanUrl = window.location.origin + window.location.pathname;
    //                             history.replaceState({}, document.title, cleanUrl);
    //                         }, 100);
    //                     }
    //                 } else if(tryCount < maxTry) {
    //                     // 菜单还没渲染好，继续等待
    //                     setTimeout(trySelectMenu, 100);
    //                 } else {
    //                     // 超时，直接打开标签页
    //                     layui.index.openTabsPage(url, title || '新页面');
    //
    //                     // 清除URL参数
    //                     setTimeout(function() {
    //                         var cleanUrl = window.location.origin + window.location.pathname;
    //                         history.replaceState({}, document.title, cleanUrl);
    //                     }, 100);
    //                 }
    //             }
    //             trySelectMenu();
    //         }
    //     });
    // });
    //
    // // 获取url参数
    // function getUrlParam(name) {
    //     var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
    //     var r = window.location.search.substr(1).match(reg);
    //     if (r != null) return decodeURIComponent(r[2]); return null;
    // }

                function trySelectMenu() {
                    tryCount++;
                    var targetMenu = findMenuByTitle(menuData, title);

                    if(targetMenu && $("#nav [data-id]").length > 0) {
                        var menuItem = $("#nav [data-id='" + targetMenu.ID + "']");
                        console.log("找到的菜单项:", menuItem.length ? "是" : "否");

                        if(menuItem.length) {
                            // 关键修复：关闭其他已展开的菜单
                            $(".layui-nav-itemed").not(menuItem.closest(".layui-nav-item")).removeClass("layui-nav-itemed");

                            var parentItem = menuItem.closest(".layui-nav-item");
                            if(parentItem.length && !parentItem.hasClass("layui-nav-itemed")) {
                                // 先关闭其他展开菜单再展开当前
                                parentItem.addClass("layui-nav-itemed");
                                setTimeout(function() {
                                    var link = menuItem.find("a[lay-href]");
                                    if(link.length) {
                                        history.replaceState({}, document.title, window.location.pathname);
                                        link[0].click();
                                    } else {
                                        layui.event.call(link[0], 'layadmin', 'clickSideMenu', {
                                            title: title,
                                            href: url
                                        });
                                    }
                                }, 100);
                            } else {
                                // 直接点击菜单项
                                var link = menuItem.find("a[lay-href]");
                                if(link.length) {
                                    history.replaceState({}, document.title, window.location.pathname);
                                    link[0].click();
                                } else {
                                    layui.index.openTabsPage(url, title || '新页面');
                                    setTimeout(() => history.replaceState({}, document.title, window.location.pathname), 100);
                                }
                            }
                        } else if(tryCount < maxTry) {
                            setTimeout(trySelectMenu, 100);
                        } else {
                            fallbackOpenTab();
                        }
                    } else if(tryCount < maxTry) {
                        setTimeout(trySelectMenu, 100);
                    } else {
                        fallbackOpenTab();
                    }
                }

                function fallbackOpenTab() {
                    layui.index.openTabsPage(url, title || '新页面');
                    setTimeout(() => history.replaceState({}, document.title, window.location.pathname), 100);
                }

                trySelectMenu();
            }
        });
    });

    function getUrlParam(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURIComponent(r[2]); return null;
    }
</script>
</body>
</html>
