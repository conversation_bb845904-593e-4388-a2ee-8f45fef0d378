<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>审核日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <link rel="stylesheet" th:href="@{/plugins/dtree/dtree.css}">
    <link rel="stylesheet" th:href="@{/plugins/dtree/font/dtreefont.css}">
<style>
    .layui-form-label{
        width: 100px;
    }
</style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form">
                <input type="hidden" id="treeDeptId">
                <blockquote class="layui-elem-quote quoteBox" id="search">
                <div >
                    <div class="layui-inline">
                        <label class="layui-form-label " >操作人名称：</label>
                        <div class="layui-input-inline mr0">
                            <input id="fullName" class="layui-input" type="text" placeholder="请输入操作人名称">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" >开始日期：</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="beginTime" autocomplete="off" placeholder="请输入开始日期">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" >结束日期：</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="endTime" autocomplete="off" placeholder="请输入结束日期">
                        </div>
                    </div>
                </div>
                    <div >
                    <div class="layui-inline">
                        <label class="layui-form-label" ></label>
                        <button class="layui-btn icon-btn "type="button" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                        <button type="reset" class="layui-btn layui-btn-primary " id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                    </div>
                </div>
                </blockquote>
            </form>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md2">
                    <div style="margin-top: 10px;">
                        <ul id="dataTree" class="dtree" th:data-id="${deptId}"></ul>
                    </div>
                </div>
                <div class="layui-col-md10">
                    <table class="layui-hide" id="zzsb-table" lay-filter="zzsb-table"></table>
                    <script type="text/html" id="test-table-toolbar-barDemo">
                        <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
                    </script>
                </div>
            </div>



            <script type="text/html" id="zzsbtoolbar">
        </script>
        </div>
    </div>
</div>
<script th:inline="javascript">
    var deptId = [[${deptId}]];
    var formSelects = layui.formSelects;
    layui.extend({
        dtree: ctx +'/dtree/dtree'
    }).use(['table','layer','jquery', 'form', 'dtree','laydate'] , function(){
        var admin = layui.admin;
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var dtree = layui.dtree;
        var laydate = layui.laydate;
        //日期
        laydate.render({
            elem: '#beginTime',
            type: 'datetime'
        });
        laydate.render({
            elem: '#endTime',
            type: 'datetime'
        });
        table.on('tool(zzsb-table)', function(obj){
            var data = obj.data;
            if(obj.event === 'detail'){
                layer.open({
                    type: 2,
                    title: "查看日志详情",
                    shadeClose: true,
                    area: ['99%', '95%'],
                    btn:"返回",
                    offset: [ //为了演示，随机坐标
                        0.1*($(window).height()-400)
                    ]
                    ,content: ctx + 'logController/logDetial?ID=' + data.id
                });
            }
        });
        var ins1 = table.render({
            elem: '#zzsb-table'
            ,url: ctx + 'auditLogController/auditLogListJson'
            ,method: 'post'
            ,defaultToolbar: ['filter', 'print']
            ,toolbar: '#table-toolbar-top'
            ,title: '登录日志表'
            ,cols: [
                [
                    {title: '序号', width:50, type:'numbers'}
                    ,{field:'fullName', title:'操作人名称',align: 'center',width:120,templet:function(data){
                        var html = '';
                        if(data.user == null || data.user.fullName == null){
                            return html;
                        }else {
                            return data.user.fullName
                        }
                    }}
                    ,{field:'deptName', title:'所属部门',align: 'center',width:150,templet:function(data){
                        if(data.user == null || data.user.dept == null || data.user.dept.deptName == null){
                            return '系统部门a_list';
                        }else {
                            return data.user.dept.deptName;
                        }
                    }}
                    ,{field:'controName', title:'操作控制器',align: 'center',width:200}
                    ,{field:'controDisplay', title:'控制器说明',align: 'center',width:200}
                    ,{field:'actionName', title:'操作动作',align: 'center',width:200}
                    ,{field:'actionDisplay', title:'动作说明',align: 'center',width:200,
                    /*templet:function(data) {
                        if (data.actionDisplay == "登录成功" ||data.actionDisplay == "登录成功") {
                            return "<span style='color:#00FF00;'>"+data.actionDisplay+"</span>";
                        }else{
                            return "<span style='color:red;'>"+data.actionDisplay+"</span>";
                        }

                    }*/}
                    ,{field:'parameterJson', title:'参数json字符串',align: 'center',width:120}
                    ,{field:'recordTime', title:'操作时间',align: 'center',width:200}
                    ,{title:'操作',toolbar: '#test-table-toolbar-barDemo',fixed: 'right',align: 'center',width:80}
                ]
            ]

            ,request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            ,parseData: function(res){ //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            ,page: true
        });
        form.render();

        // 初始化树
        var DTreeNode = dtree.render({
            elem: "#dataTree",
            url: ctx +"deptController/depTreeJson", // 使用url加载
            dataStyle: "layuiStyle",  //使用layui风格的数据格式
            dataFormat: "list",  //配置data的风格为list
            response:{message:"msg",statusCode:0},  //修改response中返回数据的定义
            checkbar:true ,//开启复选框
            checkbarType: "no-all",
            initLevel: 2,
            type:"all",
            line: true, // 有树线
            ficon: "2",  // 设定一级图标样式
            icon: "-1", // 不设定二级图标样式。
            skin: "laySimple"
        });

        // 绑定用复选框点击事件
        dtree.on("node(dataTree)", function(obj){
            var nodeId = obj.param.nodeId;
            $("#treeDeptId").val(nodeId);
            table.reload('zzsb-table',{
                where: { //设定异步数据接口的额外参数，任意设
                    deptId:nodeId,
                    fullName : $('#fullName').val(),
                    beginTime : $('#beginTime').val(),
                    endTime : $('#endTime').val(),
                }
                ,page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });

        // 绑定dtree的展开关闭点击事件
        dtree.on("changeTree('dataTree')" ,function(obj){
            var param = dtree.getChildParam(DTreeNode, obj.param.nodeId);
            if(obj.show && param.length == 0){
                $.ajax({
                    url : ctx + 'deptController/getChildNode',
                    type : "POST",
                    async : true,
                    cache : false,
                    data : {
                        "parentId" : obj.param.nodeId,
                        "checked" : obj.param.checked
                    },
                    success : function(data) {
                        DTreeNode.getChild($(obj.dom).parent("div"), data);
                        //console.log($(obj.dom).parent("div"));
                    },
                    error : function(result) {
                        layer.msg("更新失败!",{icon: 5, time: 1000,skin: 'layer-ext-moon' });
                    }
                });
            }
            //console.log(obj.dom); // 当前节点的jquery对象
            // 当前节点的jquery对象 DemoTree.getChild($div);

        });
        //搜索及重置按钮
        $("#searchBtn").click(function(){
            var fullName =$("#fullName").val();
            var beginTime =$("#beginTime").val();
            var endTime =$("#endTime").val();
            var treeDeptId=$("#treeDeptId").val();
            table.reload('zzsb-table',{
                where: { //设定异步数据接口的额外参数，任意设
                    deptId: treeDeptId,
                    fullName: fullName,
                    beginTime: beginTime,
                    endTime: endTime,
                }
                ,page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
            return false;
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function(){
            window.location.reload();
        })

    });

</script>
</body>
</html>