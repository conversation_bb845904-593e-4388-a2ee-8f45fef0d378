<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="intermediary-alert" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="intermediary-alert" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="intermediary-alert" options="-parameters -verbose -bootclasspath &quot;$PROJECT_DIR$/../../../../../Program Files/Java/jdk-17\lib\rt.jar;$PROJECT_DIR$/../../../../../Program Files/Java/jdk-17\lib\jce.jar&quot;" />
    </option>
  </component>
</project>