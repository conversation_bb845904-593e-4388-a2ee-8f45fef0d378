_jsload2&&_jsload2('autocomplete', 'z.lang.Ca.prototype.ci=ha(0,function(){delete window[z.aa]._instances[this.aa];for(var a in this)z.lang.wt(this[a])||delete this[a]});z.D.getComputedStyle=function(a,b){var a=z.D.Oj(a),c=z.D.QD(a);return c.defaultView&&c.defaultView.getComputedStyle&&(c=c.defaultView.getComputedStyle(a,s))?c[b]||c.getPropertyValue(b):""};z.D.remove=function(a){var a=z.D.Oj(a),b=a.parentNode;b&&b.removeChild(a)};z.lang.isArray=function(a){return"[object Array]"==Object.prototype.toString.call(a)}; z.event=z.event||{};z.event.stopPropagation=function(a){a.stopPropagation?a.stopPropagation():a.cancelBubble=q};z.event.preventDefault=function(a){a.preventDefault?a.preventDefault():a.returnValue=t};z.event.stop=function(a){var b=z.event;b.stopPropagation(a);b.preventDefault(a)};z.mc.indexOf=function(a,b,c){var e=a.length,c=c|0;for(0>c&&(c=Math.max(0,e+c));c<e;c++)if(c in a&&a[c]===b)return c;return-1};z.mc.contains=function(a,b){return 0<=z.mc.indexOf(a,b)};z.Ob=z.Ob||{version:"1.3.9"}; z.Ob.rY=function(a){for(var a=a.split("-"),b=z.Ob,c=a.length,e=0;e<c;e++)b=b[a[e].charAt(0).toUpperCase()+a[e].slice(1)];return b};z.Ob.create=function(a,b){z.lang.wg(a)&&(a=z.Ob.rY(a));return new a(b)};z.event.YA=z.event.YA||[]; z.event.M=function(a,b,c){function e(b){c.call(a,b)}var b=b.replace(/^on/i,""),a=z.D.Oj(a),f=z.event.YA,g=z.event.E1,i=b,b=b.toLowerCase();g&&g[b]&&(g=g[b](a,b,e),i=g.type,e=g.n4);a&&a.addEventListener?a.addEventListener(i,e,t):a&&a.attachEvent&&a.attachEvent("on"+i,e);f[f.length]=[a,b,c,e,i];return a};z.M=z.event.M; z.event.ed=function(a,b,c){for(var a=z.D.Oj(a),b=b.replace(/^on/i,"").toLowerCase(),e=z.event.YA,f=e.length,g=!c,i,k;f--;)if(i=e[f],i[1]===b&&i[0]===a&&(g||i[2]===c))k=i[4],i=i[3],a.removeEventListener?a.removeEventListener(k,i,t):a.detachEvent&&a.detachEvent("on"+k,i),e.splice(f,1);return a};z.ed=z.event.ed; z.Ob.tG={id:"",Xb:function(a){var b;b="tangram-"+this.uu+"--"+(this.id?this.id:this.aa);return a?b+"-"+a:b},Us:function(a){var b=this.wK,c=this.T_;a&&(b+="-"+a,c+="-"+a);this.T_&&(b+=" "+c);return b},hi:function(){return z.$(this.dN)},zX:function(){return z.$(this.Xb())},uu:"",Zo:function(){return"window[\'$BAIDU$\']._instances[\'"+this.aa+"\']"},l3:function(a){for(var b=0,c=Array.prototype.slice.call(arguments,1),e=c.length;b<e;b++)"string"==typeof c[b]&&(c[b]="\'"+c[b]+"\'");return this.Zo()+"."+a+"("+ c.join(",")+");"},M:function(a,b,c){z.M(a,b,c);this.addEventListener("ondispose",function(){z.ed(a,b,c)})},YZ:function(a){if(!this.hi())return a=z.$(a),a||(a=document.createElement("div"),document.body.appendChild(a),a.style.position="absolute",a.className=this.Us("main")),a.id||(a.id=this.Xb("main")),this.dN=a.id,a.setAttribute("data-guid",this.aa),a},ci:function(){this.dispatchEvent("dispose");z.lang.Ca.prototype.ci.call(this)}}; z.Ob.xW=function(a){function b(){}function c(b,e){var o=this,b=b||{};f.call(o,!g?b:b.aa||"",q);z.object.extend(o,c.options);z.object.extend(o,b);o.wK=o.wK||"tangram-"+o.uu.toLowerCase();for(i in z.Ob.Zl)"undefined"!=typeof o[i]&&o[i]&&(z.object.extend(o,z.Ob.Zl[i]),z.lang.wt(o[i])?o.addEventListener("onload",function(){z.Ob.Zl[i].call(o[i].apply(o))}):z.Ob.Zl[i].call(o));a.apply(o,arguments);i=0;for(k=c.zz.length;i<k;i++)c.zz[i](o);b.parent&&o.G_&&o.G_(b.parent);!e&&b.m2&&o.va(b.element)}var e;e= e||{};var f=e.b0||z.lang.Ca,g=f==z.lang.Ca?1:0,i,k;b.prototype=f.prototype;e=c.prototype=new b;for(i in z.Ob.tG)e[i]=z.Ob.tG[i];c.extend=function(a){for(i in a)c.prototype[i]=a[i];return c};c.zz=[];c.Ye=function(a){"function"==typeof a&&c.zz.push(a)};c.options={};return c};z.D.KD=function(a,b){a=z.D.$(a);if(a===s)return a;if("style"==b)return a.style.cssText;b=z.D.RG[b]||b;return a.getAttribute(b)};z.KD=z.D.KD; z.Ob.get=function(a){var b;if(z.lang.wg(a))return z.lang.Nc(a);do{if(!a||9==a.nodeType)return s;if(b=z.D.KD(a,"data-guid"))return z.lang.Nc(b)}while((a=a.parentNode)!=document.body)}; z.Ob.Bi=z.Ob.xW(function(){var a=this;a.addEventListener("onload",function(){eventName=H()?"touchstart":"mousedown";a.M(document,eventName,a.PW);a.M(window,"blur",a.S0)});a.PW=a.sS();a.S0=a.RS();a.tk=[];a.Zc=-1}).extend({uu:"suggestion",D4:new Function,H4:new Function,E4:new Function,G4:new Function,J4:new Function,F4:new Function,getData:function(){return[]},Y4:"",k2:"",km:{},t0:"<div id=\'#{0}\' class=\'#{1}\' style=\'position:relative; top:0px; left:0px\'></div>",G5:"<div id=\'#{0}\' class=\'#{1}\'>#{2}</div>", s0:\'<div style="height:22px;line-height:22px;overflow:hidden;zoom:1;border-top:#E7E7E7 solid 1px;background:#F7F7F7;"><span style="float:right;color:#4B4B4B;border-left:1px solid #E7E7E7;padding:0 10px;font-size:14px;" onmousedown="#{0}">\\u5173\\u95ed</span></div>\',r0:\'<table cellspacing="0" cellpadding="2"><tbody>#{0}</tbody></table>\',u0:\'<tr><td id="#{0}" onmouseover="#{2}" onmouseout="#{3}" onmousedown="#{4}" onclick="#{5}" class="#{6}"><i class="route-icon">#{1}</i></td></tr>\',iY:function(){return z.Wo(this.t0, this.Xb(),this.Us(),this.aa)},va:function(a,b){var c,a=z.$(a);this.gC=b;!this.hi()&&a&&(this.d0=a.id?a.id:a.id=this.Xb("input"),c=this.YZ(),c.style.display="none",c.innerHTML=this.iY(),this.dispatchEvent("onload"))},SA:function(){var a=this.hi();return a&&"none"!=a.style.display},wj:function(a){var b=this.km,a={data:{item:(b&&"number"==typeof a&&"undefined"!=typeof b[a]?b[a].value:a)==a?{value:a,content:a}:b[a],index:a}};this.dispatchEvent("onbeforepick",a)&&this.dispatchEvent("onpick",a)},show:function(a, b,c){var a=0,e=b.length;this.tk=[];this.Zc=-1;if(0==e&&!c)this.U();else{for(this.km=[];a<e;a++)"undefined"!=typeof b[a].value?this.km.push(b[a]):this.km.push({value:b[a],content:b[a]}),("undefined"==typeof b[a].disable||b[a].disable==t)&&this.tk.push(a);this.zX().innerHTML=this.oS();this.hi().style.display="block";this.dispatchEvent("onshow")}},U:function(){if(this.SA()){if(0<=this.Zc&&this.AY)for(var a=this.km,b=-1,c=0,e=a.length;c<e;c++)if("undefined"==typeof a[c].disable||a[c].disable==t)b++,b== this.Zc&&this.wj(c);this.hi().style.display="none";this.dispatchEvent("onhide")}},jt:function(a){var b=this.tk,c=s;this.jr(a)&&(0<=this.Zc&&this.oH(),c=this.TH(a),z.Ua(c,this.Us("current")),this.Zc=z.mc.indexOf(b,a),this.dispatchEvent("onhighlight",{index:a,data:this.qm(a)}))},rC:function(){var a=this.tk[this.Zc];this.oH()&&this.dispatchEvent("onclearhighlight",{index:a,data:this.qm(a)})},oH:function(){var a=this.Zc,b=this.tk,c=s;return 0<=a?(c=this.TH(b[a]),z.Pb(c,this.Us("current")),this.Zc=-1, q):t},confirm:function(a,b){if("keyboard"==b||this.jr(a))this.wj(a),this.dispatchEvent("onconfirm",{data:this.qm(a)||a,source:b}),this.Zc=-1,this.U()},qm:function(a){return{item:this.km[a],index:a}},dE:function(){return this.ym().value},ym:function(){return z.$(this.d0)},TH:function(a){return z.$(this.Xb("item"+a))},oS:function(){for(var a="",b=[],c=this.km,e=c.length,f=0;f<e;f++)b.push(z.Wo(this.u0,this.Xb("item"+f),c[f].content,this.Zo()+"._itemOver(event, "+f+")",this.Zo()+"._itemOut(event, "+ f+")",this.Zo()+"._itemDown(event, "+f+")",this.Zo()+"._itemClick(event, "+f+")","undefined"==typeof c[f].disable||c[f].disable==t?"":this.Us("disable")));a+=z.Wo(this.r0,b.join(""));H()&&(a+=z.Wo(this.s0,this.Zo()+"._close(event)"));return a},_close:function(){this.U()},_itemOver:function(a,b){z.event.stop(a||window.event);this.jr(b)&&this.jt(b);this.dispatchEvent("onmouseoveritem",{index:b,data:this.qm(b)})},_itemOut:function(a,b){z.event.stop(a||window.event);this.AY||this.jr(b)&&this.rC();this.dispatchEvent("onmouseoutitem", {index:b,data:this.qm(b)})},_itemDown:function(a,b){z.event.stop(a||window.event);this.dispatchEvent("onmousedownitem",{index:b,data:this.qm(b)})},_itemClick:function(a,b){z.event.stop(a||window.event);this.dispatchEvent("onitemclick",{index:b,data:this.qm(b)});this.jr(b)&&this.confirm(b,"mouse")},jr:function(a){return z.mc.contains(this.tk,a)},sS:function(){var a=this;return function(b){var b=b||window.event,b=b.target||b.srcElement,c=z.Ob.get(b);b==a.ym()||c&&c.uu==a.uu||a.U()}},RS:function(){var a= this;return function(){a.U()}},ci:function(){this.dispatchEvent("dispose");z.D.remove(this.dN);z.lang.Ca.prototype.ci.call(this)}});z.Ob.Zl=z.Ob.Zl||{};z.D.children=function(a){a=z.D.$(a);if(a===s)return a;for(var b=[],a=a.firstChild;a;a=a.nextSibling)1==a.nodeType&&b.push(a);return b};z.D.insertBefore=function(a,b){var c;c=z.D.Oj;a=c(a);b=c(b);(c=b.parentNode)&&c.insertBefore(a,b);return a}; z.D.Md=function(a,b,c){var e=z.D,f,a=e.$(a),b=z.Wk.RO(b);if(f=e.Ol)c=f.filter(b,c,"set");(f=e.PB[b])&&f.set?f.set(a,c):a.style[f||b]=c;return a};z.Md=z.D.Md;z.D.ti=function(a,b){a=z.D.$(a);if(a===s)return a;for(var c in b)z.D.Md(a,c,b[c]);return a};z.ti=z.D.ti;z.D.Ol[z.D.Ol.length]={set:function(a,b){b.constructor==Number&&!/zIndex|fontWeight|opacity|zoom|lineHeight/i.test(a)&&(b+="px");return b}}; z.D.Vt=function(a,b){function c(a,b){return parseFloat(z.oj(a,b))||0}var e={};b.width&&(e.width=parseFloat(b.width));b.height&&(e.height=parseFloat(b.height));if(z.ca.DE&&(b.width&&(e.width=parseFloat(b.width)-c(a,"paddingLeft")-c(a,"paddingRight")-c(a,"borderLeftWidth")-c(a,"borderRightWidth"),0>e.width&&(e.width=0)),b.height))e.height=parseFloat(b.height)-c(a,"paddingTop")-c(a,"paddingBottom")-c(a,"borderTopWidth")-c(a,"borderBottomWidth"),0>e.height&&(e.height=0);return z.D.ti(a,e)}; (function(){var a=z.Ob.Zl.HK=u();a.hz=t;a.dq;a.xG;a.yG;a.CP=function(){if(this.dq)this.DP(),z.Md(this.dq,"display","block");else{var a=this.nW||{},c=this.xG=a.Wa||this.hi(),e=a.opacity||"0",a=a.color||"",f=this.dq=document.createElement("iframe"),g=this.yG=document.createElement("div");0<z.D.children(c).length?z.D.insertBefore(g,c.firstChild):c.appendChild(g);z.ti(g,{position:"absolute",top:"0px",left:"0px"});z.D.Vt(g,{width:c.offsetWidth,height:c.offsetHeight});z.D.Vt(f,{width:g.offsetWidth});z.D.ti(f, {zIndex:-1,display:"block",border:0,backgroundColor:a,filter:"progid:DXImageTransform.Microsoft.Alpha(style=0,opacity="+e+")"});g.appendChild(f);f.src="javascript:void(0)";f.frameBorder="0";f.scrolling="no";f.height="97%";this.hz=q}};a.BP=function(){var a=this.dq;this.hz&&(z.Md(a,"display","none"),this.hz=t)};a.DP=function(){var a;a=a||{};var c=this.xG,e=this.yG,f=this.dq;z.D.Vt(e,{width:c.offsetWidth,height:c.offsetHeight});z.D.Vt(f,z.extend({width:z.oj(e,"width")},a))}})(); z.extend(z.Ob.Bi.prototype,{HK:q,nW:{}});z.Ob.Bi.Ye(function(a){a.HK&&(a.addEventListener("onshow",function(){a.CP()}),a.addEventListener("onhide",function(){a.BP()}))});z.Ob.Bi.extend({setData:function(a,b,c){this.JC[a]=b;c||this.show(a,this.JC[a])}});z.Ob.Bi.Ye(function(a){a.JC={};a.addEventListener("onneeddata",function(b,c){var e=a.JC;"undefined"==typeof e[c]?a.getData(c):a.show(c,e[c])})});z.D.E_=z.D.l5=function(a,b){return z.D.Vt(a,{width:b})}; z.D.uY=function(a){a=z.D.$(a);if(a===s)return a;a=z.D.QD(a);return a.parentWindow||a.defaultView||s};z.D.sa=function(a,b){return z.D.ti(a,{left:b.left-(parseFloat(z.D.oj(a,"margin-left"))||0),top:b.top-(parseFloat(z.D.oj(a,"margin-top"))||0)})};z.page=z.page||{};z.page.jE=function(){var a=document;return("BackCompat"==a.compatMode?a.body:a.documentElement).clientWidth};z.page.iE=function(){var a=document;return("BackCompat"==a.compatMode?a.body:a.documentElement).clientHeight}; z.page.gY=function(){var a=document;return window.pageYOffset||a.documentElement.scrollTop||a.body.scrollTop};z.page.fY=function(){var a=document;return window.pageXOffset||a.documentElement.scrollLeft||a.body.scrollLeft};z.nm=z.nm||{};z.nm.bind=function(a,b){var c=2<arguments.length?[].slice.call(arguments,2):s;return function(){var e=z.lang.wg(a)?b[a]:a,f=c?c.concat([].slice.call(arguments,0)):arguments;return e.apply(b||e,f)}}; (function(){function a(a){a.x=a[0]||a.x||a.left||0;a.y=a[1]||a.y||a.top||0;return a}var b=z.Ob.Zl.PZ=u();b.sa=function(a,b,f){b=z.$(b)||this.hi();f=f||{};this.gQ(b,f.BZ,[b,a,f])};b._positionByCoordinate=function(b,e,f,g){var e=e||[0,0],f=f||{},i={},k=z.page.iE(),m=z.page.jE(),n=z.page.fY(),o=z.page.gY(),p=b.offsetWidth,v=b.offsetHeight,x=b.offsetParent,x=!x||x==document.body?{left:0,top:0}:z.D.ga(x);f.position="undefined"!==typeof f.position?f.position.toLowerCase():"bottomright";e=a(e||[0,0]);f.za= a(f.za||[0,0]);e.x+=0<=f.position.indexOf("right")?e.width||0:0;e.y+=0<=f.position.indexOf("bottom")?e.height||0:0;i.left=e.x+f.za.x-x.left;i.top=e.y+f.za.y-x.top;switch(f.e4){case "surround":i.left+=i.left<n?p+(e.width||0):i.left+p>n+m?-p-(e.width||0):0;i.top+=i.top<o?v+(e.height||0):i.top+v>o+k?-v-(e.height||0):0;break;case "fix":i.left=Math.max(0-parseFloat(z.D.oj(b,"marginLeft"))||0,Math.min(i.left,z.page.jE()-p-x.left));i.top=Math.max(0-parseFloat(z.D.oj(b,"marginTop"))||0,Math.min(i.top,z.page.iE()- v-x.top));break;case "verge":var y=-1<f.position.indexOf("right")?e.width:0,A=-1<f.position.indexOf("bottom")?e.height:0,E=-1<f.position.indexOf("bottom")?e.width:0,C=-1<f.position.indexOf("right")?e.height:0;i.left-=0<=f.position.indexOf("right")?e.width||0:0;i.top-=0<=f.position.indexOf("bottom")?e.height||0:0;i.left+=i.left+y+p-n>m-x.left?E-p:y;i.top+=i.top+A+v-o>k-x.top?C-v:A}z.D.sa(b,i);!g&&(k!=z.page.iE()||m!=z.page.jE())&&this._positionByCoordinate(b,e,{},q);g||this.dispatchEvent("onpositionupdate")}; b.gQ=function(a,b,f){("undefined"==typeof b||!b)&&z.event.M(z.D.uY(a),"resize",z.nm.bind.apply(this,["_positionByCoordinate",this].concat([].slice.call(f))));this._positionByCoordinate.apply(this,f)}})(); z.Ob.Bi.extend({PZ:q,lX:q,vY:function(){var a=this;return function(){a.Fw(q)}},Fw:function(a){var b=this.ym(),c=this.gC,e=this.hi();if(this.SA()||!a)c&&(b=c),a=z.D.ga(b),b={top:a.top+b.offsetHeight-1,left:a.left,width:b.offsetWidth},b="function"==typeof this.view?this.view(b):b,this.sa([b.left,b.top],s,{BZ:q}),z.D.E_(e,b.width)}}); z.Ob.Bi.Ye(function(a){a.T0=a.vY();a.addEventListener("onload",function(){a.Fw();a.lX&&(a.mX=setInterval(function(){var b=a.hi(),c=a.ym();0!=b.offsetWidth&&(c&&c.offsetWidth!=b.offsetWidth)&&(a.Fw(),b.style.display="block")},100));a.M(window,"resize",a.T0)});a.addEventListener("onshow",function(){a.Fw()});a.addEventListener("ondispose",function(){clearInterval(a.mX)})}); z.Ob.Bi.Ye(function(a){function b(){setTimeout(function(){var b=a.ym();f=b===s?"":b.value},20)}var c,e="",f,g,i=t,k=t;a.addEventListener("onload",function(){c=this.ym();b();a.M(window,"onload",b);a.e0=a.mY();a.M(c,"keydown",a.e0);c.setAttribute("autocomplete","off");a.$V=setInterval(function(){if(!k){z.$(c)==s&&a.ci();var b=c.value;b==e&&""!=b&&b!=f&&b!=g?0==a.vF&&(a.vF=setTimeout(function(){a.dispatchEvent("onneeddata",b)},100)):(clearTimeout(a.vF),a.vF=0,""==b&&""!=e&&(g="",a.U()),e=b,b!=g&&(a.ix= b),f!=c.value&&(f=""))}},10);a.M(c,"beforedeactivate",a.EV)});a.addEventListener("onitemclick",function(){k=t;a.ix=e=a.dE()});a.addEventListener("onpick",function(a){i&&c.blur();c.value=g=a.data.item.value;i&&c.focus()});a.addEventListener("onmousedownitem",function(){k=i=q;setTimeout(function(){i=k=t},500)});a.addEventListener("ondispose",function(){clearInterval(a.$V)})}); z.Ob.Bi.extend({EV:function(){return function(){mousedownView&&(window.event.cancelBubble=q,window.event.returnValue=t)}},mY:function(){var a=this;return function(b){var c=t,b=b||window.event;switch(b.keyCode){case 9:case 27:a.U();break;case 13:z.event.stop(b);a.confirm(-1==a.Zc?a.ym().value:a.tk[a.Zc],"keyboard");break;case 38:c=q;case 40:z.event.stop(b);b=c;if(a.SA()){var c=a.tk,e=a.Zc;if(0!=c.length){if(b)switch(e){case -1:e=c.length-1;a.wj(c[e]);a.jt(c[e]);break;case 0:e=-1;a.wj(a.ix);a.rC(); break;default:e--,a.wj(c[e]),a.jt(c[e])}else switch(e){case -1:e=0;a.wj(c[e]);a.jt(c[e]);break;case c.length-1:e=-1;a.wj(a.ix);a.rC();break;default:e++,a.wj(c[e]),a.jt(c[e])}a.Zc=e}}else a.dispatchEvent("onneeddata",a.dE());break;default:a.Zc=-1}}},ix:""});ia.K5=q;var ei=t,fi=B.Wc+"res/20/bmap_autocomplete.css",gi={ALL:"0",CITY:"1"}; z.extend(Zd.prototype,{Sd:function(){this.Mi()},Mi:function(){this.ml=-1;this.Ia=s;this.lw=[];this.mH=0;this.ha=this.ov();this.ub.input&&(this.NS(),this.lU(),this.Ly(this.lI));var a=s;this.Wi&&(a={method:"search"},a.arguments=[this.Wi]);this.cI();this.KH(a)},lU:function(){var a=this,b=new z.Ob.Bi({getData:function(b){a.iJ(b)},onhide:function(){a.ml=-1;""===a.pg.dE()&&(a.Wi="",a.ha=a.ov())},onhighlight:function(b){if(a.Ia){var e={},f={},f={index:b.data.index,value:a.Ia.Lm[b.data.index].value},e={index:a.ml, value:0<=a.ml?a.Ia.Lm[a.ml].value:{}},g=new P("onhighlight");g.fromitem=g.d3=e;g.toitem=g.D5=f;a.dispatchEvent(g);a.ml=b.data.index}},onbeforepick:function(b){if(a.Ia&&"number"===typeof b.data.index){var e=a.Ia.Lm[b.data.index].value;b.data.item.content=b.data.item.value=e.province+e.city+e.district+e.street+e.business}},onconfirm:function(b){if(a.Ia){Ra(5012);var e={};"number"===typeof b.data.index&&(e={index:b.data.index,value:a.Ia.Lm[b.data.index].value},b=new P("onconfirm"),b.item=b.item=e,a.dispatchEvent(b))}}}); b.va(z.$(this.ub.input),z.$(this.ub.gC));this.pg=b},show:function(){this.pg&&(this.pg.hi().style.display="block")},U:function(){this.pg&&this.pg.U();this.ml=-1},ci:function(){this.pg&&this.pg.ci();z.lang.Ca.prototype.ci.call(this)},KF:function(a){this.ub.types=a||[];this.cI()},fn:function(a){this.Ad.src=a;this.KH()},search:function(a){this.iJ(a)},Ly:function(a){this.pg&&this.pg.wj(a)},iJ:function(a){var b=this,c=(1E5*Math.random()).toFixed(0);B._rd["_cbk"+c]=function(a){z.$(b.ub.input)!==s&&b.mS(a); delete B._rd["_cbk"+c]};var e=B.url.proto+B.url.domain.baidumap+"/su?wd="+encodeURIComponent(a)+"&callback=BMap._rd._cbk"+c+"&cid="+b.mH+"&type="+b.lw+"&t="+(new Date).getTime()+"&from=jsapi";b.Wi=a;b.ml=-1;b.ha=this.ov();Ra(5010,{tp:b.lw,wd:encodeURIComponent(a)});B.alog("cus.fire","count","z_sug");oa(e)},mS:function(a){a&&a.q&&(this.Wi=a.q);this.LU(this.$R(a));this.ha=this.ov(this.Ia);this.ub.py(this.ha)},$R:function(a){var b=[],c=a.s,e={};e.vb=a.q||"";e.count=a.s.length||0;e.status=0>a.e?7:0;e.Lm= [];var f=0,g=c.length;for(H()&&6<g&&(g=6);f<g;f++){var i=this.wS(c[f],this.lw);e.Lm.push(i.value);b.push(i.lM)}this.Ia=e;return{data:b,W5:a.q}},LU:function(a){if(this.pg){var b=this.pg;b.show(a.word,a.data);1>a.data.length&&b.U()}},wS:function(a,b){switch(b.toString()){case "1":return this.xS(a);default:return this.yS(a)}},yS:function(a){var a=a.split("$"),b=this.Wi,c=RegExp(b,"g"),e="<b>"+b+"</b>",f={province:"",city:a[0],district:a[1],street:a[2],streetNumber:"",business:a[3]},g="";7<=a.length&& (f.city=a[5]||f.city,f.district=a[6]||f.city);for(var i=3;0<=i;i--)if(g=a[i]+g,-1<g.indexOf(b)){for(var b=i-1,k="";-1<b;)k=a[b]+k,b--;g=g.replace(c,e);k&&(g+=\'&nbsp;<span class="tangram-suggestion-grey">\'+k+"</span>");break}if(!k)for(i=3;0<=i;i--)k+=a[i];return{value:{value:f},lM:g}},xS:function(a){var b=a.split("$"),c=this.Wi,a={province:b[0],city:b[1],district:"",street:"",streetNumber:"",business:""},b=(b[0]+b[1]).replace(RegExp(c,"g"),"<b>"+c+"</b>");return{value:{value:a},lM:b}},cI:function(){var a= this.ub.types,b=[];if(z.wg(a))b.push(gi[a.toUpperCase()]);else if(z.lang.isArray(a))for(var c=0,e=a.length;c<e;c++)b.push(gi[a[c].toUpperCase()]);this.lw=0<b.length?b.join(","):"0"},KH:function(a){var b=this;b.Mg(b.Ad,function(c){b.mH=c;a&&b[a.method].apply(b,a.arguments)})},NS:function(){if(!ei){var a=document,b=a.createElement("link");b.setAttribute("rel","stylesheet");b.setAttribute("type","text/css");b.setAttribute("href",fi);var c=a.getElementsByTagName("head");c.length?c[0].appendChild(b):a.documentElement.appendChild(b); ei=q}},ov:function(a){if(!a)return new hi({vb:this.Wi,total:0,Lk:[]});for(var b=[],c=0,e=a.Lm.length;c<e;c++)b.push(a.Lm[c].value);return new hi({vb:a.vb,total:a.count,Lk:b})}});T(Ef,{show:Ef.show,hide:Ef.U,setTypes:Ef.KF,setLocation:Ef.fn,search:Ef.search,setInputValue:Ef.Ly,dispose:Ef.ci});function hi(a){this.keyword=a.vb||"";this.hB=a.total||0;this.zr=a.Lk&&a.Lk.slice(0)||[]}z.extend(hi.prototype,{Ak:function(a){if(this.zr[a])return this.zr[a]},at:w("hB"),toString:ca("AutocompleteResult")});var ii=hi.prototype;T(ii,{getPoi:ii.Ak,getNumPois:ii.at}); ');