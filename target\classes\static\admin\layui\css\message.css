.light-theme .pear-nav-tree .layui-this a:hover, .light-theme .pear-nav-tree .layui-this, .light-theme .pear-nav-tree .layui-this a, .pear-nav-tree .layui-this a, .pear-nav-tree .layui-this {
    background-color: #36b368 !important;
}

.pear-admin .layui-logo .title {
    color: #36b368 !important;
}

.pear-frame-title .dot, .pear-tab .layui-this .pear-tab-active {
    background-color: #36b368 !important;
}

.bottom-nav li a:hover {
    background-color: #36b368 !important;
}

.pear-admin .layui-header .layui-nav .layui-nav-bar {
    background-color: #36b368 !important;
}

.ball-loader > span, .signal-loader > span {
    background-color: #36b368 !important;
}

.layui-header .layui-nav-child .layui-this a {
    background-color: #36b368 !important;
    color: white !important;
}

#preloader {
    background-color: #36b368 !important;
}

.pearone-color .color-content li.layui-this:after, .pearone-color .color-content li:hover:after {
    border: #36b368 3px solid !important;
}

.layui-nav .layui-nav-child dd.layui-this a, .layui-nav-child dd.layui-this {
    background-color: #36b368;
    color: white;
}

.pear-social-entrance {
    background-color: #36b368 !important
}

.pear-admin .pe-collaspe {
    background-color: #36b368 !important
}

.layui-fixbar li {
    background-color: #36b368 !important
}

.pear-btn-primary {
    background-color: #36b368 !important
}

.layui-input:focus, .layui-textarea:focus {
    border-color: #36b368 !important;
    box-shadow: 0 0 0 3px #f0f9eb !important;
}

.layui-form-checked[lay-skin=primary] i {
    border-color: #36b368 !important;
    background-color: #36b368;
}

.layui-form-onswitch {
    border-color: #36b368;
    background-color: #36b368;
}

.layui-form-radio > i:hover, .layui-form-radioed > i {
    color: #36b368;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #36b368 !important
}

.layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-bottom: 3px solid #36b368 !important
}

.layui-tab-brief > .layui-tab-title .layui-this {
    color: #36b368 !important
}

.layui-progress-bar {
    background-color: #36b368
}

.layui-elem-quote {
    border-left: 5px solid #36b368
}

.layui-timeline-axis {
    color: #36b368
}

.layui-laydate .layui-this {
    background-color: #36b368 !important
}

.pear-this, .pear-text {
    color: #36b368 !important
}

.pear-back {
    background-color: #36b368 !important
}

.pear-collasped-pe {
    background-color: #36b368 !important
}

.layui-form-select dl dd.layui-this {
    color: #36b368 !important;
}

.tag-item-normal {
    background: #36b368 !important
}

.step-item-head.step-item-head-active {
    background-color: #36b368
}

.step-item-head {
    border: 3px solid #36b368;
}

.step-item-tail i {
    background-color: #36b368
}

.step-item-head {
    color: #36b368
}

div[xm-select-skin=normal] .xm-select-title div.xm-select-label > span i {
    background-color: #36b368 !important
}

div[xm-select-skin=normal] .xm-select-title div.xm-select-label > span {
    border: 1px solid #36b368 !important;
    background-color: #36b368 !important
}

div[xm-select-skin=normal] dl dd:not(.xm-dis-disabled) i {
    border-color: #36b368 !important
}

div[xm-select-skin=normal] dl dd.xm-select-this:not(.xm-dis-disabled) i {
    color: #36b368 !important
}

div[xm-select-skin=normal].xm-form-selected .xm-select, div[xm-select-skin=normal].xm-form-selected .xm-select:hover {
    border-color: #36b368 !important
}

.layui-layer-btn a:first-child {
    border-color: #36b368;
    background-color: #36b368 !important
}

.layui-form-checkbox[lay-skin=primary]:hover i {
    border-color: #36b368 !important
}

.pear-tab-menu .item:hover {
    background-color: #36b368 !important
}

.layui-form-danger:focus {
    border-color: #FF5722 !important
}

.pear-admin .user .layui-this a:hover {
    color: white !important
}

.pear-notice .layui-this {
    color: #36b368 !important
}

.layui-form-radio:hover *, .layui-form-radioed, .layui-form-radioed > i {
    color: #36b368 !important
}

.pear-btn:hover {
    color: #36b368;
    background-color: #f0f9eb;
}

.pear-btn-primary[plain] {
    color: #36b368 !important;
    background: #f0f9eb !important;
}

.pear-btn-primary[plain]:hover {
    background-color: #36b368 !important
}

.light-theme .pear-nav-tree .layui-this a:hover, .light-theme .pear-nav-tree .layui-this, .light-theme .pear-nav-tree .layui-this a {
    background-color: #f0f9eb !important;
    color: #36b368 !important;
}

.light-theme .pear-nav-tree .layui-this {
    border-right: 3px solid #36b368 !important
}

.loader:after {
    background: #36b368
}
.pear-notice .layui-this {
    color: #5FB878 !important;
    font-weight: 500;
}

.pear-notice {
    box-shadow: 0 6px 16px -8px rgb(0 0 0 / 8%), 0 9px 28px 0 rgb(0 0 0 / 5%), 0 12px 48px 16px rgb(0 0 0 / 3%) !important;
}

.pear-notice .layui-tab-title {
    text-align: center;
    border-right: 1px solid whitesmoke;
}

.pear-notice * {
    color: dimgray !important;
}

.pear-notice {
    width: 360px !important;
}

.pear-notice img {
    margin-left: 2px;
    width: 12px !important;
    height: 12px !important;
    border-radius: 1px;
    margin-right: 2px;
}

.pear-notice-item {
    height: 45px !important;
    line-height: 45px !important;
    padding-right: 20px;
    padding-left: 20px;
    border-bottom: 1px solid whitesmoke;
    padding-top: 10px;
    padding-bottom: 15px;
}

.pear-notice-end {
    float: right;
    right: 10px;
}

.pear-notice-item span {
    height: 40px;
    line-height: 40px;
}

/** æ»šåŠ¨æ¡æ ·å¼ */
.pear-notice *::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

.pear-notice *::-webkit-scrollbar-track {
    background: white;
    border-radius: 2px;
}

.pear-notice *::-webkit-scrollbar-thumb {
    background: #E6E6E6;
    border-radius: 2px;
}

.pear-notice *::-webkit-scrollbar-thumb:hover {
    background: #E6E6E6;
}

.pear-notice *::-webkit-scrollbar-corner {
    background: #f6f6f6;
}