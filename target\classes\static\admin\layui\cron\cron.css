/**
 @ Name：layui.cron Cron表达式解析器
 @ Author：贝哥哥
 @ License：MIT
 */


/* 样式加载完毕的标识 */
html #layuicss-cron {
    display: none;
    position: absolute;
    width: 1989px;
}


/* 主体结构 */
.layui-cron {
    position: absolute;
    z-index: 1000;
    margin: 5px 0;
    border-radius: 2px;
    font-size: 14px;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    background-color: white;
    display: flex;
    flex-direction: column;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 5px 0px;
    -webkit-animation-name: cron-upbit;
    animation-name: cron-upbit;
    border: 1px solid #e6e6e6;
}

@-webkit-keyframes cron-upbit {

    /* 微微往上滑入 */
    from {
        -webkit-transform: translate3d(0, 20px, 0);
        opacity: 0.3;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes cron-upbit {
    from {
        transform: translate3d(0, 20px, 0);
        opacity: 0.3;
    }

    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

/* tabs */
.layui-cron>.layui-tab {
    margin: 0;
    box-shadow: none;
    border: none;
}

/* 表达式 */
.cron-title {
    font-weight: 700;
    font-size: 14px;
    margin: 10px;
    margin-bottom: 0;
}

.cron-box {
    margin: 10px;
}

.cron-box+.cron-box {
    margin-top: 0;
}

/* 按钮 */
.cron-footer-btns {
    text-align: right;
}

.cron-footer-btns span {
    height: 26px;
    line-height: 26px;
    margin: 0 0 0 -1px;
    padding: 0 10px;
    border: 1px solid #C9C9C9;
    background-color: #fff;
    white-space: nowrap;
    vertical-align: top;
    border-radius: 2px;
    display: inline-block;
    cursor: pointer;
    font-size: 12px;
    box-sizing: border-box;
    color: #666;
}

.cron-footer-btns span:hover {
    color: #5FB878;
}


/* 表单 */
.layui-cron .layui-form-radio {
    margin-right: 0;
}

.cron-form {
    line-height: 28px;
    font-size: 14px;
}

.cron-input-mid {
    display: inline-block;
    vertical-align: middle;
    margin-top: 6px;
    background-color: #e5e5e5;
    padding: 0 12px;
    height: 28px;
    line-height: 28px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

.cron-input {
    display: inline-block;
    vertical-align: middle;
    margin-top: 6px;
    padding: 0 8px;
    background-color: #fff;
    border: 1px solid #ccc;
    height: 28px;
    line-height: 28px;
    box-sizing: border-box;
    width: 80px;
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
}

.cron-input:focus {
    outline: 0;
    border: 1px solid #01AAED;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 4px 0px #01AAED;
    translate: 1s;
}

.layui-cron .layui-form-checkbox[lay-skin="primary"] span {
    padding-right: 10px;
    min-width: 16px;
}

.layui-cron .layui-form-checkbox[lay-skin="primary"] {
    padding-left: 22px;
    margin-top: 5px;
}
.layui-cron input[type=number] {
    -moz-appearance:textfield;
}
.layui-cron input[type=number]::-webkit-inner-spin-button,
.layui-cron input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.cron-tips{
    color: grey;
    line-height: 28px;
    height: 28px;
    display: inline-block;
    vertical-align: middle;
    margin-top: 8px;
    margin-left: 5px;
}