﻿<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org"
	  xmlns:http="http://www.w3.org/1999/xhtml">
<head>
	<div th:replace="Importfile::html"></div>
    <meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link th:href="@{/cron-generator/bootstrap.min.css}" rel="stylesheet">
	<link th:href="@{/cron-generator/font/font-awesome.min.css}" rel="stylesheet">
	<link th:href="@{/cron-generator/cronGen.css}" rel="stylesheet">
	<script th:src="@{/cron-generator/jquery-2.1.4.min.js}"></script>
	<script th:src="@{/cron-generator/later.min.js}"></script>
	<script th:src="@{/cron-generator/cronGen.min.js}"></script>
	<script th:src="@{/cron-generator/bootstrap.min.js}"></script>
    <title></title>
	<script type="text/javascript">
	$(function() {
	    $("#cron").cronGen({
	    	direction : 'right'
	    });
	  
	});
</script>
</head>
    <body>
	<div class="container">
		<form role="form" class="form-inline" onsubmit="return false;">
			<div class="form-group" style="width: 200px">
				<label for="cron">Cron</label>
				<input id="cron" class="form-control" onchange="test()" />
				<button type=button id="startBtn">表达式解析</button>
			</div>
		</form>
		<div id="timeContent">
		</div>
		<button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>
	</div>       


        <script type="text/javascript">
        	$('#startBtn').click(function() {
				run();
        	});
			layui.use(["form"],function(){
				var form = layui.form,cron = layui.cron;
				form.render();
				//监听提交
				form.on('submit(submitBut)', function(data){
					parent.getCron($("#cron").val()); //这是父页面函数
					// 关闭弹窗
					var index = parent.layer.getFrameIndex(window.name);
					parent.layer.close(index);
					return false;
				});
			});


        	function run() {
			    var link = $("#cron").val();
			    if (link == '') {
			        layer.msg("内容不能空.");
			        return;
			    }
			    console.log(link);
			    var sched = later.parse.cron(link);
			    later.date.localTime();
			    var results = later.schedule(sched).next(7);
			    $("#timeContent").html("");
			    for (var i = 0; i < results.length; i++) {
			        $("#timeContent").append(results[i].toLocaleString() + "<hr/>");
			    }
			    //toolfk.report('crontab', link);
			   	return false;
			}
        </script>
    </body>
</html>
