<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fwy.intermediary.dao.IReportPreventionControlDao">
    <resultMap id="BaseResultMap" type="com.fwy.intermediary.entity.ReportPreventionControl">
        <id column="code" property="code" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="suspected_personnel" property="suspectedPersonnel" jdbcType="INTEGER"/>
        <result column="suspected_car" property="suspectedCar" jdbcType="INTEGER"/>
        <result column="black_personnel" property="blackPersonnel" jdbcType="INTEGER"/>
        <result column="black_car" property="blackCar" jdbcType="INTEGER"/>
        <result column="statistical_date" property="statisticalDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--删除状态为3的数据-->
    <delete id="deleteDataByState">
        delete from report_prevention_control
        where state_id=3
    </delete>

    <!--结果体-->
    <resultMap id="alertList" type="com.fwy.intermediary.entity.show.AlertList">
        <result property="month" column="month"/>
        <result property="count" column="count"/>
    </resultMap>
    <!--表1 总预警/月 总黑名单/月  总疑似名单/月-->
    <select id="getT11" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record a
        where 1=1
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>
    <select id="getT12" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        where alert_type=2
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>
    <select id="getT13" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        where alert_type=1
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>


    <!--表2 总次数 已处理次数 未处理次数-->
    <select id="getT21" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        where 1=1
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>
    <select id="getT22" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        where state_id = 3
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>
    <select id="getT23" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        where state_id!=3 and alert_object=2
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>


    <!--表3 疑似人总数 疑似车辆总数-->
    <select id="getT31" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        WHERE alert_type = 1 AND alert_object = 1
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>
    <select id="getT32" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        WHERE alert_type = 1 AND alert_object = 2
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>


    <!--表4 黑名单车辆总 数黑名单人总数-->
    <select id="getT41" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        where alert_type=2 and alert_object=1
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>
    <select id="getT42" resultType="com.fwy.intermediary.entity.show.AlertList">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month, count(code) AS count
        FROM alert_record
        where alert_type=2 and alert_object=2
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        GROUP BY month;
    </select>


  <insert id="saveByDay" parameterType="com.fwy.intermediary.entity.ReportPreventionControl" >
    INSERT INTO report_prevention_control
    set
    code = #{code,jdbcType=VARCHAR},dept_id = #{deptId,jdbcType=VARCHAR},
    dept_name = #{deptName,jdbcType=VARCHAR},
    suspected_personnel = (SELECT COUNT(*)as suspected_personnel FROM alert_record
    	WHERE DATE(create_time) = CURDATE() and alert_object = 1 and alert_type = 1),
    suspected_car = (SELECT COUNT(*)as suspected_car FROM alert_record
    	WHERE DATE(create_time) = CURDATE() and alert_object = 2 and alert_type = 1),
    black_personnel = (SELECT COUNT(*)as black_personnel FROM alert_record
    	WHERE DATE(create_time) = CURDATE() and alert_object = 1 and alert_type = 2),
    black_car = (SELECT COUNT(*)as black_personnel FROM alert_record
    	WHERE DATE(create_time) = CURDATE() and alert_object = 2 and alert_type = 2),
    state_id = 1,statistical_date = (select CURDATE() as statistical_date)
  </insert>

</mapper>