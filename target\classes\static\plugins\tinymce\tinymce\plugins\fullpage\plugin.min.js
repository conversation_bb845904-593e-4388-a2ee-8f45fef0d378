/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.2 (2020-08-17)
 */
!function(p){"use strict";var s=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},e=tinymce.util.Tools.resolve("tinymce.PluginManager"),u=function(){return(u=Object.assign||function(e){for(var t,n=1,l=arguments.length;n<l;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},y=tinymce.util.Tools.resolve("tinymce.util.Tools"),t=tinymce.util.Tools.resolve("tinymce.html.DomParser"),m=tinymce.util.Tools.resolve("tinymce.html.Node"),f=tinymce.util.Tools.resolve("tinymce.html.Serializer"),h=function(e){return e.getParam("fullpage_hide_in_source_view")},r=function(e){return e.getParam("fullpage_default_encoding")},g=function(e){return e.getParam("fullpage_default_font_family")},v=function(e){return e.getParam("fullpage_default_font_size")},_=function(e){return t({validate:!1,root_name:"#document"}).parse(e,{format:"xhtml"})},d=function(l,i){var e,t,n,r,o,a,c=(e=l,t=i.get(),o=_(t),(a={}).fontface=g(e),a.fontsize=v(e),7===(n=o.firstChild).type&&(a.xml_pi=!0,(r=/encoding="([^"]+)"/.exec(n.value))&&(a.docencoding=r[1])),(n=o.getAll("#doctype")[0])&&(a.doctype="<!DOCTYPE"+n.value+">"),(n=o.getAll("title")[0])&&n.firstChild&&(a.title=n.firstChild.value),y.each(o.getAll("meta"),function(e){var t,n=e.attr("name"),l=e.attr("http-equiv");n?a[n.toLowerCase()]=e.attr("content"):"Content-Type"===l&&(t=/charset\s*=\s*(.*)\s*/gi.exec(e.attr("content")))&&(a.docencoding=t[1])}),(n=o.getAll("html")[0])&&(a.langcode=s(n,"lang")||s(n,"xml:lang")),a.stylesheets=[],y.each(o.getAll("link"),function(e){"stylesheet"===e.attr("rel")&&a.stylesheets.push(e.attr("href"))}),(n=o.getAll("body")[0])&&(a.langdir=s(n,"dir"),a.style=s(n,"style"),a.visited_color=s(n,"vlink"),a.link_color=s(n,"link"),a.active_color=s(n,"alink")),a);function s(e,t){return e.attr(t)||""}var d=u(u({},{title:"",keywords:"",description:"",robots:"",author:"",docencoding:""}),c);l.windowManager.open({title:"Metadata and Document Properties",size:"normal",body:{type:"panel",items:[{name:"title",type:"input",label:"Title"},{name:"keywords",type:"input",label:"Keywords"},{name:"description",type:"input",label:"Description"},{name:"robots",type:"input",label:"Robots"},{name:"author",type:"input",label:"Author"},{name:"docencoding",type:"input",label:"Encoding"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:d,onSubmit:function(e){var t=e.getData(),n=function(e,r,t){var n,o,l,i=e.dom;function a(e,t,n){e.attr(t,n||undefined)}function c(e){n.firstChild?n.insert(e,n.firstChild):n.append(e)}var s=_(t);(n=s.getAll("head")[0])||(o=s.getAll("html")[0],n=new m("head",1),o.firstChild?o.insert(n,o.firstChild,!0):o.append(n)),o=s.firstChild,r.xml_pi?(l='version="1.0"',r.docencoding&&(l+=' encoding="'+r.docencoding+'"'),7!==o.type&&(o=new m("xml",7),s.insert(o,s.firstChild,!0)),o.value=l):o&&7===o.type&&o.remove(),o=s.getAll("#doctype")[0],r.doctype?(o||(o=new m("#doctype",10),r.xml_pi?s.insert(o,s.firstChild):c(o)),o.value=r.doctype.substring(9,r.doctype.length-1)):o&&o.remove(),o=null,y.each(s.getAll("meta"),function(e){"Content-Type"===e.attr("http-equiv")&&(o=e)}),r.docencoding?(o||((o=new m("meta",1)).attr("http-equiv","Content-Type"),o.shortEnded=!0,c(o)),o.attr("content","text/html; charset="+r.docencoding)):o&&o.remove(),o=s.getAll("title")[0],r.title?(o?o.empty():c(o=new m("title",1)),o.append(new m("#text",3)).value=r.title):o&&o.remove(),y.each("keywords,description,author,copyright,robots".split(","),function(e){var t,n,l=s.getAll("meta"),i=r[e];for(t=0;t<l.length;t++)if((n=l[t]).attr("name")===e)return void(i?n.attr("content",i):n.remove());i&&((o=new m("meta",1)).attr("name",e),o.attr("content",i),o.shortEnded=!0,c(o))});var d={};y.each(s.getAll("link"),function(e){"stylesheet"===e.attr("rel")&&(d[e.attr("href")]=e)}),y.each(r.stylesheets,function(e){d[e]||((o=new m("link",1)).attr({rel:"stylesheet",text:"text/css",href:e}),o.shortEnded=!0,c(o)),delete d[e]}),y.each(d,function(e){e.remove()}),(o=s.getAll("body")[0])&&(a(o,"dir",r.langdir),a(o,"style",r.style),a(o,"vlink",r.visited_color),a(o,"link",r.link_color),a(o,"alink",r.active_color),i.setAttribs(e.getBody(),{style:r.style,dir:r.dir,vLink:r.visited_color,link:r.link_color,aLink:r.active_color})),(o=s.getAll("html")[0])&&(a(o,"lang",r.langcode),a(o,"xml:lang",r.langcode)),n.firstChild||n.remove();var u=f({validate:!1,indent:!0,indent_before:"head,html,body,meta,title,script,link,style",indent_after:"head,html,body,meta,title,script,link,style"}).serialize(s);return u.substring(0,u.indexOf("</body>"))}(l,y.extend(c,t),i.get());i.set(n),e.close()}})},b=y.each,x=function(e){return e.replace(/<\/?[A-Z]+/g,function(e){return e.toLowerCase()})},k=function(e,t,n,l){var i,r,o,a,c,s="",d=e.dom;if(!l.selection&&(a=e.getParam("protect"),c=l.content,y.each(a,function(e){c=c.replace(e,function(e){return"\x3c!--mce:protected "+escape(e)+"--\x3e"})}),o=c,!("raw"===l.format&&t.get()||l.source_view&&h(e)))){0!==o.length||l.source_view||(o=y.trim(t.get())+"\n"+y.trim(o)+"\n"+y.trim(n.get())),-1!==(i=(o=o.replace(/<(\/?)BODY/gi,"<$1body")).indexOf("<body"))?(i=o.indexOf(">",i),t.set(x(o.substring(0,i+1))),-1===(r=o.indexOf("</body",i))&&(r=o.length),l.content=y.trim(o.substring(i+1,r)),n.set(x(o.substring(r)))):(t.set(C(e)),n.set("\n</body>\n</html>"));var u=_(t.get());b(u.getAll("style"),function(e){e.firstChild&&(s+=e.firstChild.value)});var m=u.getAll("body")[0];m&&d.setAttribs(e.getBody(),{style:m.attr("style")||"",dir:m.attr("dir")||"",vLink:m.attr("vlink")||"",link:m.attr("link")||"",aLink:m.attr("alink")||""}),d.remove("fullpage_styles");var f=e.getDoc().getElementsByTagName("head")[0];if(s)d.add(f,"style",{id:"fullpage_styles"}).appendChild(p.document.createTextNode(s));var g={};y.each(f.getElementsByTagName("link"),function(e){"stylesheet"===e.rel&&e.getAttribute("data-mce-fullpage")&&(g[e.href]=e)}),y.each(u.getAll("link"),function(e){var t=e.attr("href");if(!t)return!0;g[t]||"stylesheet"!==e.attr("rel")||d.add(f,"link",{rel:"stylesheet",text:"text/css",href:t,"data-mce-fullpage":"1"}),delete g[t]}),y.each(g,function(e){e.parentNode.removeChild(e)})}},C=function(e){var t,n="",l="";if(e.getParam("fullpage_default_xml_pi")){var i=r(e);n+='<?xml version="1.0" encoding="'+(i||"ISO-8859-1")+'" ?>\n'}return n+=e.getParam("fullpage_default_doctype","<!DOCTYPE html>"),n+="\n<html>\n<head>\n",(t=e.getParam("fullpage_default_title"))&&(n+="<title>"+t+"</title>\n"),(t=r(e))&&(n+='<meta http-equiv="Content-Type" content="text/html; charset='+t+'" />\n'),(t=g(e))&&(l+="font-family: "+t+";"),(t=v(e))&&(l+="font-size: "+t+";"),(t=e.getParam("fullpage_default_text_color"))&&(l+="color: "+t+";"),n+="</head>\n<body"+(l?' style="'+l+'"':"")+">\n"},A=function(e,t,n,l){l.selection||l.source_view&&h(e)||(l.content=(y.trim(t)+"\n"+y.trim(l.content)+"\n"+y.trim(n)).replace(/<!--mce:protected ([\s\S]*?)-->/g,function(e,t){return unescape(t)}))};!function n(){e.add("fullpage",function(e){var t,n,l,i,r,o,a=s(""),c=s("");n=a,(t=e).addCommand("mceFullPageProperties",function(){d(t,n)}),(l=e).ui.registry.addButton("fullpage",{tooltip:"Metadata and document properties",icon:"document-properties",onAction:function(){l.execCommand("mceFullPageProperties")}}),l.ui.registry.addMenuItem("fullpage",{text:"Metadata and document properties",icon:"document-properties",onAction:function(){l.execCommand("mceFullPageProperties")}}),r=a,o=c,(i=e).on("BeforeSetContent",function(e){k(i,r,o,e)}),i.on("GetContent",function(e){A(i,r.get(),o.get(),e)})})}()}(window);