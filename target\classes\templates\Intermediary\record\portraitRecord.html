<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>人像采集记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <style>
        /*固定input宽度*/
        .layui-input, .layui-textarea {
            display: block;
            width: 180px;
            padding-left: 10px;
        }

        #portrait-table tr {
            vertical-align: middle;
        }

        tbody .layui-table-cell {
            vertical-align: middle;
            vertical-align: middle;
        }

        .trend-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin-bottom: 24px;
            padding: 0 0 16px 0;
        }
        .trend-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px 0 24px;
        }
        .trend-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .trend-tabs {
            display: flex;
            gap: 8px;
        }
        .trend-tab {
            background: #f5f7fa;
            border: none;
            border-radius: 4px;
            color: #333;
            padding: 4px 18px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        .trend-tab.active, .trend-tab:hover {
            background: #1890ff;
            color: #fff;
        }

        .car-info-bar {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 18px 0 10px 0;
            font-size: 20px;
            font-weight: bold;
            border-radius: 6px;
            padding: 10px 0px;
            color: #fff;
            min-width: 320px;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        .car-info-bar.blue { background: #1890ff; }
        .car-info-bar.green { background: #4caf50; }
        .car-info-bar.yellow { background: #ffbb00; color: #333; }
        .car-info-bar.white { background: #fff; color: #333; border: 1px solid #eee; }
        .car-info-bar.black { background: #222; }
        .car-info-bar.red { background: #e53935; }
        .car-info-bar.orange { background: #ff9800; color: #333; }
    </style>
</head>

<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <div class="layui-inline">
                    <input type="text" name="startDate" id="startDate" placeholder="开始日期" autocomplete="off"
                           class="layui-input">
                </div>
                <div class="layui-inline">
                    <input type="text" name="endDate" id="endDate" placeholder="结束日期" autocomplete="off"
                           class="layui-input">
                </div>
                <!--                地点-->
                <div class="layui-inline">
                    <label class="layui-form-label">地点：</label>
                    <div class="layui-input-inline">
                        <div class="xm-select-demo" id="typeSelect"></div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                    <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                    <button class="layui-btn" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                </div>
            </blockquote>

            <div id="car-info-bar" class="car-info-bar">
                <!-- 这里内容由JS动态填充 -->
            </div>

            <div class="trend-card">
                <div class="trend-card-header">
                    <span class="trend-title">记录趋势</span>
<!--                    <div class="trend-tabs">-->
<!--                        <button class="trend-tab active" data-type="week">周</button>-->
<!--                        <button class="trend-tab" data-type="month">月</button>-->
<!--                        <button class="trend-tab" data-type="day">日</button>-->
<!--                    </div>-->
                </div>
                <div id="trendChart" style="width:100%;height:260px;"></div>
            </div>


            <table class="layui-hide" id="portrait-table" lay-filter="portrait-table"></table>

            <script type="text/html" id="table-toolbar">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">
                        <i class="layui-icon">&#xe640;</i>删除
                    </button>
                    <div class="layui-btn-group" style="float: right;">
                        <button class="layui-btn layui-btn-sm" lay-event="toBeWhite"
                                th:if="${portraitCollectionImage.type != -1 && portraitCollectionImage.type != 2}">
                            <i class="layui-icon layui-icon-vercode"></i>设为白名单
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="toBeBlack"
                                th:if="${portraitCollectionImage.type != -1 && portraitCollectionImage.type != 2}">
                            <i class="layui-icon layui-icon-disabled"></i>列为黑名单
                        </button>
                        <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="toBeSuspected"
                                th:if="${portraitCollectionImage.type != -1 && portraitCollectionImage.type != 1}">
                            <i class="layui-icon layui-icon-question"></i>设为疑似人员
                        </button>
                    </div>
                </div>
            </script>


            <script type="text/html" id="table-toolbar-bar">
                <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
        </div>
    </div>
</div>
<script th:inline="javascript">
    var personCode = /*[[${portraitCollectionImage.code}]]*/ null;
    var count = [[${count}]]
    console.log( count);
</script>

<script th:src="@{/plugins/echarts/echarts.min.js}"></script>

<script>

    var deptId = [[${deptId}]];
    console.log(deptId);

    //回车搜索
    $("blockquote").on('keyup', 'input,select', function (e) {
        if (e.keyCode == 13) {//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });

    layui.use(['laydate', 'table'], function () {
        var admin = layui.admin
            , table = layui.table;
        var laydate = layui.laydate;
        // 渲染日期选择器
        laydate.render({
            elem: '#startDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            max: new Date().toLocaleString('zh-CN', {hour12: false}).replace(/\//g, '-').replace(',', '')
        });

        laydate.render({
            elem: '#endDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            max: new Date().toLocaleString('zh-CN', {hour12: false}).replace(/\//g, '-').replace(',', ''),

        });
        var tableIns = table.render({
            elem: '#portrait-table'
            ,id:'portrait-table'
            , defaultToolbar: [
                {
                    title: '导出'
                    ,layEvent: 'exports1'
                    ,icon: 'layui-icon-export'
                },
                'print','filter']
            , height: 'full-50'
            , url: ctx + 'portraitCollectionRecordController/findRecordByCondition'
            , toolbar: '#table-toolbar'
            , title: '人像采集记录'
            , cols: [
                [
                    {type: 'checkbox'}
                    , {field: 'deptName', title: '部门', align: 'center'}
                    , {field: 'cameraLocation', title: '记录地点', align: 'center'}
                    , {field: 'createTime', title: '记录时间', align: 'center'}
                    , {fixed: 'right', title: '操作', toolbar: '#table-toolbar-bar', align: 'center'}
                ]
            ]
            , request: {
                pageName: 'currentPage' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , limit: 10
            , limits: [5, 10, 20, 50]// 可选的每页显示条目数选项
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true,
            where: {
                personCode: personCode
            },
            method: 'post', // 设置请求方法为 POST
            contentType: 'application/json', // 设置请求内容类型为 JSON
        })
        ;


        //头工具栏事件
        table.on('toolbar(portrait-table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'toBeWhite':
                    layer.open({
                        type: 2,
                        title: "编辑白名单人员信息",
                        shadeClose: true,
                        area: ['700px', '500px'],
                        btn: ['保存', '取消'],
                        content: ctx + 'portraitCollectionImageController/insertWhiteDialog/' + personCode,
                        yes: function (index, layero) { //当前层索引、当前层DOM对象
                            //提交表单
                            var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                            submit.click();// 触发提交监听
                            return false;
                        }
                    });
                    break;
                case 'toBeSuspected':
                    layer.open({
                        type: 2,
                        title: "编辑详细疑似人员信息",
                        shadeClose: true,
                        area: ['700px', '500px'],
                        btn: ['保存', '取消'],
                        content: ctx + 'portraitCollectionImageController/insertSuspectedDialog/' + personCode,
                        yes: function (index, layero) { //当前层索引、当前层DOM对象
                            //提交表单
                            var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                            submit.click();// 触发提交监听
                            return false;
                        }
                    });
                    break;
                case 'toBeBlack':
                    layer.open({
                        type: 2,
                        title: "编辑详细黑名单信息",
                        shadeClose: true,
                        area: ['700px', '500px'],
                        btn: ['保存', '取消'],
                        content: ctx + 'portraitCollectionImageController/insertBlackDialog/' + personCode,
                        yes: function (index, layero) { //当前层索引、当前层DOM对象
                            //提交表单
                            var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                            submit.click();// 触发提交监听
                            return false;
                        }
                    });
                    break;
                case 'del':
                    var data = checkStatus.data;
                    if (data.length < 1) {
                        layer.msg('请至少选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    //获取选中的行id
                    var ids = [];
                    for (var i = 0; i < data.length; i++) {
                        console.log(data[i])
                        ids.push(data[i].code);
                    }

                    layer.confirm('您确定要删除吗？', function (index) {
                        var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                        $.ajax({
                            url: ctx + 'portraitCollectionRecordController/deleteByCodeList',
                            method: 'post',
                            data: JSON.stringify(ids),
                            contentType: "application/json",
                            dataType: 'JSON',
                            success: function (res) {
                                layer.close(loading);
                                if (res.code != 0) {
                                    layer.alert(res.msg, {icon: 2});
                                } else {
                                    table.reload('portrait-table'); //只重载数据
                                    layer.msg(res.msg, {icon: 1});
                                }
                            },
                            error: function (res) {
                                layer.close(loading);
                                layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                            }
                        });
                        layer.close(index);
                    });
                    break;
                case 'exports1':
                    var data = checkStatus.data;
                    if (data.length === 0){
                        layer.confirm('未勾选数据，是否导出当前页面数据', {
                            title: '操作确认',  // 弹窗标题
                            icon: 0,          // 图标类型（0-信息，1-成功，2-错误，3-问号）
                            btn: ['确定', '取消'] // 按钮文本
                        }, function(index){
                            // 用户点击"确定"后的回调
                            var currentData = table.cache['portrait-table'];
                            table.exportFile('portrait-table', currentData, 'xls');
                            layer.close(index); // 关闭弹窗
                        }, function(index){
                            // 用户点击"取消"后的回调
                            layer.close(index); // 关闭弹窗
                            console.log("操作取消");
                        });
                    }else {
                        layer.confirm('已勾选数据，是否导出勾选数据', {
                            title: '操作确认',  // 弹窗标题
                            icon: 0,          // 图标类型（0-信息，1-成功，2-错误，3-问号）
                            btn: ['确定', '取消'] // 按钮文本
                        }, function(index){
                            // 用户点击"确定"后的回调
                            table.exportFile('portrait-table', data, 'xls');
                            layer.close(index); // 关闭弹窗
                        }, function(index){
                            // 用户点击"取消"后的回调
                            layer.close(index); // 关闭弹窗
                            console.log("操作取消");
                        })
                    }
                    break;
            }
            ;
        });

        //监听行工具事件
        table.on('tool(portrait-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('您确定要删除吗？', function (index) {
                    var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                    $.ajax({
                        url: ctx + 'portraitCollectionRecordController/deleteById/' + data.code,
                        method: 'post',
                        data: {id: data.id},
                        dataType: 'JSON',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code != 0) {
                                layer.alert(res.msg, {icon: 2});
                            } else {
                                table.reload('portrait-table'); //只重载数据
                                layer.msg(res.msg, {icon: 1});
                            }
                        },
                        error: function (res) {
                            layer.close(loading);
                            layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                        }
                    });
                    layer.close(index);
                });
            } else if (obj.event === 'detail') {
                layer.open({
                    type: 2,
                    title: "人像记录详细",
                    shadeClose: true,
                    area: ['700px', '500px'],
                    btn: ['关闭'],
                    content: ctx + 'portraitCollectionRecordController/recordInfo/' + data.code
                });
            }
        });

        //搜索及重置按钮
        $("#searchBtn").click(function () {

            const carModel = []
            typeSelect.getValue().forEach(data => {
                carModel.push(data.ID)
            })

            tableIns.where = {};

            table.reload('portrait-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    startDate: $("#startDate").val(),
                    endDate: $("#endDate").val(),
                    personCode: personCode,
                    deptIds: carModel
                },
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                method: 'post',
                contentType: 'application/json' ,// 设置请求的内容类型为 JSON
                done: function(res) {
                    // 数据加载完成后的回调
                    console.log('表格重载完成', res);
                    // this.where = {};
                    tableIns = this;

                    var personCount = res.count || 0;
                    renderPersonInfoBar(personCount );
                }
            }); //只重载数据
            // 初始渲染
            renderTrendChart(personCode,1);
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#startDate").val("");
            $("#endDate").val("");

            table.reload('portrait-table', {
                where: {
                    personCode: personCode
                },
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                method: 'post',
                contentType: 'application/json' // 设置请求的内容类型为 JSON
            }); //只重载数据
        })

        var trendData = {
            1: {
                x: ['1/2','2/2','3/2','4/2','5/2','6/2','7/2'],
                y1: [120, 132, 201, 234, 190, 230, 210],
                y2: [110, 122, 191, 224, 180, 220, 200]
            },
            2: {
                x: ['1月','2月','3月','4月','5月','6月'],
                y1: [320, 332, 301, 334, 390, 330],
                y2: [310, 322, 291, 324, 380, 320]
            },
            3: {
                x: ['8:00','10:00','12:00','14:00','16:00','18:00'],
                y1: [12, 32, 21, 34, 19, 30],
                y2: [11, 22, 19, 24, 18, 22]
            }
        };

        var typeSelect = xmSelect.render({
            el: '#typeSelect',
            radio: false,
            // clickClose: true,
            filterable: true,
            toolbar: {show: true},
            name: 'deptIds',
            layVerify: 'required',
            prop: {name: 'deptName', value: 'ID'},
            data: [],
            // 启用严格模式，确保不会有重复选中项
            strict: true,
            style: {
                paddingLeft: '0px',
                position: 'relative',
                width: '160px',
                height: '38px'
            },
            tree: {
                show: true,
                strict: false, //是否父子结构，父子结构父节点不会被选中
                indent: 30,//间距
                expandedKeys: [-1],
                clickCheck: true,
                clickExpand: true,//点击展开
            },

        });

        $.ajax({
            url: ctx + '/deptController/deptDetail',
            type: 'PUT',
            success: function (res) {

                // // 1. 将ID统一转为字符串
                // var data = res.data.map(item => {
                //     return {
                //         deptName: item.deptName,
                //         ID: item.ID.toString() // 确保ID是字符串
                //     };
                // });

                // 3. 更新下拉框数据
                typeSelect.update({
                    data: res.data,
                });

                // 4. 设置初始值（确保deptId转为字符串）
                var initValue = deptId ? deptId.toString() : "";
                if (initValue) {
                    typeSelect.setValue([initValue]);
                }
            }
        });

        // 获取时间段的车辆采集记录趋势图数据
        function renderTrendChart(personCode,status) {

            var chart = echarts.init(document.getElementById('trendChart'));

            console.log("typeSelect.getValue",typeSelect.getValue())
            const startDate = $("#startDate").val();
            const endDate = $("#endDate").val();

            const carModel = []
            var value = typeSelect.getValue();
            if (value.length > 0) {
                value.forEach(data => {
                    carModel.push(data.ID)
                })
            }

            // 调用接口获取车辆采集的详细数据
            $.ajax({
                url: ctx + '/homeController/peopleWarningCount',
                type: 'POST',
                data: JSON.stringify({
                    personCode: personCode ,
                    status: status,
                    deptIds:carModel,
                    startDate: startDate,
                    endDate: endDate
                }),
                contentType: 'application/json' ,
                success: function(res) {
                    if (res.code === 0) {
                        var data = res.data;

                        // 初始化时间段数组和预警数据数组
                        var hours = [];
                        var peopleWarningData = [];

                        // 处理数据
                        data.forEach(function(item,index) {
                            // 添加月份标签
                            hours.push(item.hour + '点');
                            peopleWarningData.push(item.peopleWarningCount || 0)
                        });

                        var option = {
                            tooltip: { trigger: 'axis' },
                            legend: { data: ['人像采集记录'] },
                            grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                            xAxis: { type: 'category', data: hours },
                            yAxis: { type: 'value' },
                            series: [
                                {
                                    name: '人像采集记录',
                                    type: 'line',
                                    data: peopleWarningData,
                                    smooth: true
                                }
                            ]
                        };
                        chart.setOption(option);
                        window.addEventListener('resize', function(){ chart.resize(); });
                    } else {
                        layer.msg('获取趋势分析数据失败: ' + res.msg);
                        // 显示错误信息
                        $('#trendChart').html('<div style="text-align:center;padding:50px;color:#ff6b6b;">加载失败</div>');
                    }
                },
                error: function() {
                    layer.msg('获取趋势分析图表接口请求失败');
                    // 显示错误信息
                    $('#trendChart').html('<div style="text-align:center;padding:50px;color:#ff6b6b;">加载失败</div>');
                }
            });
        }


        // 初始渲染
        renderTrendChart(personCode,1);

        // 切换按钮事件
        $('.trend-tab').on('click', function(){
            $('.trend-tab').removeClass('active');
            $(this).addClass('active');
            var type = $(this).data('type');
            var status;
            if (type === 'week'){
                status=1;
            }else if (type === 'month'){
                status=2;
            }else if (type === 'day'){
                status=3;
            }
            renderTrendChart(personCode,status);
        });

        // 车牌颜色判断
        function getCarColorClass() {
            return 'blue';
        }
        function renderPersonInfoBar(count){
            var colorClass = getCarColorClass();
            var html = '<span> 该人员出现总次数' + count + '次</span>';
            $('#car-info-bar').attr('class', 'car-info-bar ' + colorClass).html(html);
        }

        renderPersonInfoBar(count);
    });

</script>

</body>
</html>