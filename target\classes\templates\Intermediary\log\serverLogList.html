<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>服务日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <link rel="stylesheet" th:href="@{/plugins/dtree/dtree.css}">
    <link rel="stylesheet" th:href="@{/plugins/dtree/font/dtreefont.css}">

</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form">
                <blockquote class="layui-elem-quote quoteBox" id="search">
                    <input type="hidden" id="treeDeptId">
                    <div>
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto">服务名：</label>
                            <div class="layui-input-inline mr0">
                                <input id="serverName" class="layui-input" type="text" placeholder="请输入服务名">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto">操作状态：</label>
                            <div class="layui-input-inline" style="width: 180px;">
                                <select name="stateId" id="stateId">
                                    <option value="">请选择操作状态</option>
                                    <option value="1">成功</option>
                                    <option value="2">失败</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto">服务描述：</label>
                            <div class="layui-input-inline mr0">
                                <input id="serverDesc" class="layui-input" type="text" placeholder="请输入服务描述">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">开始日期：</label>
                            <div class="layui-input-inline">
                                <input readonly class="layui-input" id="startDate" name="startDate" autocomplete="off"
                                       placeholder="请输入开始日期">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">结束日期：</label>
                            <div class="layui-input-inline">
                                <input readonly class="layui-input" id="endDate" name="endDate" autocomplete="off"
                                       placeholder="请输入结束日期">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                            <button class="layui-btn icon-btn " type="button" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询
                            </button>
                            <button type="reset" class="layui-btn layui-btn-primary " id="unsetBtn"><i
                                    class="layui-icon">&#xe669;</i>重置
                            </button>
                        </div>
                    </div>
                </blockquote>
            </form>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md2">
                    <div style="margin-top: 10px;">
                        <ul id="dataTree" class="dtree" th:data-id="${deptId}"></ul>
                    </div>
                </div>
                <div class="layui-col-md10">
                    <table class="layui-hide" id="zzsb-table" lay-filter="zzsb-table"></table>
                    <script type="text/html" id="test-table-toolbar-barDemo">
                        <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
                    </script>
                    <script type="text/html" id="table-toolbar-top">
                        <button class="layui-btn  layui-btn-sm" lay-event="print"><i class="layui-icon">&#xe629;</i>报表
                        </button>
                    </script>
                </div>
            </div>

            <script type="text/html" id="zzsbtoolbar">
        </script>
        </div>
    </div>
</div>
<script th:inline="javascript">
    //回车搜索
    $("blockquote").on('keyup', 'input,select', function (e) {
        if (e.keyCode == 13) {//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    var deptId = [[${deptId}]];
    var formSelects = layui.formSelects;

    function timeRender(attrName, value, minOrMax, type) {
        //每当日期被选择完的时候重新渲染另外一个日期组件  限制最大或者是最小值  就不会出现日期重叠的情况。
        // alert(minOrMax);
        var date = new Date();
        layui.use(['laydate'], function () {
            var laydate = layui.laydate;
            $('input[name$=' + attrName + ']').each(function () {
                $(this).removeAttr('lay-key');
                var clone = $(this).clone().appendTo($(this).parent().empty())[0];//解决最大和最少值无法重新渲染问题
                if (type == 1) {
                    //表示开始日期
                    var secDate = laydate.render({
                        elem: clone,
                        value: value,
                        btns: ['clear', 'confirm'],
                        max: minOrMax == '' || minOrMax == null ? date.getFullYear() + 1 + '-12-31' : minOrMax,  //如果为空或者为null会限制当天选择  给一个初始值
                        type: 'datetime',
                        done: function (value, date, endDate) {
                            this.value = value
                            this.elem.val(value)

                            timeRender("endDate", $("#endDate").val(), value, 0)
                            $("#endDate").focus();
                        },
                        change: function (value, date) { //监听日期被切换
                            var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            start = new Date(Date.parse(start));
                            var end = $("#endDate").val().replace("-", "/");
                            end = new Date(Date.parse(end));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                secDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                } else {
                    var secDate = laydate.render({
                        elem: clone,
                        value: value,
                        min: minOrMax == '' || minOrMax == null ? '2010-12-31' : minOrMax, //初始最小限制时间
                        type: 'datetime',
                        btns: ['clear', 'confirm'],
                        done: function (value, date, endDate) {
                            this.value = value
                            this.elem.val(value)

                            timeRender('startDate', $("#startDate").val(), value, 1)
                        },
                        change: function (value, date) { //监听日期被切换
                            var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            end = new Date(Date.parse(end));
                            var start = $("#startDate").val().replace("-", "/");
                            start = new Date(Date.parse(start));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                secDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                }

            })
        });
    }

    layui.extend({
        dtree: ctx + 'plugins/dtree/dtree'
    }).use(['table', 'layer', 'jquery', 'form', 'dtree', 'laydate'], function () {
        var admin = layui.admin;
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var dtree = layui.dtree;
        var laydate = layui.laydate;
        //日期
        var ends1 = laydate.render({
            elem: '#endDate',
            type: 'datetime',
            btns: ['clear', 'confirm'],
            done: function (value, date, endDate) {
                this.value = value
                this.elem.val(value)

                timeRender('startDate', $("#startDate").val(), value, 1)
            },
            change: function (value, date) { //监听日期被切换
                var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                end = new Date(Date.parse(end));
                var start = $("#startDate").val().replace("-", "/");
                start = new Date(Date.parse(start));
                if (start > end) { //点击2017年8月15日，弹出提示语
                    ends1.hint('开始时间不得大于结束时间');
                }
                return false;
            }
        });
        var starts1 = laydate.render({
            elem: '#startDate',
            type: 'datetime',
            // max: 'null',
            btns: ['clear', 'confirm'],
            done: function (value, date, endDate) {
                this.value = value
                this.elem.val(value)

                timeRender('endDate', $("#endDate").val(), value, 0)
                $("#endDate").focus();
            },
            change: function (value, date) { //监听日期被切换
                var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                start = new Date(Date.parse(start));
                var end = $("#endDate").val().replace("-", "/");
                end = new Date(Date.parse(end));
                if (start > end) {
                    starts1.hint('开始时间不得大于结束时间');
                }
                return false;
            }
        });
        table.on('tool(zzsb-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.open({
                    type: 2,
                    title: "查看日志详情",
                    shadeClose: true,
                    btn: "返回",
                    area: ['99%', '95%'],
                    offset: [ //为了演示，随机坐标
                        0.1 * ($(window).height() - 400)
                    ]
                    , content: ctx + 'serverLogController/logDetail' + data.code
                });
            }
        });

        var ins1 = table.render({
            elem: '#zzsb-table'
            , url: ctx + 'serverLogController/findByCondition'
            , method: 'post'
            , defaultToolbar: ['exports', 'print','filter']
            , toolbar: '#table-toolbar-top'
            , title: '登录日志表'
            , cols: [
                [
                    {field: 'serverName', title: '服务名', align: 'center', width: 120}
                    , {field: 'deptName', title: '所属部门', width: '150', align: 'center', width: 120}
                    , {
                    field: 'stateId', title: '操作状态', width: '150', align: 'center', templet: function (data) {
                        if (data.stateId == '2') {
                            return '失败';
                        } else {
                            return '成功';
                        }
                    }
                }
                    , {field: 'serverDesc', width: '150', title: '操作说明', align: 'center'}

                    , {field: 'createTime', width: '200', title: '操作时间', align: 'center'}
                    , {
                    title: '操作',
                    toolbar: '#test-table-toolbar-barDemo',
                    fixed: 'right',
                    width: '100',
                    align: 'center'
                }
                ]
            ]

            , request: {
                pageName: 'currentPage' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , limit: 10
            , limits: [5, 10, 20, 50]// 可选的每页显示条目数选项
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true
            , done: function (res, page, count) {
                var that = this.elem.next();
                res.data.forEach(function (item, index) {
                    if (item.stateId == '2') {
                        var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']").css("background-color", "#FFFF00");
                    }
                });
            }
            , contentType: 'application/json', // 设置请求内容类型为 JSON
        });
        form.render();

        // 初始化树
        var DTreeNode = dtree.render({
            elem: "#dataTree",
            url: ctx + "deptController/depTreeJson", // 使用url加载
            dataStyle: "layuiStyle",  //使用layui风格的数据格式
            dataFormat: "list",  //配置data的风格为list
            response: {message: "msg", statusCode: 0},  //修改response中返回数据的定义
            checkbar: true,//开启复选框
            checkbarType: "no-all",
            initLevel: 2,
            type: "all",
            line: true, // 有树线
            ficon: "2",  // 设定一级图标样式
            icon: "-1", // 不设定二级图标样式。
            skin: "laySimple"
        });

        // 绑定用复选框点击事件
        dtree.on("node(dataTree)", function (obj) {
            var nodeId = obj.param.nodeId;
            $("#treeDeptId").val(nodeId);
            table.reload('zzsb-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    deptId: nodeId,
                    stateId: $("#stateId").val(),
                    serverDesc: $("#serverDesc").val(),
                    serverName: $('#serverName').val(),
                    startDate: $('#startDate').val(),
                    endDate: $('#endDate').val(),
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });

        // 绑定dtree的展开关闭点击事件
        dtree.on("changeTree('dataTree')", function (obj) {
            var param = dtree.getChildParam(DTreeNode, obj.param.nodeId);
            if (obj.show && param.length == 0) {
                $.ajax({
                    url: ctx + 'deptController/getChildNode',
                    type: "POST",
                    async: true,
                    cache: false,
                    data: {
                        "parentId": obj.param.nodeId,
                        "checked": obj.param.checked
                    },
                    success: function (data) {
                        DTreeNode.getChild($(obj.dom).parent("div"), data);
                        //console.log($(obj.dom).parent("div"));
                    },
                    error: function (result) {
                        layer.msg("更新失败!", {icon: 5, time: 1000, skin: 'layer-ext-moon'});
                    }
                });
            }

        });
        //搜索及重置按钮
        $("#searchBtn").click(function () {
            var serverName = $("#serverName").val();
            var startDate = $("#startDate").val();
            var endDate = $("#endDate").val();
            var treeDeptId = $("#treeDeptId").val();
            var stateId = $("#stateId").val();
            table.reload('zzsb-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    deptId: treeDeptId,
                    serverName: serverName,
                    stateId: stateId,
                    serverDesc: $("#serverDesc").val(),
                    startDate: startDate,
                    endDate: endDate,
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
                , method: 'post'
                , contentType: 'application/json', // 设置请求内容类型为 JSON
            });
            return false;
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            window.location.reload();
        })

    });


</script>
</body>
</html>