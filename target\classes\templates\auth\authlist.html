<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>权限管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <div class="layui-inline">
                    <label class="layui-form-label w-auto" style="width: unset;padding: 9px 15px 9px 0px;">权限名称：</label>
                    <div class="layui-input-inline mr0">
                        <input class="layui-input" id="authName" autocomplete="off" placeholder="请输入权限名称">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                    <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                    <button class="layui-btn" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                </div>
            </blockquote>
            <table class="layui-hide" id="auth-table" lay-filter="auth-table"></table>

            <script type="text/html" id="table-toolbar">

                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe608;</i>增加</button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit"><i class="layui-icon">&#xe642;</i>编辑
                    </button>
                    <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del"><i class="layui-icon">&#xe640;</i>删除
                    </button>
                </div>
            </script>

            <script type="text/html" id="table-toolbar-bar">
                <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
        </div>
    </div>
</div>

<script>
    //回车搜索
    $("blockquote").on('keyup', 'input,select', function (e) {
        if (e.keyCode == 13) {//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.use('table', function () {
        var admin = layui.admin
            , table = layui.table;

        table.render({
            elem: '#auth-table'
            , defaultToolbar: ['filter']
            , height: 'full-50'
            , url: ctx + 'authController/authJson'
            , toolbar: '#table-toolbar'
            , title: '权限列表'
            , cols: [
                [
                    {type: 'checkbox'}
                    , {field: 'authName', title: '权限名称', align: 'center'}
                    , {fixed: 'right', title: '操作', toolbar: '#table-toolbar-bar', align: 'center'}
                ]
            ]
            , request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , limit: 20
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true
        });

        //头工具栏事件
        table.on('toolbar(auth-table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'add':
                    var data = checkStatus.data;
                    layer.open({
                        type: 2,
                        title: "添加权限",
                        shadeClose: true,
                        area: ['700px', '500px'],
                        btn: ['保存', '取消'],
                        content: ctx + 'authController/addAuth',
                        yes: function (index, layero) { //当前层索引、当前层DOM对象
                            //提交表单
                            var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                            submit.click();// 触发提交监听
                            return false;
                        }
                    });
                    break;
                case 'edit':
                    var data = checkStatus.data;
                    if (data.length < 1) {
                        layer.msg('请选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    if (data.length > 1) {
                        layer.msg('只能选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    layer.open({
                        type: 2,
                        title: "编辑权限",
                        shadeClose: true,
                        area: ['700px', '500px'],
                        btn: ['保存', '取消'],
                        content: ctx + 'authController/editAuth?id=' + checkStatus.data[0].id,
                        yes: function (index, layero) { //当前层索引、当前层DOM对象
                            //提交表单
                            var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                            submit.click();// 触发提交监听
                            return false;
                        }
                    });
                    break;
                case 'del':
                    var data = checkStatus.data;
                    if (data.length < 1) {
                        layer.msg('请至少选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    //获取选中的行id
                    var ids = '';
                    for (var i = 0; i < data.length; i++) {
                        ids += data[i].id + ",";
                    }
                    layer.confirm('您确定要删除吗？', function (index) {
                        var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                        $.ajax({
                            url: ctx + 'authController/batchDeleteAuth',
                            method: 'post',
                            data: {ids: ids},
                            dataType: 'JSON',
                            success: function (res) {
                                layer.close(loading);
                                if (res.code != 0) {
                                    layer.alert(res.msg, {icon: 2});
                                } else {
                                    table.reload('auth-table'); //只重载数据
                                    layer.msg(res.msg, {icon: 1});
                                }
                            },
                            error: function (res) {
                                layer.close(loading);
                                layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                            }
                        });
                        layer.close(index);
                    });
                    break;
            }
            ;
        });

        //监听行工具事件
        table.on('tool(auth-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('您确定要删除吗？', function (index) {
                    var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                    $.ajax({
                        url: ctx + 'authController/deleteAuth',
                        method: 'post',
                        data: {id: data.id},
                        dataType: 'JSON',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code != 0) {
                                layer.alert(res.msg, {icon: 2});
                            } else {
                                table.reload('auth-table'); //只重载数据
                                layer.msg(res.msg, {icon: 1});
                            }
                        },
                        error: function (res) {
                            layer.close(loading);
                            layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                        }
                    });
                    layer.close(index);
                });
            } else if (obj.event === 'edit') {
                layer.open({
                    type: 2,
                    title: "编辑权限",
                    shadeClose: true,
                    area: ['700px', '500px'],
                    btn: ['保存', '取消'],
                    content: ctx + 'authController/editAuth?id=' + data.id,
                    yes: function (index, layero) { //当前层索引、当前层DOM对象
                        //提交表单
                        var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                        submit.click();// 触发提交监听
                        return false;
                    }
                });
            } else if (obj.event === 'detail') {
                layer.open({
                    type: 2,
                    title: data.authName + "所属动作列表",
                    shadeClose: true,
                    area: ['700px', '500px'],
                    btn: ['关闭'],
                    content: ctx + 'authController/actionDialog?id=' + data.id
                });
            }
        });

        //搜索及重置按钮
        $("#searchBtn").click(function () {
            table.reload('auth-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    authName: $("#authName").val()
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            }); //只重载数据
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#authName").val("");
            table.reload('auth-table', {
                where: null
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            }); //只重载数据
        })
    });
</script>

</body>
</html>