{"data": [{"moduleCode": "", "detailCode": "405984", "description": "", "solution": "", "updateTime": 1559564188000}, {"moduleCode": "", "detailCode": "10035", "description": "获取子账户AccessToken异常,子账户不存在或子账户不属于该开发者", "solution": "", "updateTime": 1559551958000}, {"moduleCode": "", "detailCode": "1052674", "description": "获取本地录像失败", "solution": "", "updateTime": 1558579653000}, {"moduleCode": "", "detailCode": "395547", "description": "", "solution": "", "updateTime": 1557367296000}, {"moduleCode": "", "detailCode": "12", "description": "", "solution": "", "updateTime": 1557229476000}, {"moduleCode": "", "detailCode": "10052", "description": "余额不足", "solution": "", "updateTime": 1557121463000}, {"moduleCode": "", "detailCode": "20108", "description": "当前用户和所添加用户不是好友关系", "solution": "", "updateTime": 1556541725000}, {"moduleCode": "", "detailCode": "10009", "description": "", "solution": "", "updateTime": 1556422452000}, {"moduleCode": "", "detailCode": "320001", "description": "通道不存在", "solution": "请检查录像机的关联状态是否正常，没有摄像头的通道无法播放", "updateTime": 1556419044000}, {"moduleCode": "", "detailCode": "120001", "description": "通道不存在", "solution": "请检查录像机的关联状态是否正常，没有摄像头的通道无法播放", "updateTime": 1556419030000}, {"moduleCode": "", "detailCode": "320049", "description": "", "solution": "", "updateTime": 1556272984000}, {"moduleCode": "", "detailCode": "380227", "description": "", "solution": "", "updateTime": 1556264379000}, {"moduleCode": "", "detailCode": "10033", "description": "policy参数信息异常", "solution": "", "updateTime": 1555922124000}, {"moduleCode": "", "detailCode": "10065", "description": "weakAppKey 不属于accessToken对应的用户", "solution": "", "updateTime": 1555497522000}, {"moduleCode": "", "detailCode": "395416", "description": "设备达到最大连接数，无法建立连接", "solution": "请升级设备固件版本", "updateTime": 1555394722000}, {"moduleCode": "", "detailCode": "100001", "description": "", "solution": "", "updateTime": 1555141776000}, {"moduleCode": "", "detailCode": "395558", "description": "", "solution": "", "updateTime": 1554987121000}, {"moduleCode": "", "detailCode": "70001", "description": "智能家居买断用户设备受到限制,建议合理选择pagestart和pageSize", "solution": "", "updateTime": 1554691023000}, {"moduleCode": "", "detailCode": "170001", "description": "", "solution": "", "updateTime": 1554691023000}, {"moduleCode": "", "detailCode": "1012", "description": "重置失败", "solution": "", "updateTime": 1554645841000}, {"moduleCode": "", "detailCode": "1043", "description": "重置失败", "solution": "", "updateTime": 1554645834000}, {"moduleCode": "", "detailCode": "60060", "description": "直播功能未开通", "solution": "通道未开通直播功能，请先开通直播", "updateTime": 1554346018000}, {"moduleCode": "", "detailCode": "380203", "description": "", "solution": "", "updateTime": 1554093666000}, {"moduleCode": "", "detailCode": "399048", "description": "免费版并发数达到上限，请升级企业版使用多并发能力", "solution": "升级成为企业版套餐即可取消并发数限制", "updateTime": 1553839878000}, {"moduleCode": "", "detailCode": "60007", "description": "预置点个数超过最大值", "solution": "", "updateTime": 1553671316000}, {"moduleCode": "", "detailCode": "1005", "description": "", "solution": "", "updateTime": 1553513701000}, {"moduleCode": "", "detailCode": "20605", "description": "其他用户正在认证中", "solution": "", "updateTime": 1552976317000}, {"moduleCode": "", "detailCode": "90004", "description": "当前型号设备暂时不支持AI任务：CS-C3W-3B1WFR-YGL", "solution": "", "updateTime": 1552898525000}, {"moduleCode": "", "detailCode": "60046", "description": "添加的设备的IP和本设备的IP冲突", "solution": "", "updateTime": 1552872372000}, {"moduleCode": "", "detailCode": "3", "description": "修改视频清晰度失败!", "solution": "", "updateTime": 1552440229000}, {"moduleCode": "", "detailCode": "1013", "description": "", "solution": "", "updateTime": 1552035069000}, {"moduleCode": "", "detailCode": "370007", "description": "", "solution": "", "updateTime": 1551852327000}, {"moduleCode": "", "detailCode": "-1", "description": "", "solution": "", "updateTime": 1551752889000}, {"moduleCode": "", "detailCode": "30005", "description": "弱账户不存在", "solution": "", "updateTime": 1551422358000}, {"moduleCode": "", "detailCode": "90006", "description": "用户操作AI任务受限", "solution": "", "updateTime": 1551073320000}, {"moduleCode": "", "detailCode": "60203", "description": "未开通相关服务", "solution": "", "updateTime": 1550623070000}, {"moduleCode": "", "detailCode": "10002", "description": "accessToken过期或异常", "solution": "", "updateTime": 1550300346000}, {"moduleCode": "", "detailCode": "380339", "description": "", "solution": "", "updateTime": 1549889458000}, {"moduleCode": "", "detailCode": "90002", "description": "AI任务设备配置数达到上限：3", "solution": "", "updateTime": 1549071664000}, {"moduleCode": "", "detailCode": "380008", "description": "", "solution": "", "updateTime": 1549005979000}, {"moduleCode": "", "detailCode": "320227", "description": "", "solution": "", "updateTime": 1548739731000}, {"moduleCode": "", "detailCode": "60059", "description": "ezopen地址均不可用", "solution": "", "updateTime": 1548395350000}, {"moduleCode": "", "detailCode": "10005", "description": "appKey异常", "solution": "", "updateTime": 1548317858000}, {"moduleCode": "", "detailCode": "60045", "description": "添加的设备的IP和其他通道的IP冲突", "solution": "", "updateTime": 1548155085000}, {"moduleCode": "", "detailCode": "60047", "description": "码流类型不支持", "solution": "", "updateTime": 1547962108000}, {"moduleCode": "", "detailCode": "60041", "description": "添加的设备被其他设备关联或响应超时", "solution": "", "updateTime": 1547960980000}, {"moduleCode": "", "detailCode": "110029", "description": "个人用户接口调用频率超限", "solution": "请升级企业版：https://open.ys7.com/price.html", "updateTime": 1547606859000}, {"moduleCode": "", "detailCode": "380355", "description": "设备直连推流异常结束", "solution": "", "updateTime": 1547106294000}, {"moduleCode": "", "detailCode": "320081", "description": "", "solution": "", "updateTime": 1547106279000}, {"moduleCode": "", "detailCode": "60035", "description": "购买云存储服务失败", "solution": "", "updateTime": 1547026959000}, {"moduleCode": "", "detailCode": "90005", "description": "设备已存在：C75714141", "solution": "", "updateTime": 1546940622000}, {"moduleCode": "", "detailCode": "1053445", "description": "该时间段没有录像片段", "solution": "", "updateTime": 1546935727000}, {"moduleCode": "", "detailCode": "90007", "description": "设备未加入到AI任务", "solution": "", "updateTime": 1546932948000}, {"moduleCode": "", "detailCode": "326000", "description": "", "solution": "", "updateTime": 1546823143000}, {"moduleCode": "", "detailCode": "1021", "description": "重置失败", "solution": "", "updateTime": 1546781152000}, {"moduleCode": "", "detailCode": "2001", "description": "删除设备失败!", "solution": "", "updateTime": 1546422886000}, {"moduleCode": "", "detailCode": "380425", "description": "", "solution": "", "updateTime": 1546407694000}, {"moduleCode": "", "detailCode": "120097", "description": "", "solution": "", "updateTime": 1546085995000}, {"moduleCode": "", "detailCode": "10059", "description": "requestId已存在", "solution": "", "updateTime": 1545824509000}, {"moduleCode": "", "detailCode": "1154723", "description": "", "solution": "", "updateTime": 1545795209000}, {"moduleCode": "", "detailCode": "60043", "description": "添加的设备超出最大数量", "solution": "", "updateTime": 1545493607000}, {"moduleCode": "", "detailCode": "1152677", "description": "", "solution": "", "updateTime": 1545313404000}, {"moduleCode": "", "detailCode": "20097", "description": "设备添加异常,设备验证码为ABCDEF或设备被N1，R1关联", "solution": "", "updateTime": 1545310795000}, {"moduleCode": "", "detailCode": "10060", "description": "设备不支持该云存储类型", "solution": "", "updateTime": 1545309064000}, {"moduleCode": "", "detailCode": "20102", "description": "无相应邀请信息，无法接受邀请", "solution": "", "updateTime": 1545204966000}, {"moduleCode": "", "detailCode": "10053", "description": "云存储开通中", "solution": "", "updateTime": 1545100293000}, {"moduleCode": "", "detailCode": "20401", "description": "用户云空间信息不存在", "solution": "", "updateTime": 1545017880000}, {"moduleCode": "", "detailCode": "20600", "description": "临时密码数已达上限", "solution": "", "updateTime": 1544873457000}, {"moduleCode": "", "detailCode": "901", "description": "", "solution": "", "updateTime": 1544693519000}, {"moduleCode": "", "detailCode": "60210", "description": "图片数据错误", "solution": "", "updateTime": 1544604457000}, {"moduleCode": "", "detailCode": "10013", "description": "您的应用没有权限调用", "solution": "", "updateTime": 1544416237000}, {"moduleCode": "", "detailCode": "70007", "description": "授权码不存在", "solution": "", "updateTime": 1544179533000}, {"moduleCode": "", "detailCode": "10015", "description": "授权地址不存在", "solution": "", "updateTime": 1544163240000}, {"moduleCode": "", "detailCode": "320423", "description": "", "solution": "", "updateTime": 1544100685000}, {"moduleCode": "", "detailCode": "370009", "description": "", "solution": "", "updateTime": 1544077151000}, {"moduleCode": "", "detailCode": "10031", "description": "子账户或萤石用户没有权限", "solution": "", "updateTime": 1543990462000}, {"moduleCode": "", "detailCode": "10055", "description": "设备不支持试用云存储服务", "solution": "", "updateTime": 1543986292000}, {"moduleCode": "", "detailCode": "60042", "description": "添加的设备密码错误", "solution": "", "updateTime": 1543710913000}, {"moduleCode": "", "detailCode": "60082", "description": "设备正在响应本次声源定位", "solution": "", "updateTime": 1543647426000}, {"moduleCode": "", "detailCode": "10056", "description": "设备不支持云存储服务转出", "solution": "", "updateTime": 1543558342000}, {"moduleCode": "", "detailCode": "20104", "description": "好友不存在", "solution": "", "updateTime": 1543492403000}, {"moduleCode": "", "detailCode": "20111", "description": "好友不是等待验证状态，无法接受邀请", "solution": "", "updateTime": 1543492365000}, {"moduleCode": "", "detailCode": "20107", "description": "不能添加自己为好友", "solution": "", "updateTime": 1543480986000}, {"moduleCode": "", "detailCode": "1", "description": "设备返回其他错误", "solution": "", "updateTime": 1543459921000}, {"moduleCode": "", "detailCode": "60084", "description": "当前正在关闭隐私遮蔽", "solution": "", "updateTime": 1543456515000}, {"moduleCode": "", "detailCode": "380255", "description": "", "solution": "", "updateTime": 1543411652000}, {"moduleCode": "", "detailCode": "20015", "description": "设备不支持", "solution": "", "updateTime": 1543390936000}, {"moduleCode": "", "detailCode": "30003", "description": "手机验证码错误", "solution": "", "updateTime": 1543389137000}, {"moduleCode": "", "detailCode": "20615", "description": "锁用户已存在", "solution": "", "updateTime": 1543388325000}, {"moduleCode": "", "detailCode": "60061", "description": "账户流量已超出或未购买，限制开通", "solution": "", "updateTime": 1543372581000}, {"moduleCode": "", "detailCode": "60020", "description": "设备不支持该信令", "solution": "", "updateTime": 1543321636000}, {"moduleCode": "", "detailCode": "320146", "description": "", "solution": "", "updateTime": 1543318472000}, {"moduleCode": "", "detailCode": "60018", "description": "设备升级失败", "solution": "", "updateTime": 1543304928000}, {"moduleCode": "", "detailCode": "60044", "description": "添加的设备网络不可达超时", "solution": "", "updateTime": 1543304102000}, {"moduleCode": "", "detailCode": "20619", "description": "主用户无法删除", "solution": "", "updateTime": 1543290219000}, {"moduleCode": "", "detailCode": "20608", "description": "锁用户不存在", "solution": "", "updateTime": 1543281950000}, {"moduleCode": "", "detailCode": "20609", "description": "设备响应超时,门锁通信故障或者电量不足,请重试.", "solution": "", "updateTime": 1543281601000}, {"moduleCode": "", "detailCode": "1049954", "description": "升级设备失败", "solution": "", "updateTime": 1543279264000}, {"moduleCode": "", "detailCode": "60009", "description": "正在调用预置点", "solution": "", "updateTime": 1543238114000}, {"moduleCode": "", "detailCode": "1052677", "description": "获取本地录像失败", "solution": "", "updateTime": 1543207604000}, {"moduleCode": "", "detailCode": "327000", "description": "", "solution": "", "updateTime": 1543196609000}, {"moduleCode": "", "detailCode": "20021", "description": "设备在线，未被用户添加", "solution": "", "updateTime": 1543193436000}, {"moduleCode": "", "detailCode": "20202", "description": "操作留言信息失败", "solution": "", "updateTime": 1543191562000}, {"moduleCode": "", "detailCode": "1052678", "description": "获取本地录像失败", "solution": "", "updateTime": 1543132218000}, {"moduleCode": "", "detailCode": "1054723", "description": "格式化设备失败", "solution": "", "updateTime": 1543129833000}, {"moduleCode": "", "detailCode": "20109", "description": "对应分享不存在", "solution": "", "updateTime": 1543129111000}, {"moduleCode": "", "detailCode": "60026", "description": "设备处于隐私遮蔽状态", "solution": "", "updateTime": 1543110403000}, {"moduleCode": "", "detailCode": "60083", "description": "当前正在开启隐私遮蔽", "solution": "", "updateTime": 1543071148000}, {"moduleCode": "", "detailCode": "60001", "description": "用户无云台控制权限", "solution": "", "updateTime": 1543059167000}, {"moduleCode": "", "detailCode": "2003", "description": "设备不在线", "solution": "", "updateTime": 1543051046000}, {"moduleCode": "", "detailCode": "-24", "description": "设置设备enable错误", "solution": "", "updateTime": 1543042701000}, {"moduleCode": "", "detailCode": "10018", "description": "", "solution": "", "updateTime": 1543041564000}, {"moduleCode": "", "detailCode": "20103", "description": "好友已存在", "solution": "", "updateTime": 1543038430000}, {"moduleCode": "", "detailCode": "70010", "description": "授权异常请重试", "solution": "", "updateTime": 1543035590000}, {"moduleCode": "", "detailCode": "60056", "description": "删除设备失败", "solution": "", "updateTime": 1543031275000}, {"moduleCode": "", "detailCode": "60040", "description": "添加的设备不在同一局域网", "solution": "", "updateTime": 1543031210000}, {"moduleCode": "", "detailCode": "60019", "description": "加密已开启", "solution": "", "updateTime": 1543029931000}, {"moduleCode": "", "detailCode": "1054722", "description": "格式化设备失败", "solution": "", "updateTime": 1543028537000}, {"moduleCode": "", "detailCode": "20016", "description": "当前设备正在格式化", "solution": "", "updateTime": 1543028537000}, {"moduleCode": "", "detailCode": "10024", "description": "透明通道权限校验不通过", "solution": "", "updateTime": 1543025540000}, {"moduleCode": "", "detailCode": "6002", "description": "删除设备失败!", "solution": "", "updateTime": 1543025026000}, {"moduleCode": "", "detailCode": "1011", "description": "验证码错误！", "solution": "", "updateTime": 1543016865000}, {"moduleCode": "", "detailCode": "60032", "description": "卡密已使用", "solution": "", "updateTime": 1543006668000}, {"moduleCode": "", "detailCode": "10034", "description": "子账号已存在", "solution": "", "updateTime": 1542989194000}, {"moduleCode": "", "detailCode": "20301", "description": "根据uuid查询联动信息不存在", "solution": "", "updateTime": 1542988651000}, {"moduleCode": "", "detailCode": "1041", "description": "获取验证码过于频繁", "solution": "", "updateTime": 1542980953000}, {"moduleCode": "", "detailCode": "10012", "description": "该appkey下已绑定重复的phone！", "solution": "", "updateTime": 1542980800000}, {"moduleCode": "", "detailCode": "1008", "description": "phone不合法！", "solution": "", "updateTime": 1542979812000}, {"moduleCode": "", "detailCode": "60023", "description": "订阅操作失败", "solution": "", "updateTime": 1542979006000}, {"moduleCode": "", "detailCode": "5", "description": "设备返回其他错误", "solution": "", "updateTime": 1542977828000}, {"moduleCode": "", "detailCode": "60006", "description": "云台当前操作失败", "solution": "", "updateTime": 1542977598000}, {"moduleCode": "", "detailCode": "131", "description": "修改视频清晰度失败!", "solution": "", "updateTime": 1542977246000}, {"moduleCode": "", "detailCode": "10019", "description": "密码错误", "solution": "", "updateTime": 1542976628000}, {"moduleCode": "", "detailCode": "10004", "description": "用户不存在", "solution": "", "updateTime": 1542976268000}, {"moduleCode": "", "detailCode": "20201", "description": "操作报警信息失败", "solution": "", "updateTime": 1542975906000}, {"moduleCode": "", "detailCode": "20024", "description": "设备不在线，已经被别的用户添加", "solution": "", "updateTime": 1542975858000}, {"moduleCode": "", "detailCode": "60004", "description": "设备云台旋转达到左限位", "solution": "", "updateTime": 1542975207000}, {"moduleCode": "", "detailCode": "1052679", "description": "修改视频清晰度失败!", "solution": "", "updateTime": 1542974886000}, {"moduleCode": "", "detailCode": "20031", "description": "请在萤石客户端关闭终端绑定", "solution": "", "updateTime": 1542974756000}, {"moduleCode": "", "detailCode": "1053825", "description": "获取本地录像失败", "solution": "", "updateTime": 1542974692000}, {"moduleCode": "", "detailCode": "60011", "description": "预置点不存在", "solution": "", "updateTime": 1542974414000}, {"moduleCode": "", "detailCode": "1052936", "description": "修改视频清晰度失败!", "solution": "", "updateTime": 1542974390000}, {"moduleCode": "", "detailCode": "1016", "description": "", "solution": "", "updateTime": 1542974273000}, {"moduleCode": "", "detailCode": "10032", "description": "子账号不存在", "solution": "", "updateTime": 1542973906000}, {"moduleCode": "", "detailCode": "20013", "description": "设备已被别人添加", "solution": "", "updateTime": 1542973817000}, {"moduleCode": "", "detailCode": "50000", "description": "服务器错误!", "solution": "", "updateTime": 1542973801000}, {"moduleCode": "", "detailCode": "60010", "description": "该预置点已经是当前位置", "solution": "", "updateTime": 1542973800000}, {"moduleCode": "", "detailCode": "60003", "description": "设备云台旋转达到下限位", "solution": "", "updateTime": 1542973770000}, {"moduleCode": "", "detailCode": "4", "description": "设备返回其他错误", "solution": "", "updateTime": 1542973755000}, {"moduleCode": "", "detailCode": "60016", "description": "加密未开启，无需关闭", "solution": "", "updateTime": 1542973753000}, {"moduleCode": "", "detailCode": "60002", "description": "设备云台旋转达到上限位", "solution": "", "updateTime": 1542973742000}, {"moduleCode": "", "detailCode": "20023", "description": "设备不在线，未被用户添加", "solution": "", "updateTime": 1542973685000}, {"moduleCode": "", "detailCode": "10008", "description": "", "solution": "", "updateTime": 1542973676000}, {"moduleCode": "", "detailCode": "20010", "description": "设备验证码错误", "solution": "", "updateTime": 1542973658000}, {"moduleCode": "", "detailCode": "60005", "description": "设备云台旋转达到右限位", "solution": "", "updateTime": 1542973657000}, {"moduleCode": "", "detailCode": "20017", "description": "设备已经被自己添加", "solution": "", "updateTime": 1542973648000}, {"moduleCode": "", "detailCode": "20020", "description": "设备在线，已经被自己添加", "solution": "", "updateTime": 1542973533000}, {"moduleCode": "", "detailCode": "20029", "description": "设备不在线，已经被自己添加", "solution": "", "updateTime": 1542973530000}, {"moduleCode": "", "detailCode": "10014", "description": "APPKEY下对应的第三方userId和phone未绑定！", "solution": "", "updateTime": 1542973499000}, {"moduleCode": "", "detailCode": "20002", "description": "设备不存在", "solution": "", "updateTime": 1542973499000}, {"moduleCode": "", "detailCode": "10030", "description": "appkey和appsecret不匹配", "solution": "", "updateTime": 1542973490000}, {"moduleCode": "", "detailCode": "20022", "description": "设备在线，已经被别的用户添加", "solution": "", "updateTime": 1542973486000}, {"moduleCode": "", "detailCode": "20008", "description": "设备响应超时", "solution": "", "updateTime": 1542973484000}, {"moduleCode": "", "detailCode": "20032", "description": "该用户下通道不存在", "solution": "", "updateTime": 1542973481000}, {"moduleCode": "", "detailCode": "20006", "description": "网络异常", "solution": "", "updateTime": 1542973475000}, {"moduleCode": "", "detailCode": "20014", "description": "deviceSerial不合法!", "solution": "", "updateTime": 1542973454000}, {"moduleCode": "", "detailCode": "20007", "description": "设备不在线", "solution": "", "updateTime": 1542973454000}, {"moduleCode": "", "detailCode": "20018", "description": "该用户不拥有该设备", "solution": "", "updateTime": 1542973453000}, {"moduleCode": "", "detailCode": "10010", "description": "", "solution": "", "updateTime": 1542973453000}, {"moduleCode": "", "detailCode": "10011", "description": "未绑定！", "solution": "", "updateTime": 1542973453000}, {"moduleCode": "", "detailCode": "20001", "description": "通道不存在!", "solution": "", "updateTime": 1542973452000}, {"moduleCode": "", "detailCode": "10017", "description": "appKey不存在", "solution": "", "updateTime": 1542973451000}, {"moduleCode": "", "detailCode": "400259", "description": "", "solution": "", "updateTime": 1542875643000}, {"moduleCode": "", "detailCode": "400004", "description": "", "solution": "", "updateTime": 1542873364000}, {"moduleCode": "", "detailCode": "3840", "description": "", "solution": "", "updateTime": 1541860000000}, {"moduleCode": "", "detailCode": "-1017", "description": "", "solution": "", "updateTime": 1541733663000}, {"moduleCode": "", "detailCode": "320025", "description": "", "solution": "", "updateTime": 1541078281000}, {"moduleCode": "", "detailCode": "320024", "description": "", "solution": "", "updateTime": 1540801374000}, {"moduleCode": "", "detailCode": "321002", "description": "", "solution": "", "updateTime": 1540631734000}, {"moduleCode": "", "detailCode": "321000", "description": "", "solution": "", "updateTime": 1540609178000}, {"moduleCode": "", "detailCode": "321022", "description": "", "solution": "", "updateTime": 1540548345000}, {"moduleCode": "", "detailCode": "321016", "description": "", "solution": "", "updateTime": 1540287187000}, {"moduleCode": "", "detailCode": "320023", "description": "", "solution": "", "updateTime": 1539825993000}, {"moduleCode": "", "detailCode": "-1016", "description": "", "solution": "", "updateTime": 1539584931000}, {"moduleCode": "", "detailCode": "8", "description": "", "solution": "", "updateTime": 1539391812000}, {"moduleCode": "", "detailCode": "1075127593", "description": "", "solution": "", "updateTime": 1538959251000}, {"moduleCode": "", "detailCode": "380421", "description": "", "solution": "", "updateTime": 1537288465000}, {"moduleCode": "", "detailCode": "322000", "description": "麦克风权限未开启", "solution": "", "updateTime": 1536820136000}, {"moduleCode": "", "detailCode": "1152678", "description": "", "solution": "", "updateTime": 1536738348000}, {"moduleCode": "", "detailCode": "320047", "description": "", "solution": "", "updateTime": 1536664472000}, {"moduleCode": "", "detailCode": "327006", "description": "", "solution": "", "updateTime": 1536136120000}, {"moduleCode": "", "detailCode": "1074807593", "description": "", "solution": "", "updateTime": 1536135035000}, {"moduleCode": "", "detailCode": "320291", "description": "", "solution": "", "updateTime": 1536110836000}, {"moduleCode": "", "detailCode": "320045", "description": "", "solution": "", "updateTime": 1535963775000}, {"moduleCode": "", "detailCode": "370004", "description": "", "solution": "", "updateTime": 1535883699000}, {"moduleCode": "", "detailCode": "1149954", "description": "", "solution": "", "updateTime": 1535700674000}, {"moduleCode": "", "detailCode": "320053", "description": "", "solution": "", "updateTime": 1535681079000}, {"moduleCode": "", "detailCode": "400000", "description": "", "solution": "", "updateTime": 1535532332000}, {"moduleCode": "", "detailCode": "110028", "description": "个人版抓图接口日调用次数超出限制", "solution": "请升级企业版：https://open.ys7.com/price.html", "updateTime": 1535348756000}, {"moduleCode": "", "detailCode": "110027", "description": "个人版帐号数量超出安全限制，无法调用", "solution": "请升级企业版：https://open.ys7.com/price.html", "updateTime": 1535348734000}, {"moduleCode": "", "detailCode": "110026", "description": "设备数量超出个人版限制，当前设备无法操作", "solution": "请升级企业版：https://open.ys7.com/price.html", "updateTime": 1535348588000}, {"moduleCode": "", "detailCode": "100000", "description": "", "solution": "", "updateTime": 1534980008000}, {"moduleCode": "", "detailCode": "324004", "description": "", "solution": "", "updateTime": 1534927762000}, {"moduleCode": "", "detailCode": "360104", "description": "", "solution": "", "updateTime": 1534761006000}, {"moduleCode": "", "detailCode": "320204", "description": "", "solution": "", "updateTime": 1534584221000}, {"moduleCode": "", "detailCode": "380001", "description": "", "solution": "", "updateTime": 1534404715000}, {"moduleCode": "", "detailCode": "380204", "description": "", "solution": "", "updateTime": 1534401682000}, {"moduleCode": "", "detailCode": "328006", "description": "", "solution": "", "updateTime": 1534144407000}, {"moduleCode": "", "detailCode": "321703", "description": "", "solution": "", "updateTime": 1534127274000}, {"moduleCode": "", "detailCode": "321510", "description": "", "solution": "", "updateTime": 1533428892000}, {"moduleCode": "", "detailCode": "321006", "description": "", "solution": "", "updateTime": 1533036916000}, {"moduleCode": "", "detailCode": "50009", "description": "", "solution": "", "updateTime": 1532078548000}, {"moduleCode": "", "detailCode": "50007", "description": "", "solution": "", "updateTime": 1531991720000}, {"moduleCode": "", "detailCode": "50018", "description": "", "solution": "", "updateTime": 1531912829000}, {"moduleCode": "", "detailCode": "380451", "description": "", "solution": "", "updateTime": 1531615700000}, {"moduleCode": "", "detailCode": "380336", "description": "", "solution": "", "updateTime": 1531231721000}, {"moduleCode": "", "detailCode": "360020", "description": "", "solution": "", "updateTime": 1531117554000}, {"moduleCode": "", "detailCode": "380418", "description": "", "solution": "", "updateTime": 1531107070000}, {"moduleCode": "", "detailCode": "1153445", "description": "设备在该时间段内没有录像", "solution": "", "updateTime": 1530944007000}, {"moduleCode": "", "detailCode": "110007", "description": "调用接口总次数达到上限", "solution": "请升级企业版，获取更高能力", "updateTime": 1530935584000}, {"moduleCode": "", "detailCode": "360019", "description": "", "solution": "", "updateTime": 1530869771000}, {"moduleCode": "", "detailCode": "360100", "description": "", "solution": "", "updateTime": 1530786188000}, {"moduleCode": "", "detailCode": "380042", "description": "", "solution": "", "updateTime": 1530775199000}, {"moduleCode": "", "detailCode": "320355", "description": "", "solution": "", "updateTime": 1530716074000}, {"moduleCode": "", "detailCode": "100003", "description": "", "solution": "", "updateTime": 1530232541000}, {"moduleCode": "", "detailCode": "371026", "description": "", "solution": "", "updateTime": 1530192600000}, {"moduleCode": "", "detailCode": "102", "description": "", "solution": "", "updateTime": 1529895641000}, {"moduleCode": "", "detailCode": "380201", "description": "", "solution": "", "updateTime": 1529740929000}, {"moduleCode": "", "detailCode": "320054", "description": "", "solution": "", "updateTime": 1529544875000}, {"moduleCode": "", "detailCode": "500101", "description": "", "solution": "", "updateTime": 1529485953000}, {"moduleCode": "", "detailCode": "321001", "description": "", "solution": "", "updateTime": 1529411048000}, {"moduleCode": "", "detailCode": "321508", "description": "", "solution": "", "updateTime": 1529393279000}, {"moduleCode": "", "detailCode": "405991", "description": "", "solution": "", "updateTime": 1529380238000}, {"moduleCode": "", "detailCode": "380461", "description": "", "solution": "", "updateTime": 1529130941000}, {"moduleCode": "", "detailCode": "-1019", "description": "", "solution": "", "updateTime": 1529057245000}, {"moduleCode": "", "detailCode": "322009", "description": "", "solution": "", "updateTime": 1528965717000}, {"moduleCode": "", "detailCode": "324005", "description": "", "solution": "", "updateTime": 1528950153000}, {"moduleCode": "", "detailCode": "325000", "description": "", "solution": "", "updateTime": 1528947143000}, {"moduleCode": "", "detailCode": "326032", "description": "", "solution": "", "updateTime": 1528872971000}, {"moduleCode": "", "detailCode": "325032", "description": "", "solution": "", "updateTime": 1528863189000}, {"moduleCode": "", "detailCode": "328000", "description": "", "solution": "", "updateTime": 1528794505000}, {"moduleCode": "", "detailCode": "53", "description": "", "solution": "", "updateTime": 1528693249000}, {"moduleCode": "", "detailCode": "-1020", "description": "", "solution": "", "updateTime": 1528499440000}, {"moduleCode": "", "detailCode": "329032", "description": "", "solution": "", "updateTime": 1528446301000}, {"moduleCode": "", "detailCode": "-1202", "description": "", "solution": "", "updateTime": 1528439820000}, {"moduleCode": "", "detailCode": "2", "description": "", "solution": "", "updateTime": 1528434175000}, {"moduleCode": "", "detailCode": "1152936", "description": "", "solution": "", "updateTime": 1528345986000}, {"moduleCode": "", "detailCode": "328011", "description": "", "solution": "", "updateTime": 1528338600000}, {"moduleCode": "", "detailCode": "28", "description": "", "solution": "", "updateTime": 1528337530000}, {"moduleCode": "", "detailCode": "320356", "description": "", "solution": "", "updateTime": 1528188693000}, {"moduleCode": "", "detailCode": "320357", "description": "", "solution": "", "updateTime": 1528188517000}, {"moduleCode": "", "detailCode": "405800", "description": "", "solution": "", "updateTime": 1528168732000}, {"moduleCode": "", "detailCode": "405996", "description": "", "solution": "", "updateTime": 1528168686000}, {"moduleCode": "", "detailCode": "380357", "description": "", "solution": "", "updateTime": 1528011565000}, {"moduleCode": "", "detailCode": "328022", "description": "", "solution": "", "updateTime": 1527929065000}, {"moduleCode": "", "detailCode": "380003", "description": "", "solution": "", "updateTime": 1527927819000}, {"moduleCode": "", "detailCode": "50004", "description": "", "solution": "", "updateTime": 1527770643000}, {"moduleCode": "", "detailCode": "50011", "description": "", "solution": "", "updateTime": 1527770635000}, {"moduleCode": "", "detailCode": "370017", "description": "", "solution": "", "updateTime": 1527739514000}, {"moduleCode": "", "detailCode": "327032", "description": "", "solution": "", "updateTime": 1527726704000}, {"moduleCode": "", "detailCode": "324001", "description": "", "solution": "", "updateTime": 1527681892000}, {"moduleCode": "", "detailCode": "405997", "description": "", "solution": "", "updateTime": 1527653408000}, {"moduleCode": "", "detailCode": "405995", "description": "", "solution": "", "updateTime": 1527647283000}, {"moduleCode": "", "detailCode": "1153825", "description": "", "solution": "", "updateTime": 1527601747000}, {"moduleCode": "", "detailCode": "328002", "description": "", "solution": "", "updateTime": 1527495292000}, {"moduleCode": "", "detailCode": "1152679", "description": "", "solution": "", "updateTime": 1527486665000}, {"moduleCode": "", "detailCode": "380356", "description": "", "solution": "", "updateTime": 1527125669000}, {"moduleCode": "", "detailCode": "328032", "description": "", "solution": "", "updateTime": 1527069382000}, {"moduleCode": "", "detailCode": "22", "description": "", "solution": "", "updateTime": 1527049826000}, {"moduleCode": "", "detailCode": "9", "description": "", "solution": "", "updateTime": 1527006778000}, {"moduleCode": "", "detailCode": "89", "description": "", "solution": "", "updateTime": 1526622784000}, {"moduleCode": "", "detailCode": "328016", "description": "", "solution": "", "updateTime": 1526452365000}, {"moduleCode": "", "detailCode": "368005", "description": "", "solution": "", "updateTime": 1525921264000}, {"moduleCode": "", "detailCode": "0", "description": "", "solution": "", "updateTime": 1525920242000}, {"moduleCode": "", "detailCode": "380006", "description": "", "solution": "", "updateTime": 1525918868000}, {"moduleCode": "", "detailCode": "310", "description": "", "solution": "", "updateTime": 1525834436000}, {"moduleCode": "", "detailCode": "360011", "description": "", "solution": "", "updateTime": 1525681552000}, {"moduleCode": "", "detailCode": "170005", "description": "", "solution": "", "updateTime": 1525433900000}, {"moduleCode": "", "detailCode": "50023", "description": "", "solution": "", "updateTime": 1525403338000}, {"moduleCode": "", "detailCode": "100131", "description": "", "solution": "", "updateTime": 1525229691000}, {"moduleCode": "", "detailCode": "-1018", "description": "", "solution": "", "updateTime": 1525142341000}, {"moduleCode": "", "detailCode": "362026", "description": "", "solution": "", "updateTime": 1524882677000}, {"moduleCode": "", "detailCode": "368007", "description": "", "solution": "", "updateTime": 1524832269000}, {"moduleCode": "", "detailCode": "54", "description": "", "solution": "", "updateTime": 1524793646000}, {"moduleCode": "", "detailCode": "1154722", "description": "", "solution": "", "updateTime": 1524620807000}, {"moduleCode": "", "detailCode": "320229", "description": "", "solution": "", "updateTime": 1524551682000}, {"moduleCode": "", "detailCode": "360012", "description": "", "solution": "", "updateTime": 1524472094000}, {"moduleCode": "", "detailCode": "380229", "description": "", "solution": "", "updateTime": 1524110755000}, {"moduleCode": "", "detailCode": "360016", "description": "", "solution": "", "updateTime": 1523933518000}, {"moduleCode": "", "detailCode": "-1003", "description": "", "solution": "", "updateTime": 1523584804000}, {"moduleCode": "", "detailCode": "410026", "description": "", "solution": "", "updateTime": 1523517430000}, {"moduleCode": "", "detailCode": "360102", "description": "TTS初始化失败", "solution": "", "updateTime": 1523503528000}, {"moduleCode": "", "detailCode": "360013", "description": "设备开启了隐私保护", "solution": "", "updateTime": 1523503507000}, {"moduleCode": "", "detailCode": "360010", "description": "设备正在对讲中", "solution": "", "updateTime": 1523503491000}, {"moduleCode": "", "detailCode": "360007", "description": "TTS关闭了与客户端的连接", "solution": "", "updateTime": 1523503475000}, {"moduleCode": "", "detailCode": "360006", "description": "客户端接收发生错误", "solution": "", "updateTime": 1523503457000}, {"moduleCode": "", "detailCode": "360005", "description": "客户端发送的消息错误", "solution": "", "updateTime": 1523503437000}, {"moduleCode": "", "detailCode": "360004", "description": "TTS内部发生错误", "solution": "", "updateTime": 1523503421000}, {"moduleCode": "", "detailCode": "360003", "description": "TTS的设备端发生错误", "solution": "", "updateTime": 1523503397000}, {"moduleCode": "", "detailCode": "360002", "description": "对讲发起超时", "solution": "", "updateTime": 1523503376000}, {"moduleCode": "", "detailCode": "360001", "description": "客户端请求超时", "solution": "", "updateTime": 1523503357000}, {"moduleCode": "", "detailCode": "320077", "description": "", "solution": "", "updateTime": 1523444274000}, {"moduleCode": "", "detailCode": "370047", "description": "", "solution": "", "updateTime": 1523440480000}, {"moduleCode": "", "detailCode": "100002", "description": "", "solution": "", "updateTime": 1523413964000}, {"moduleCode": "", "detailCode": "-1004", "description": "", "solution": "", "updateTime": 1523336653000}, {"moduleCode": "", "detailCode": "380000", "description": "", "solution": "", "updateTime": 1523180856000}, {"moduleCode": "", "detailCode": "380213", "description": "", "solution": "", "updateTime": 1523180623000}, {"moduleCode": "", "detailCode": "380101", "description": "", "solution": "", "updateTime": 1522834231000}, {"moduleCode": "", "detailCode": "50047", "description": "", "solution": "", "updateTime": 1522833243000}, {"moduleCode": "", "detailCode": "-999", "description": "", "solution": "", "updateTime": 1522831034000}, {"moduleCode": "", "detailCode": "320418", "description": "", "solution": "", "updateTime": 1522829072000}, {"moduleCode": "", "detailCode": "-1009", "description": "", "solution": "", "updateTime": 1522746247000}, {"moduleCode": "", "detailCode": "320209", "description": "", "solution": "", "updateTime": 1522744395000}, {"moduleCode": "", "detailCode": "368006", "description": "", "solution": "", "updateTime": 1522744300000}, {"moduleCode": "", "detailCode": "369003", "description": "", "solution": "", "updateTime": 1522736355000}, {"moduleCode": "", "detailCode": "405989", "description": "", "solution": "", "updateTime": 1522726571000}, {"moduleCode": "", "detailCode": "-1012", "description": "", "solution": "", "updateTime": 1522726203000}, {"moduleCode": "", "detailCode": "322016", "description": "", "solution": "", "updateTime": 1522722918000}, {"moduleCode": "", "detailCode": "500103", "description": "", "solution": "", "updateTime": 1522655556000}, {"moduleCode": "", "detailCode": "405999", "description": "", "solution": "", "updateTime": 1522654716000}, {"moduleCode": "", "detailCode": "321032", "description": "", "solution": "", "updateTime": 1522647732000}, {"moduleCode": "", "detailCode": "381101", "description": "", "solution": "", "updateTime": 1522392414000}, {"moduleCode": "", "detailCode": "399999", "description": "", "solution": "", "updateTime": 1522379834000}, {"moduleCode": "", "detailCode": "380103", "description": "", "solution": "", "updateTime": 1522312724000}, {"moduleCode": "", "detailCode": "360014", "description": "", "solution": "", "updateTime": 1522304341000}, {"moduleCode": "", "detailCode": "-1005", "description": "", "solution": "", "updateTime": 1522288195000}, {"moduleCode": "", "detailCode": "395454", "description": "", "solution": "", "updateTime": 1522220180000}, {"moduleCode": "", "detailCode": "100005", "description": "", "solution": "", "updateTime": 1522218849000}, {"moduleCode": "", "detailCode": "100004", "description": "", "solution": "", "updateTime": 1522209053000}, {"moduleCode": "", "detailCode": "106002", "description": "", "solution": "", "updateTime": 1522206200000}, {"moduleCode": "", "detailCode": "410030", "description": "", "solution": "", "updateTime": 1522162252000}, {"moduleCode": "", "detailCode": "-1002", "description": "", "solution": "", "updateTime": 1522150690000}, {"moduleCode": "", "detailCode": "-1200", "description": "", "solution": "", "updateTime": 1522139025000}, {"moduleCode": "", "detailCode": "-1001", "description": "", "solution": "", "updateTime": 1522046436000}, {"moduleCode": "", "detailCode": "-1011", "description": "", "solution": "", "updateTime": 1522045931000}, {"moduleCode": "", "detailCode": "381102", "description": "", "solution": "", "updateTime": 1522044953000}, {"moduleCode": "", "detailCode": "381103", "description": "", "solution": "", "updateTime": 1522044953000}, {"moduleCode": "", "detailCode": "391205", "description": "vtdu解析服务器ip失败", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "391206", "description": "vtdu描述符select失败", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "391207", "description": "vtdu文件描述符不在可读中", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "391208", "description": "vtdu网络发生错误getsockopt", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "391209", "description": "vtdu描述符select超时", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395000", "description": "cas回复信令，发现内存已经释放（和设备之间异常断开）", "solution": "检查设备网络；刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395400", "description": "私有化协议vtm检测私有化协议中码流类型小于0或者设备序列号为空等非法参数场景返回(app不重试取流)", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395402", "description": "回放找不到录像文件", "solution": "检查是否有存储卡并且接触良好", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395403", "description": "操作码或信令密钥与设备不匹配", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395404", "description": "设备不在线", "solution": "检查设备网络；重启设备接入萤石云", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395405", "description": "流媒体向设备发送或接受信令超时/cas响应超时", "solution": "检查设备网络；重启设备", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395406", "description": "token失效", "solution": "刷新重试或者重启设备", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395407", "description": "客户端的URL格式错误", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395409", "description": "预览开启隐私保护", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395410", "description": "设备达到最大连接数", "solution": "请升级设备固件版本", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395411", "description": "token无权限", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395412", "description": "session不存在 ", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395413", "description": "验证token的他异常（不具体） ", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395415", "description": "设备通道错", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395451", "description": "设备不支持的码流类型", "solution": "刷新重试或者切换到高清模式", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395452", "description": "设备链接流媒体服务器失败 ", "solution": "检查设备网络，重启设备，刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395500", "description": "服务器处理失败 ", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395501", "description": "流媒体vtdu达到最大负载，请扩容", "solution": "服务器负载达到上限，请稍后重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395503", "description": "vtm返回分配vtdu失败", "solution": "服务器负载达到上限，请稍后重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395544", "description": "设备返回无视频源 ", "solution": "设备是否接触良好；", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395545", "description": "视频分享时间已经结束", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395546", "description": "vtdu返回达到取流并发路数限制", "solution": "请升级为企业版，放开并发限制", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395560", "description": "蚁兵代理不支持的用户取流类型，会重定向到vtdu取流", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395557", "description": "回放服务器等待流头超时", "solution": "刷新重试，检测设备网络，重启设备", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395600", "description": "分享设备不在分享时间内", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395601", "description": "群组分享用户没权限", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395602", "description": "群组分享权限变更", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395556", "description": "ticket取流验证失败", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395530", "description": "机房故障不可用", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "395701", "description": "cas信令返回格式错误", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396001", "description": "客户端参数出错", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396099", "description": "客户端默认错误", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396101", "description": "不支持的命令", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396102", "description": "设备流头发送失败", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396103", "description": "cas/设备返回错误1", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396104", "description": "cas/设备返回错误-1", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396105", "description": "设备返回错误码3", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396106", "description": "设备返回错误码4", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396107", "description": "设备返回错误码5", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396108", "description": "cas信令回应重复", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396109", "description": "视频广场取消分享", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396110", "description": "设备信令默认错误", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396501", "description": "设备数据链路和实际链路不匹配", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396502", "description": "设备数据链路重复建立连接", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396503", "description": "设备数据链路端口不匹配", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396504", "description": "缓存设备数据链路失败(内存块不足)", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396505", "description": "设备发送确认头消息重复", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396506", "description": "设备数据先于确定头部到达", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396508", "description": "设备数据头部长度非法", "solution": "刷新重试，或者重启设备", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396509", "description": "索引找不到设备数据管理块", "solution": "刷新重试", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396510", "description": "设备数据链路vtdu内存块协议状态不匹配", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396511", "description": "设备数据头部没有streamkey错误", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396512", "description": "设备数据头部非法(较笼统)", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396513", "description": "设备数据长度过小", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396514", "description": "设备老协议推流头部没有streamkey错误", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396515", "description": "设备老协议推流数据非法", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396516", "description": "设备老协议索引找不到内存管理块", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396517", "description": "设备老协议推流数据非法", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396518", "description": "设备数据包过大", "solution": "刷新重试，或者重启设备", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396519", "description": "设备推流链路网络不稳定", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "396520", "description": "设备推流链路网络不稳定(默认)", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "170003", "description": "refreshToken不存在", "solution": "建议用户重新调用logout接口，然后调用openLoginPage接口重新启动登录页面登录", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "170004", "description": "refreshToken已过期", "solution": "建议用户重新调用logout接口，然后调用openLoginPage接口重新启动登录页面登录", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380011", "description": "设备隐私保护中", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380045", "description": "设备直连取流连接数量过大", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380047", "description": "设备不支持该命令", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380077", "description": "设备正在对讲中", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380102", "description": "数据接收异常", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380205", "description": "设备检测入参异常", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380209", "description": "网络连接超时", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "380212", "description": "设备端网络连接超时", "solution": "", "updateTime": 1522034841000}, {"moduleCode": "", "detailCode": "101007", "description": "手机号未注册", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120503", "description": "正在响铃", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390016", "description": "vtdu成功响应未携带流头", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101008", "description": "手机号码不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120504", "description": "室内机正在通话", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390017", "description": "无数据流，尚未使用", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101009", "description": "用户名与手机不匹配", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120505", "description": "设备操作失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390018", "description": "信令消息体PB解析失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101010", "description": "获取验证码失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120506", "description": "非法命令", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390019", "description": "信令消息体PB封装失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101011", "description": "验证码错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120507", "description": "智能锁密码错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390020", "description": "申请系统内存资源失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101012", "description": "验证码失效", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120508", "description": "开关锁失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390021", "description": "vtdu地址尚未获取到", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101013", "description": "用户不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120509", "description": "开关锁超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390022", "description": "客户端尚未支持", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101014", "description": "密码不正确或者appKey不正确", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120510", "description": "智能锁设备繁忙", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390023", "description": "获取系统socket资源失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101015", "description": "用户被锁住", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120511", "description": "远程开锁功能未打开", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390024", "description": "上层填充的StreamSsnId不匹配", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101021", "description": "验证参数异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120600", "description": "临时密码数已达上限", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390025", "description": "链接服务器失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101026", "description": "邮箱已经被注册", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120601", "description": "添加临时密码失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390026", "description": "客户端请求未收到服务端应答", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101031", "description": "邮箱未注册", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120602", "description": "删除临时密码失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390027", "description": "链路断开", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101032", "description": "邮箱不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120603", "description": "该临时密码不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390028", "description": "没有取流链接", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101041", "description": "获取验证码过于频繁", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120604", "description": "指纹锁射频通信失败,请稍后再试", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390029", "description": "流成功停止", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101043", "description": "手机验证码输入错误超过规定次数", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120605", "description": "其他用户正在认证中", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390030", "description": "客户端防串流校验失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "102000", "description": "设备不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120606", "description": "验证已启动,请在120s内进行本地验证和调用添加设备接口", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390031", "description": "应用层tcp粘包处理缓冲区满", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "102001", "description": "摄像机不存在", "solution": "摄像机未注册到萤石云平台，请仔细检查摄像机的网络配置，确保连接到网络", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120607", "description": "删除用户失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390032", "description": "无效状态迁移", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "102003", "description": "设备不在线", "solution": "参考服务中心排查方法", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120608", "description": "用户不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390033", "description": "无效客户端状态", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "102004", "description": "设备异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120609", "description": "设备响应超时,门锁通信故障或者电量不足,请重试.", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390034", "description": "向vtm取流流媒体信息请求超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "102007", "description": "设备序列号不正确", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120610", "description": "获取临时密码列表失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390035", "description": "向代理取流请求超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "102009", "description": "设备请求响应超时异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "130001", "description": "用户不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390036", "description": "向代理保活取流请求超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "105000", "description": "设备已被自己添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "130002", "description": "手机号码已经注册", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390037", "description": "向vtdu取流请求超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "105001", "description": "设备已被别人添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "130003", "description": "手机验证码错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390038", "description": "向vtdu保活取流请求超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "105002", "description": "设备验证码错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "130004", "description": "终端绑定操作失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391001", "description": "vtm地址或端口非法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "107001", "description": "邀请不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "149999", "description": "数据异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391002", "description": "vtm生成文件描述符失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "107002", "description": "邀请验证失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "150000", "description": "服务器异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391003", "description": "vtm设置文件描述符非阻塞失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "107003", "description": "邀请用户不匹配", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160000", "description": "设备不支持云台控制", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391004", "description": "vtm设置文件描述符阻塞失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "107004", "description": "无法取消邀请", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160001", "description": "用户无云台控制权限", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391005", "description": "vtm解析服务器ip失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "7005", "description": "VTDU主动断开连接", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "107005", "description": "VTDU主动断开连接", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160002", "description": "设备云台旋转达到上限位", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391006", "description": "vtm描述符select失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "107006", "description": "不能邀请自己", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160003", "description": "设备云台旋转达到下限位", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391007", "description": "vtm文件描述符不在可读中", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "107007", "description": "重复邀请", "solution": "分享和删除分享必须全部由接口形式操作，如果与萤石客户端混用会造成这个问题，解决办法：在萤石客户端清空所有相关分享数据并重新添加设备，再通过接口操作即可", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160004", "description": "设备云台旋转达到左限位", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391008", "description": "vtm网络发生错误getsockopt", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110001", "description": "参数错误", "solution": "参数为空或者格式不对", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160005", "description": "设备云台旋转达到右限位", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391009", "description": "vtm描述符select超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110002", "description": "accessToken异常或过期", "solution": "accessToken有效期为七天，建议在accessToken即将过期或者出现10002错误码的时候重新获取accessToken", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160006", "description": "云台当前操作失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391101", "description": "proxy地址或端口非法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110004", "description": "用户不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160007", "description": "预置点个数超过最大值", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391102", "description": "proxy生成文件描述符失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110005", "description": "appKey异常", "solution": "确认appKey状态，不通过或者冻结状态会返回该错误码", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160009", "description": "正在调用预置点", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391103", "description": "proxy设置文件描述符非阻塞失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110006", "description": "ip受限", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160010", "description": "该预置点已经是当前位置", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391104", "description": "proxy设置文件描述符阻塞失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160011", "description": "预置点不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391105", "description": "proxy解析服务器ip失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110008", "description": "签名错误", "solution": "①获取签名方式详见apidemo及[旧]API文档 ②注意编码格式为UTF-8", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160013", "description": "设备版本已是最新", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391106", "description": "proxy描述符select失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110009", "description": "签名参数错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160014", "description": "设备正在升级", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391107", "description": "proxy文件描述符不在可读中", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110010", "description": "签名超时", "solution": "请调用同步服务器时间接口进行校时", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160015", "description": "设备正在重启", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391108", "description": "proxy网络发生错误getsockopt", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110011", "description": "未开通萤石云服务", "solution": "参照绑定流程", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160016", "description": "加密未开启，无须关闭", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391109", "description": "proxy描述符select超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110012", "description": "第三方账户与萤石账号已经绑定", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160017", "description": "设备抓图失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391201", "description": "vtdu地址或端口非法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110013", "description": "应用没有权限调用此接口", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160018", "description": "设备升级失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391202", "description": "vtdu生成文件描述符失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110014", "description": "APPKEY下对应的第三方userId和phone未绑定", "solution": "获取AccessToken时所用appKey与SDK所用appKey不一致", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160019", "description": "加密已开启", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391203", "description": "vtdu设置文件描述符非阻塞失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110017", "description": "appKey不存在", "solution": "请填写在官网申请的应用秘钥", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160020", "description": "不支持该命令", "solution": "请确认设备是否支持该命令", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "391204", "description": "vtdu设置文件描述符阻塞失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110018", "description": "AccessToken与Appkey不匹配", "solution": "请检查获取accessToken对应的appKey和SDK中设置的appKey是否一致", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160023", "description": "订阅操作失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110019", "description": "密码错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160024", "description": "取消订阅操作失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110020", "description": "请求方法为空", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160025", "description": "客流统计配置失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110021", "description": "ticket校验失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160026", "description": "设备处于隐私遮蔽状态", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110022", "description": "透传目的地非法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160027", "description": "设备正在镜像操作", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110024", "description": "无透传权限", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160028", "description": "设备正在键控动作", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110025", "description": "appKey被禁止使用通明通道", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160029", "description": "设备处于语音对讲状态", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160030", "description": "卡密输入错误次数过多，24小时后再输入", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160031", "description": "卡密信息不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160032", "description": "卡密状态不对或已过期", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160033", "description": "卡密非卖品，只能开通对应的绑定设备", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110030", "description": "appKey和appSecret不匹配", "solution": "请检查appKey和appSecret是否对应", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160035", "description": "购买云存储服务失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110031", "description": "子账户或萤石用户没有权限", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160040", "description": "添加的设备不在同一局域网", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110032", "description": "子账户不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160041", "description": "添加的设备被其他设备关联或响应超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110033", "description": "子账户未设置授权策略", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160042", "description": "添加的设备密码错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110034", "description": "子账户已存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160043", "description": "添加的设备超出最大数量", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110035", "description": "获取子账户AccessToken异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160044", "description": "添加的设备网络不可达超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110036", "description": "子账户被禁用", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160045", "description": "添加的设备的IP和其他通道的IP冲突", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "110051", "description": "无权限进行抓图", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160046", "description": "添加的设备的IP和本设备的IP冲突", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160047", "description": "码流类型不支持", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120002", "description": "设备不存在", "solution": "①设备没有注册到萤石云平台，请检查下设备网络参数，确保能正常连接网络②设备序列号不存在", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160048", "description": "带宽超出系统接入带宽", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120003", "description": "参数异常，SDK版本过低", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160049", "description": "IP或者端口不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120004", "description": "参数异常，SDK版本过低", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160050", "description": "添加的设备版本不支持需要升级才能接入", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120005", "description": "安全认证失败，需进行SDK安全认证", "solution": "已去掉安全验证", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160051", "description": "添加的设备不支持接入", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120006", "description": "网络异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160052", "description": "添加的设备通道号出错", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120007", "description": "设备不在线", "solution": "参考服务中心排查方法", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160053", "description": "添加的设备分辨率不支持", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120008", "description": "设备响应超时", "solution": "设备响应超时，请检测设备网络或重试", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160054", "description": "添加的设备账号被锁定", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120009", "description": "子账号不能添加设备", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160055", "description": "添加的设备取码流出错", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120010", "description": "设备验证码错误", "solution": "验证码在设备标签上，六位大写字母，注意大小写", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160056", "description": "删除设备失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120012", "description": "设备添加失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160057", "description": "删除的设备未关联", "solution": "检查IPC与NVR是否有关联关系", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120013", "description": "设备已被别人添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160060", "description": "地址未绑定", "solution": "请前往官网设置直播", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120014", "description": "设备序列号不正确", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160061", "description": "账户流量已超出或未购买，限制开通", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120015", "description": "设备不支持该功能", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160062", "description": "该通道直播已开通", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120016", "description": "当前设备正在格式化", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160063", "description": "直播未使用或直播已关闭", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120017", "description": "设备已被自己添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160070", "description": "设备不能转移给自己", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120018", "description": "该用户不拥有该设备", "solution": "确认设备是否属于用户", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160071", "description": "设备不能转移，设备与其他设备存在关联关系", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400001", "description": "参数为空", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120019", "description": "设备不支持云存储服务", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160072", "description": "设备不能转移，通道被分享给其他用户或者分享到视频广场", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400002", "description": "参数错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120020", "description": "设备在线，被自己添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160073", "description": "云存储转移失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400025", "description": "设备不支持对讲", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120021", "description": "设备在线，但是未被用户添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160080", "description": "当前正在声源定位", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400029", "description": "没有初始化或资源被释放", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120022", "description": "设备在线，但是已经被别的用户添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160081", "description": "当前正在轨迹巡航", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400030", "description": "json解析异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120023", "description": "设备不在线，未被用户添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160082", "description": "设备正在响应本次声源定位", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400031", "description": "网络异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120024", "description": "设备不在线，但是已经被别的用户添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160083", "description": "当前正在开启隐私遮蔽", "solution": "设备正在操作隐私遮蔽，无法进行当前操作", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400032", "description": "设备信息异常为空", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120025", "description": "重复申请分享", "solution": "确认设备是否由添加过该设备且申请过分享的账户下是否还存在分享记录", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "160084", "description": "当前正在关闭隐私遮蔽", "solution": "设备正在操作隐私遮蔽，无法进行当前操作", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400034", "description": "取流超时", "solution": "一般是由于网络状况不好导致，可以尝试下让用户重新播放", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120026", "description": "视频广场不存在该视频", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "380290", "description": "　连接CAS服务器失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400035", "description": "设备已加密，需要输入验证码", "solution": "收到此错误码，需要让用户输入验证码后，调用EZPlayer.setPlayKey传入验证码，并重新调用播放函数", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120027", "description": "视频转码失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361001", "description": "对讲服务端排队超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400036", "description": "播放验证码错误", "solution": "收到此错误码，需要让用户输入验证码后，调用EZPlayer.setPlayKey传入验证码，并重新调用播放函数", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120028", "description": "设备固件升级包不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361002", "description": "对讲服务端处理超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400037", "description": "surfacehold错误", "solution": "请检查是否是播放之前销毁了surface，收到此错误也可以重新建立surface后播放", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120029", "description": "设备不在线，但是已经被自己添加", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361003", "description": "设备链接对讲服务器超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400100", "description": "未知错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120030", "description": "该用户不拥有该视频广场视频", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361004", "description": "服务器内部错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400200", "description": "player sdk出错，这种错误一般开发者也是无法解决，不具体分类传出，传一个统一的inner错误码出去", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120031", "description": "开启终端绑定，硬件特征码验证失败", "solution": "请在萤石客户端关闭终端绑定，参考此步骤", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361005", "description": "解析消息失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400300", "description": "内存溢出", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120032", "description": "该用户下通道不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361006", "description": "请求重定向--需要向其他服务申请对讲", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400901", "description": "设备不在线，可以提示用户", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120033", "description": "无法收藏自己分享的视频", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361007", "description": "请求url非法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400902", "description": "accesstoken异常或失效，需要重新获取accesstoken，并传入到sdk", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120034", "description": "该用户下无设备", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361008", "description": "token失效", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400903", "description": "当前账号开启了终端绑定，只允许指定设备登录操作，提示用户登录i.ys7.com解除终端绑定", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120090", "description": "用户反馈失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361009", "description": "设备验证码或者通信秘钥不匹配", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400904", "description": "设备正在对讲中", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120095", "description": "APP包下载失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361010", "description": "设备已经在对讲", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "400905", "description": "设备开启了隐私保护，不允许预览、对讲等", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120096", "description": "APP包信息删除失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361011", "description": "设备10s响应超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120101", "description": "视频不支持分享给本人", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361012", "description": "设备不在线", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320002", "description": "参数无效", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120102", "description": "无相应邀请信息", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361013", "description": "设备开启隐私保护拒绝对讲", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320003", "description": "暂不支持此操作", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120103", "description": "好友已存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361014", "description": "token无权限", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320004", "description": "内存溢出", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120104", "description": "好友不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361015", "description": "设备返回session不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320005", "description": "创建CAS session失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120105", "description": "好友状态错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361016", "description": "验证token其他异常错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320006", "description": "创建cloud session失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120106", "description": "对应群组不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361017", "description": "服务端监听设备建立端口超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320007", "description": "token失效", "solution": "重新设置token后再重试", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120107", "description": "不能添加自己为好友", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361018", "description": "设备链路异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320008", "description": "token池里面没有token,请传入token", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120108", "description": "当前用户和所添加用户不是好友关系", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361019", "description": "对讲服务端不支持的信令消息", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320009", "description": "传入新的INIT_PARAM并reset(保留，目前未用)", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120109", "description": "对应分享不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361020", "description": "对讲服务端解析对讲请求未携带会话描述能力集", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320010", "description": "请重试", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120110", "description": "好友群组不属于当前用户", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361021", "description": "对讲服务端优先能力集结果为空", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320011", "description": "500毫秒后请重试", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120111", "description": "好友不是等待验证状态", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361022", "description": "cas链路异常", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320012", "description": "token池已满", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120112", "description": "添加应用下的用户为好友失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361023", "description": "对讲服务端分配对讲会话资源失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320013", "description": "P2P client超过限制", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120201", "description": "操作报警信息失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "361024", "description": "对讲服务端解析信令消息失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320014", "description": "sdk未初始化", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120202", "description": "操作留言信息失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390001", "description": "通用错误返回", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320015", "description": "超时", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120301", "description": "根据UUID查询报警消息不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390002", "description": "入参为空指针", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320016", "description": "正在打洞中", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120302", "description": "根据UUID查询图片不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390003", "description": "入参值无效", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320017", "description": "没有视频文件头(播放器层面产生和处理此错误)", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120303", "description": "根据FID查询图片不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390004", "description": "信令消息解析非法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320018", "description": "解码错误/超时(播放器层面产生和处理此错误)", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120305", "description": "设备ip解析错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390005", "description": "内存资源不足", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320019", "description": "取消(保留，用户不用处理)", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120401", "description": "用户云空间信息不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390006", "description": "协议格式不对或者消息体长度超过STREAM_MAX_MSGBODY_LEN", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320020", "description": "播放过程中预连接被用户清除预操作信息", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120402", "description": "云空间操作失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390007", "description": "设备序列号长度不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320021", "description": "流加密码不对", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120403", "description": "用户目录不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390008", "description": "取流url长度不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "320022", "description": "未传入播放窗口", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120404", "description": "要操作的目标目录不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390009", "description": "解析vtm返回vtdu地址不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "100200", "description": "操作成功", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120405", "description": "要删除的文件信息不存在", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390010", "description": "解析vtm返回级联vtdu地址不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101001", "description": "用户名不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120406", "description": "已开通云存储", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390011", "description": "解析vtm返回会话标识长度不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101002", "description": "用户名已被占用", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120407", "description": "开通记录失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390012", "description": "vtdu返回流头长度不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101003", "description": "密码不合法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120500", "description": "获取数据错误", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390013", "description": "vtdu会话长度非法", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101004", "description": "密码为同一字符", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120501", "description": "开锁失败", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390014", "description": "回调函数未注册", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "101006", "description": "手机号码已经被注册", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "120502", "description": "室内机未收到呼叫", "solution": "", "updateTime": 1522034840000}, {"moduleCode": "", "detailCode": "390015", "description": "vtdu成功响应未携带会话标识", "solution": "", "updateTime": 1522034840000}], "code": "200", "msg": "操作成功!"}