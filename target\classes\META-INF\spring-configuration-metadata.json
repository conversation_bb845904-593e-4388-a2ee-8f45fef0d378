{"groups": [{"name": "spring.redis", "type": "com.fwy.common.config.RedissonConfig", "sourceType": "com.fwy.common.config.RedissonConfig"}], "properties": [{"name": "spring.redis.database", "type": "java.lang.Integer", "sourceType": "com.fwy.common.config.RedissonConfig"}, {"name": "spring.redis.host", "type": "java.lang.String", "sourceType": "com.fwy.common.config.RedissonConfig"}, {"name": "spring.redis.password", "type": "java.lang.String", "sourceType": "com.fwy.common.config.RedissonConfig"}, {"name": "spring.redis.port", "type": "java.lang.String", "sourceType": "com.fwy.common.config.RedissonConfig"}, {"name": "spring.redis.timeout", "type": "java.lang.Integer", "sourceType": "com.fwy.common.config.RedissonConfig"}], "hints": []}