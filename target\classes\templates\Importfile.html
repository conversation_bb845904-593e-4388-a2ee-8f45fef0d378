<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
	<head th:fragment="importfile">

	    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}">
	   <!--  <link rel="stylesheet" th:href="@{/admin/layui/css/admin.css}"> -->
	    
		<script th:src="@{/plugins/jquery/jquery-3.4.1.min.js}"></script>
		<script th:src="@{/admin/layui/layui.all.js}"></script>

        <script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
        <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" />
		
		<script th:inline="javascript">
            var ctx = [[${#servletContext.contextPath}]] + '/';
		</script>
		<script th:inline="javascript">
			var contextPath = [[${#httpServletRequest.getContextPath()}]];
		</script>
		<script th:inline="javascript">
			var imageBase = [[${#httpServletRequest.getContextPath()}]] + '/intermediary/images/';
		</script>
		<script th:inline="javascript">
			function replaceWithErrorImage(imageElement) {
				imageElement.src = [[${#httpServletRequest.getContextPath()}]] + '/images/none.jpg';
			}
		</script>
	</head>
</html>