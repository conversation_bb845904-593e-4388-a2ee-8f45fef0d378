<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>选择限制条件</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>

</head>
<body>  
  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body">
		  <form class="layui-form" lay-filter="example">
			  <div class="layui-form-item">
				  <div class="layui-inline">
					  <label class="layui-form-label"><span style="color:red">*</span>数据类型</label>
					  <div class="layui-input-inline" style="width: 300px;">
						  <input type="radio" name="types" th:if="${controlType==2}" value="0" title="整型" th:checked="${types=='0'}">
						  <input type="radio" name="types" th:if="${controlType==2}" value="1" title="浮点型" th:checked="${types=='1'}">
						  <input type="radio" name="types" th:if="${controlType==4}" value="2" title="字符串" th:checked="${types=='2'}">
					  </div>
				  </div>
				  <div class="layui-inline">
					  <label class="layui-form-label"><span style="color:red">*</span>长度</label>
					  <div class="layui-input-inline" style="width: 200px;">
						  <div class="layui-input-inline" style="width: 70px;">
							  <input type="number" id="min" name="min" th:value="${min}" autocomplete="off" lay-verify="required|number" class="layui-input">
						  </div>
						  <div class="layui-form-mid">-</div>
						  <div class="layui-input-inline" style="width: 70px;">
							  <input type="number" id="max" name="max" th:value="${max}" autocomplete="off" lay-verify="required|number|maxlength" class="layui-input">
						  </div>
					  </div>
				  </div>
			  </div>
			  <button style="display:none" lay-submit lay-filter="subBtn" id="subBtn"></button>
		  </form>
      </div>
    </div>
  </div>
  <script>
	  layui.use(['table','layer','jquery', 'form'], function(){
		  var table = layui.table;
		  var layer = layui.layer;
		  var $ = layui.jquery;
		  var form = layui.form;
		  form.render();
		  form.verify({
			  maxlength: function(value, item){ //value：表单的值、item：表单的DOM对象
			  	var minlength=$("#min").val();
			  	if(minlength!=null && minlength!=""){
					if(parseInt(minlength)>parseInt(value)){
						return '长度范围只能从小到大';
					}
				}
			  }
		  });

		  //监听提交
		  form.on('submit(subBtn)', function(data){
			  var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
			  parent.layer.close(index); //再执行关闭
			  return false;
		  });
	  });
  </script>
</body>
</html>