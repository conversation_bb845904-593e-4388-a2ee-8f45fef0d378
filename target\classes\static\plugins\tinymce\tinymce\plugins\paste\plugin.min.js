/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.2 (2020-08-17)
 */
!function(y){"use strict";var e,t,n,r,m=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},c=tinymce.util.Tools.resolve("tinymce.PluginManager"),o=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},f=function(n,r){n.addCommand("mceTogglePlainTextPaste",function(){var e,t;e=n,"text"===(t=r).pasteFormat.get()?(t.pasteFormat.set("html"),o(e,!1)):(t.pasteFormat.set("text"),o(e,!0)),e.focus()}),n.addCommand("mceInsertClipboardContent",function(e,t){t.content&&r.pasteHtml(t.content,t.internal),t.text&&r.pasteText(t.text)})},a=function(e){return function(){return e}},i=a(!1),s=a(!0),u=function(){return l},l=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:i,isSome:i,isNone:s,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:a(null),getOrUndefined:a(undefined),or:n,orThunk:t,map:u,each:function(){},bind:u,exists:i,forall:s,filter:u,equals:e,equals_:e,toArray:function(){return[]},toString:a("none()")}),d=function(n){var e=a(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:s,isNone:i,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return d(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(i,function(e){return t(n,e)})}};return o},p={some:d,none:u,from:function(e){return null===e||e===undefined?l:d(e)}},g=(r="function",function(e){return typeof e===r}),v=Array.prototype.slice,h=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var a=e[o];r[o]=t(a,o)}return r},b=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},x=g(Array.from)?Array.from:function(e){return v.call(e)},P=tinymce.util.Tools.resolve("tinymce.Env"),w=tinymce.util.Tools.resolve("tinymce.util.Delay"),_=tinymce.util.Tools.resolve("tinymce.util.Promise"),T=tinymce.util.Tools.resolve("tinymce.util.VK"),C=function(e){return e.getParam("paste_data_images",!1)},D=function(e){return e.getParam("paste_retain_style_properties")},k=function(e){return e.getParam("validate")},S=function(e){return e.getParam("paste_data_images",!1,"boolean")},O="x-tinymce/html",R="\x3c!-- "+O+" --\x3e",A=function(e){return-1!==e.indexOf(R)},I=tinymce.util.Tools.resolve("tinymce.util.Tools"),F=tinymce.util.Tools.resolve("tinymce.html.Entities"),E=function(e,t,n){var r=e.split(/\n\n/),o=function(e,t){var n,r=[],o="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+F.encodeAllRaw(t[n])+'"');r.length&&(o+=" "+r.join(" "))}return o+">"}(t,n),a="</"+t+">",i=I.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===i.length?i[0]:I.map(i,function(e){return o+e+a}).join("")},M=tinymce.util.Tools.resolve("tinymce.html.DomParser"),N=tinymce.util.Tools.resolve("tinymce.html.Serializer"),B="\xa0",$=tinymce.util.Tools.resolve("tinymce.html.Node"),j=tinymce.util.Tools.resolve("tinymce.html.Schema");function H(t,e){return I.each(e,function(e){t=e.constructor===RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function L(e){var t=j(),n=M({},t),r="",o=t.getShortEndedElements(),a=I.makeMap("script noscript style textarea video audio iframe object"," "),i=t.getBlockElements();return e=H(e,[/<!\[[^\]]+\]>/g]),function s(e){var t=e.name,n=e;if("br"!==t){if("wbr"!==t)if(o[t]&&(r+=" "),a[t])r+=" ";else{if(3===e.type&&(r+=e.value),!e.shortEnded&&(e=e.firstChild))for(;s(e),e=e.next;);i[t]&&n.next&&(r+="\n","p"===t&&(r+="\n"))}}else r+="\n"}(n.parse(e)),r}function z(e){return e=H(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function r(e,t,n){return t||n?B:" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])}function U(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function q(t){var n;return t=t.replace(/^[\u00a0 ]+/,""),I.each([/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],function(e){if(e.test(t))return!(n=!0)}),n}function V(e){var a,i,s=1;function n(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=n(e),e=e.next;);return t}function u(e,t){if(3!==e.type||!t.test(e.value)){if(e=e.firstChild)do{if(!u(e,t))return}while(e=e.next);return 1}e.value=e.value.replace(t,"")}function t(e,t,n){var r=e._listLevel||s;r!==s&&(a=r<s?a&&a.parent.parent:(i=a,null)),a&&a.name===t?a.append(e):(i=i||a,a=new $(t,1),1<n&&a.attr("start",""+n),e.wrap(a)),e.name="li",s<r&&i&&i.lastChild.append(a),s=r,function o(e){if(e._listIgnore)e.remove();else if(e=e.firstChild)for(;o(e),e=e.next;);}(e),u(e,/^\u00a0+/),u(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),u(e,/^\u00a0+/)}for(var r=[],o=e.firstChild;null!=o;)if(r.push(o),null!==(o=o.walk()))for(;void 0!==o&&o.parent!==e;)o=o.walk();for(var l=0;l<r.length;l++)if("p"===(e=r[l]).name&&e.firstChild){var c=n(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(c)){t(e,"ul");continue}if(q(c)){var f=/([0-9]+)\./.exec(c),d=1;f&&(d=parseInt(f[1],10)),t(e,"ol",d);continue}if(e._listLevel){t(e,"ul",1);continue}a=null}else i=a,a=null}function K(n,r,o,a){var i,s={},e=n.dom.parseStyle(a);return I.each(e,function(e,t){switch(t){case"mso-list":(i=/\w+ \w+([0-9]+)/i.exec(a))&&(o._listLevel=parseInt(i[1],10)),/Ignore/i.test(e)&&o.firstChild&&(o._listIgnore=!0,o.firstChild._listIgnore=!0);break;case"horiz-align":t="text-align";break;case"vert-align":t="vertical-align";break;case"font-color":case"mso-foreground":t="color";break;case"mso-background":case"mso-highlight":t="background";break;case"font-weight":case"font-style":return void("normal"!==e&&(s[t]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void o.remove()}0!==t.indexOf("mso-comment")?0!==t.indexOf("mso-")&&("all"===D(n)||r&&r[t])&&(s[t]=e):o.remove()}),/(bold)/i.test(s["font-weight"])&&(delete s["font-weight"],o.wrap(new $("b",1))),/(italic)/i.test(s["font-style"])&&(delete s["font-style"],o.wrap(new $("i",1))),(s=n.dom.serializeStyle(s,o.name))||null}var X=function(e,t){return e.getParam("paste_enable_default_filters",!0)?function(r,e){var o,t=D(r);t&&(o=I.makeMap(t.split(/[, ]/))),e=H(e,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,B],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return 0<t.length?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join(B):""}]]);var n=r.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody"),a=j({valid_elements:n,valid_children:"-li[p]"});I.each(a.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var i=M({},a);i.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)(t=e[n]).attr("style",K(r,o,t,t.attr("style"))),"span"===t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),i.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),i.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),i.addNodeFilter("a",function(e){for(var t,n,r,o=e.length;o--;)if(n=(t=e[o]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=(n=n.split("#")[1])&&"#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=i.parse(e);return r.getParam("paste_convert_word_fake_lists",!0)&&V(s),e=N({validate:k(r)},a).serialize(s)}(e,t):t},W=function(e,t){return{content:e,cancelled:t}},Y=function(e,t,n,r){var o,a,i,s,u,l,c,f,d,m,p,g,v=(o=t,a=n,i=r,e.fire("PastePreProcess",{content:o,internal:a,wordContent:i})),h=function(e,t){var n=M({},e.schema);n.addNodeFilter("meta",function(e){I.each(e,function(e){return e.remove()})});var r=n.parse(t,{forced_root_block:!1,isRootContent:!0});return N({validate:k(e)},e.schema).serialize(r)}(e,v.content);return e.hasEventListeners("PastePostProcess")&&!v.isDefaultPrevented()?(u=h,l=n,c=r,p=(s=e).dom.create("div",{style:"display:none"},u),f=p,d=l,m=c,g=s.fire("PastePostProcess",{node:f,internal:d,wordContent:m}),W(g.node.innerHTML,g.isDefaultPrevented())):W(h,v.isDefaultPrevented())},Z=function(e,t){return e.insertContent(t,{merge:e.getParam("paste_merge_formats",!0),paste:!0}),!0},G=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},J=function(e){return G(e)&&/.(gif|jpe?g|png)$/.test(e)},Q=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!G(t))&&(o=t,a=n,(r=e).undoManager.extra(function(){a(r,o)},function(){r.execCommand("mceInsertLink",!1,o)}),!0);var r,o,a},ee=function(e,t,n){return!!J(t)&&(o=t,a=n,(r=e).undoManager.extra(function(){a(r,o)},function(){r.insertContent('<img src="'+o+'">')}),!0);var r,o,a},te=function(e,t,n){var r,o;n||!1===e.getParam("smart_paste",!0)?Z(e,t):(r=e,o=t,I.each([Q,ee,Z],function(e){return!0!==e(r,o,Z)}))},ne=function(e){return"\n"===e||"\r"===e},re=function(e,t){var n,r,o,a,i=(n=" ",(r=e.getParam("paste_tab_spaces",4,"number"))<=0?"":new Array(r+1).join(n)),s=t.replace(/\t/g,i);return(a={pcIsSpace:!(o=function(e,t){return-1!==" \f\t\x0B".indexOf(t)||t===B?e.pcIsSpace||""===e.str||e.str.length===s.length-1||(n=s,(r=e.str.length+1)<n.length&&0<=r&&ne(n[r]))?{pcIsSpace:!1,str:e.str+B}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:ne(t),str:e.str+t};var n,r}),str:""},b(s,function(e){a=o(a,e)}),a).str},oe=function(e,t,n,r){var o,a,i,s,u,l=(o=e,i=n,s=U(a=t),u=s?X(o,a):a,Y(o,u,i,s));!1===l.cancelled&&te(e,l.content,r)},ae=function(e,t,n){var r=n||A(t);oe(e,t.replace(R,""),r,!1)},ie=function(e,t){var n,r,o,a=e.dom.encode(t).replace(/\r\n/g,"\n"),i=re(e,a),s=(n=i,r=e.getParam("forced_root_block"),o=e.getParam("forced_root_block_attrs"),r?E(n,!0===r?"p":r,o):n.replace(/\r?\n/g,"<br>"));oe(e,s,!1,!0)},se=function(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&0<n.length&&-1===n.indexOf("data:text/mce-internal,")&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var o=e.types[r];try{t[o]=e.getData(o)}catch(a){t[o]=""}}}return t},ue=function(e,t){return t in e&&0<e[t].length},le=function(e){return ue(e,"text/html")||ue(e,"text/plain")},ce=function $e(e){var t=0;return function(){return e+t++}}("mceclip"),fe=function(e,t){var n,r,o,a,i,s,u,l=(n=t.uri,(r=/data:([^;]+);base64,([a-z0-9\+\/=]+)/i.exec(n))?{type:r[1],data:decodeURIComponent(r[2])}:{type:null,data:null}),c=l.data,f=l.type,d=ce(),m=e.getParam("images_reuse_filename")&&t.blob.name?(o=e,a=t.blob.name,(i=a.match(/([\s\S]+?)\.(?:jpeg|jpg|png|gif)$/i))?o.dom.encode(i[1]):null):d,p=new y.Image;if(p.src=t.uri,s=p,!(u=e.getParam("images_dataimg_filter"))||u(s)){var g=e.editorUpload.blobCache,v=void 0,h=g.getByData(c,f);h?v=h:(v=g.create(d,t.blob,c,m),g.add(v)),ae(e,'<img src="'+v.blobUri()+'">',!1)}else ae(e,'<img src="'+t.uri+'">',!1)},de=function(t,e,n){var r,o,a,i,s="paste"===e.type?e.clipboardData:e.dataTransfer;if(S(t)&&s){var u=(a=(o=s).items?h(x(o.items),function(e){return e.getAsFile()}):[],i=o.files?x(o.files):[],function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var a=e[r];t(a,r)&&n.push(a)}return n}(0<a.length?a:i,function(e){return/^image\/(jpeg|png|gif|bmp)$/.test(e.type)}));if(0<u.length)return e.preventDefault(),r=u,_.all(h(r,function(r){return new _(function(e){var t=r.getAsFile?r.getAsFile():r,n=new window.FileReader;n.onload=function(){e({blob:t,uri:n.result})},n.readAsDataURL(t)})})).then(function(e){n&&t.selection.setRng(n),b(e,function(e){fe(t,e)})}),!0}return!1},me=function(e){return T.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},pe=function(s,u,l){var t,c,f=(t=m(p.none()),{clear:function(){t.set(p.none())},set:function(e){t.set(p.some(e))},isSet:function(){return t.get().isSome()},on:function(e){t.get().each(e)}});function d(e,t,n,r){var o;ue(e,"text/html")?o=e["text/html"]:(o=u.getHtml(),r=r||A(o),u.isDefaultContent(o)&&(n=!0)),o=z(o),u.remove();var a=!1===r&&!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(o),i=J(o);o.length&&(!a||i)||(n=!0),(n||i)&&(o=ue(e,"text/plain")&&a?e["text/plain"]:L(o)),u.isDefaultContent(o)?t||s.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):n?ie(s,o):ae(s,o,r)}s.on("keydown",function(e){function t(e){me(e)&&!e.isDefaultPrevented()&&u.remove()}if(me(e)&&!e.isDefaultPrevented()){if((c=e.shiftKey&&86===e.keyCode)&&P.webkit&&-1!==y.navigator.userAgent.indexOf("Version/"))return;if(e.stopImmediatePropagation(),f.set(e),window.setTimeout(function(){f.clear()},100),P.ie&&c)return e.preventDefault(),n=!0,void s.fire("paste",{ieFake:n});u.remove(),u.create(),s.once("keyup",t),s.once("paste",function(){s.off("keyup",t)})}var n});s.on("paste",function(e){var t,n,r=f.isSet(),o=(t=s,se(e.clipboardData||t.getDoc().dataTransfer)),a="text"===l.get()||c,i=ue(o,O);(c=!1,e.isDefaultPrevented()||(n=e.clipboardData,-1!==y.navigator.userAgent.indexOf("Android")&&n&&n.items&&0===n.items.length))?u.remove():le(o)||!de(s,e,u.getLastRng()||s.selection.getRng())?(r||e.preventDefault(),!P.ie||r&&!e.ieFake||ue(o,"text/html")||(u.create(),s.dom.bind(u.getEl(),"paste",function(e){e.stopPropagation()}),s.getDoc().execCommand("Paste",!1,null),o["text/html"]=u.getHtml()),ue(o,"text/html")?(e.preventDefault(),i=i||A(o["text/html"]),d(o,r,a,i)):w.setEditorTimeout(s,function(){d(o,r,a,i)},0)):u.remove()})},ge=function(i,e,t){var s;pe(i,e,t),i.parser.addNodeFilter("img",function(e,t,n){var r,o=function(e){e.attr("data-mce-object")||s===P.transparentSrc||e.remove()};if(!S(i)&&((r=n).data&&!0===r.data.paste))for(var a=e.length;a--;)(s=e[a].attr("src"))&&(0===s.indexOf("webkit-fake-url")?o(e[a]):i.getParam("allow_html_data_urls",!1,"boolean")||0!==s.indexOf("data:")||o(e[a]))})},ve=function(e){return P.ie&&e.inline?y.document.body:e.getBody()},he=function(t,e,n){var r;ve(r=t)!==r.getBody()&&t.dom.bind(e,"paste keyup",function(e){xe(t,n)||t.fire("paste")})},ye=function(e){return e.dom.get("mcepastebin")},be=function(e,t){return t===e},xe=function(e,t){var n,r=ye(e);return(n=r)&&"mcepastebin"===n.id&&be(t,r.innerHTML)},Pe=function(e){var t=m(null),n="%MCEPASTEBIN%";return{create:function(){return function(e,t,n){var r=e.dom,o=e.getBody();t.set(e.selection.getRng());var a=e.dom.add(ve(e),"div",{id:"mcepastebin","class":"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n);(P.ie||P.gecko)&&r.setStyle(a,"left","rtl"===r.getStyle(o,"direction",!0)?65535:-65535),r.bind(a,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),he(e,a,n),a.focus(),e.selection.select(a,!0)}(e,t,n)},remove:function(){return function(e,t){if(ye(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(e,t)},getEl:function(){return ye(e)},getHtml:function(){return function(n){var t=function(e,t){e.appendChild(t),n.dom.remove(t,!0)},e=I.grep(ve(n).childNodes,function(e){return"mcepastebin"===e.id}),r=e.shift();I.each(e,function(e){t(r,e)});for(var o=n.dom.select("div[id=mcepastebin]",r),a=o.length-1;0<=a;a--){var i=n.dom.create("div");r.insertBefore(i,o[a]),t(i,o[a])}return r?r.innerHTML:""}(e)},getLastRng:function(){return t.get()},isDefault:function(){return xe(e,n)},isDefaultContent:function(e){return e===n}}},we=function(e,t,n){if(r=e,!1!==P.iOS||"function"!=typeof(null==r?void 0:r.setData))return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(O,t),!0}catch(o){return!1}var r},_e=function(e,t,n,r){we(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},Te=function(s){return function(e,t){var n=R+e,r=s.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),o=s.dom.create("div",{contenteditable:"true"},n);s.dom.setStyles(r,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),r.appendChild(o),s.dom.add(s.getBody(),r);var a=s.selection.getRng();o.focus();var i=s.dom.createRng();i.selectNodeContents(o),s.selection.setRng(i),w.setTimeout(function(){s.selection.setRng(a),r.parentNode.removeChild(r),t()},0)}},Ce=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},De=function(e){return!e.selection.isCollapsed()||!!(t=e).dom.getParent(t.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",t.getBody());var t},ke=function(e){var t,n;e.on("cut",(t=e,function(e){De(t)&&_e(e,Ce(t),Te(t),function(){if(P.browser.isChrome()){var e=t.selection.getRng();w.setEditorTimeout(t,function(){t.selection.setRng(e),t.execCommand("Delete")},0)}else t.execCommand("Delete")})})),e.on("copy",(n=e,function(e){De(n)&&_e(e,Ce(n),Te(n),function(){})}))},Se=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Oe=function(e,t){return Se.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},Re=function(e,t){e.focus(),e.selection.setRng(t)},Ae=function(i,s,u){i.getParam("paste_block_drop",!1)&&i.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),C(i)||i.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&0<t.files.length&&e.preventDefault()}),i.on("drop",function(e){var t=Oe(i,e);if(!e.isDefaultPrevented()&&!u.get()){var n,r=s.getDataTransferItems(e.dataTransfer),o=s.hasContentType(r,O);if(s.hasHtmlOrText(r)&&(!(n=r["text/plain"])||0!==n.indexOf("file://"))||!s.pasteImageData(e,t))if(t&&i.getParam("paste_filter_drop",!0)){var a=r["mce-internal"]||r["text/html"]||r["text/plain"];a&&(e.preventDefault(),w.setEditorTimeout(i,function(){i.undoManager.transact(function(){r["mce-internal"]&&i.execCommand("Delete"),Re(i,t),a=z(a),r["text/html"]?s.pasteHtml(a,o):s.pasteText(a)})}))}}}),i.on("dragstart",function(e){u.set(!0)}),i.on("dragover dragend",function(e){C(i)&&!1===u.get()&&(e.preventDefault(),Re(i,Oe(i,e))),"dragend"===e.type&&u.set(!1)})};function Ie(t,n){t.on("PastePreProcess",function(e){e.content=n(t,e.content,e.internal,e.wordContent)})}function Fe(e,t){if(!U(t))return t;var n=[];return I.each(e.schema.getBlockElements(),function(e,t){n.push(t)}),t=H(t,[[new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g"),"$1"]]),t=H(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function Ee(e,t,n,r){if(r||n)return t;var l,o=e.getParam("paste_webkit_styles");if(!1===e.getParam("paste_remove_styles_if_webkit",!0)||"all"===o)return t;if(o&&(l=o.split(/[, ]/)),l){var c=e.dom,f=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var o=c.parseStyle(c.decode(n)),a={};if("none"===l)return t+r;for(var i=0;i<l.length;i++){var s=o[l[i]],u=c.getStyle(f,l[i],!0);/color/.test(l[i])&&(s=c.toHex(s),u=c.toHex(u)),u!==s&&(a[l[i]]=s)}return(a=c.serializeStyle(a,"span"))?t+' style="'+a+'"'+r:t+r})}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r})}function Me(n,e){n.$("a",e).find("font,u").each(function(e,t){n.dom.remove(t,!0)})}var Ne=function(e){P.webkit&&Ie(e,Ee),P.ie&&(Ie(e,Fe),function r(t,n){t.on("PastePostProcess",function(e){n(t,e.node)})}(e,Me))},Be=function(n,r){return function(t){t.setActive("text"===r.pasteFormat.get());var e=function(e){return t.setActive(e.state)};return n.on("PastePlainTextToggle",e),function(){return n.off("PastePlainTextToggle",e)}}};!function je(){c.add("paste",function(e){if(!1==!(!/(^|[ ,])powerpaste([, ]|$)/.test(e.getParam("plugins"))||!c.get("powerpaste")||("undefined"!=typeof y.window.console&&y.window.console.log&&y.window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),0))){var t=m(!1),n=m(e.getParam("paste_as_text",!1)?"text":"html"),r=(u=n,l=Pe(s=e),s.on("PreInit",function(){return ge(s,l,u)}),{pasteFormat:u,pasteHtml:function(e,t){return ae(s,e,t)},pasteText:function(e){return ie(s,e)},pasteImageData:function(e,t){return de(s,e,t)},getDataTransferItems:se,hasHtmlOrText:le,hasContentType:ue}),o=Ne(e);return i=r,(a=e).ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:Be(a,i)}),a.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:Be(a,i)}),f(e,r),function(e){var t=e.plugins.paste,n=e.getParam("paste_preprocess");n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=e.getParam("paste_postprocess");r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})}(e),ke(e),Ae(e,r,t),{clipboard:r,quirks:o}}var a,i,s,u,l})}()}(window);