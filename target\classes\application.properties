spring.profiles.active=test
server.port=8867
server.servlet.context-path=/kth
# 文件上传大小
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
# 设置最大堆大小为1.5GB
# 其他 JVM 参数
spring.boot.admin.jvmoptions=-Xms1536m -Xmx1536m -Xmn512m -Xss256k -XX:SurvivorRatio=8 -XX:MaxMetaspaceSize=256m -XX:+UseConcMarkSweepGC -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/path/to/dumpfile -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+UseCompressedOops -XX:+UseStringDeduplication

# 数据源配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.url=******************************************************************************************************************************************************************
#spring.datasource.url=***************************************************************************************************************************************************************
#spring.datasource.url=****************************************************************************************************************************************************************
#spring.datasource.url=************************************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=jxkth_mysql
#spring.datasource.password=123456
#spring.datasource.url=************************************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=123456

spring.datasource.url=*****************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root


#连接池配置
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
#最大连接数量
spring.datasource.druid.max-active=50
#最小空闲连接
spring.datasource.druid.min-idle=5
#最大空闲连接
jasypt.encryptor.password=jxkth
jasypt.encryptor.algorithm=PBEWithMD5AndDES
jasypt.encryptor.iv-generator-classname=org.jasypt.iv.NoIvGenerator
#初始化连接
spring.datasource.druid.initial-size=5
#连接被泄露时是否打印：与removeAbandoned，removeAbandonedTimeout一起设置
spring.datasource.druid.log-abandoned=true
#是否自动回收超时连接
spring.datasource.druid.remove-abandoned=false
#当一个连接活动的时间超过多久被认定为是泄露的或不正常的
spring.datasource.druid.remove-abandoned-timeout=10
#最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制
#spring.datasource.druid.max-wait=1000
# 配置获取连接等待超时的时间
spring.datasource.druid.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.druid.time-between-eviction-runs-millis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.test-on-return=false
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，wall用于防火墙
#spring.datasource.filters=
spring.datasource.druid.filters=stat,slf4j,wall
#使用非公平锁
spring.datasource.druid.use-unfair-lock=true
spring.datasource.druid.removeAbandoned=true
spring.datasource.druid.remove-abandoned-timeout-millis=1800
spring.datasource.druid.logAbandoned=false
spring.datasource.druid.validation-query-timeout=1
spring.datasource.druid.keep-alive=true
#mybatis
#mybatis.mapper-locations=classpath:mapper/*.xml,classpath:mapper/**/*.xml
#mybatis-plus
mybatis-plus.mapper-locations=classpath:mapper/*.xml,classpath:mapper/**/*.xml
#实体扫描，多个package用逗号或者分号分隔
mybatis-plus.typeAliasesPackage=com.fwy.*.entity, com.fwy.*.*.condition
mybatis-plus.check-config-location=true
#mybatis-plus配置控制台打印完整带参数SQL语句
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.nologging.NoLoggingImpl
#是否开启自动驼峰命名规则（camel case）映射
mybatis-plus.configuration.map-underscore-to-camel-case=false
#全局地开启或关闭配置文件中的所有映射器已经配置的任何缓存
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.call-setters-on-nulls=true
#配置JdbcTypeForNull, oracle数据库必须配置
mybatis-plus.configuration.jdbc-type-for-null=null
#MyBatis 自动映射时未知列或未知属性处理策略 NONE：不做任何处理 (默认值), WARNING：以日志的形式打印相关警告信息, FAILING：当作映射失败处理，并抛出异常和详细信息
mybatis-plus.configuration.auto-mapping-unknown-column-behavior=warning
mybatis-plus.global-config.db-config.logic-delete-value=3
mybatis-plus.global-config.db-config.logic-not-delete-value=1
#主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
#mybatis-plus.global-config.id-type=1
#Sequence序列接口实现类配置
#mybatis-plus.global-config.key-generator=com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator
#pagehelper
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
logging.level.root=debug
#thymelea模板配置
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
#热部署文件，页面不产生缓存，及时更新
spring.thymeleaf.cache=false
spring.resources.chain.strategy.content.enabled=true
spring.resources.chain.strategy.content.paths=/**
#表示所有的访问都经过静态资源路径
spring.mvc.static-path-pattern=/**
#配置静态资源路径，最后面是上传文件保存路径，这个静态路径不能乱配，会导致内存占用过大。
spring.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
#cookie到期时间(秒)86400为1天
cookie.timeout=86400
#下载路径
upload.file.path=D://intermediary/files/
#控制系统角色id
controlSys.roleId=1002
##图片上传及展示
img.upload.imgUrl=D://intermediary/images/
##配置上传参数大小 （无限）
server.tomcat.max-http-post-size=-1
##AES加密秘钥
AES.KEY=9B4E7C1045A02A2F
##人口库token
renkouToken=E7C5EFFD9806F85141188C47986B6FC5
##人口库接口
renkouUrl=http://10.136.4.18:9686/KeyPersonnelThan/api/getIdInfo
##未获取到人口照片时尝试调用次数
renkouCount=10
##调人口库间隔时间
renkouTimeout=3000
##AES加密秘钥
project.url=http://192.168.0.33:8867/kth/
API.TOKEN=B52F632AC9CD85BD
similarNumber=50.0
