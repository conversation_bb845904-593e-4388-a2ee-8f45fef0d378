<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>字典详情管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
    <script th:src="@{/scripts/common/openWay.js}"></script>

</head>
<body>

<div id="" class="layui-layer-content" style="overflow: visible;">
    <form id="modelAuthForm" lay-filter="modelAuthForm" class="layui-form">
        <div class="layui-form-item">
            <input name="id" id="id" type="hidden" th:value="${Detail?.id}">
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"><span style="color:#ff0000">*</span>字典类型:</label>
                <div class="layui-input-inline">
                    <input class="layui-input"
                           oninput="del(this,'blank|char')"
                           type="text" name="dicCode" disabled="disabled" id="dicCode" th:value="${Detail?.dicCode}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"><span style="color:red">*</span>数据值:</label>
                <div class="layui-input-inline">
                    <input type="text"
                           oninput="del(this,'blank|char')" placeholder="请输入数据值"
                           class="layui-input" lay-verify="required|dicValue" name="dicValue" th:value="${Detail?.dicValue}" id="dicValue">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"><span style="color:red">*</span>数据描述:</label>
                <div class="layui-input-inline">
                        <textarea name="disCription"
                                  oninput="del(this,'blank|char')" placeholder="请输入数据描述"
                                  id="disCription" lay-verify="required|disCription" th:text="${Detail?.disCription}" class="layui-textarea"></textarea>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">是否启用:</label>
                <div class="layui-input-inline">
                    <input type="checkbox" th:attr="checked=${Detail?.isStart == 1 ? true : false}" value=1 name="isStart" id="isStart" lay-skin="switch" lay-filter="isStart" lay-text="启用|停用">
                </div>
            </div>
        </div>

        <button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>
    </form>
</div>
<script>
    layui.use('form', function(){
        var form = layui.form;
        form.render();
        form.verify({
            dicValue:function (value) {
                if (value.length>200){
                    return "数据值不能超过200个字符";
                }
            },
            disCription:function (value) {
                if (value.length>200){
                    return "数据描述不能超过200个字符"
                }
            },
        })
        //监听提交
        form.on('submit(submitBut)', function(data){
            var saveUrl = ctx + 'dataDetailController/saveDataDetail';
            $.ajax({
                url: saveUrl,
                type: 'POST',
                dataType: 'JSON',
                async: false,
                data: data.field,
                success: function(res) {
                    if(res.code == 0){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        parent.layer.msg(res.msg, { icon: 1});
                        parent.layui.table.reload("dataDetail");
                    }else{
                        layer.msg(res.msg,{icon: 2});
                    }
                },
                error:function(){
                    layer.close(loading);
                    layer.msg('操作失败',{icon: 1});
                }
            });
            return false;
        });
    })

</script>
</body>
</html>