<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>添加人员</title>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
</head>

<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form class="layui-form layui-hide" action="">
                        <input type="hidden" name="list" id="list">
                    </form>
                    <blockquote class="layui-elem-quote quoteBox" id="search" style="margin-bottom: 5px">

                        <div class="layui-inline" style="margin-top:5px;">
                            <label class="layui-form-label">人员姓名：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" style="width: 157px" id="fullName" autocomplete="off" placeholder="人员姓名">
                            </div>
                        </div>
                        <div class="layui-inline" style="margin-top:5px;">
                            <label class="layui-form-label">身份证号：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" style="width: 157px" id="idCardNumber" autocomplete="off" placeholder="身份证号">
                            </div>
                        </div>
                        <div class="layui-inline" style="margin-top:5px;">
                            <label class="layui-form-label">手机号：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" style="width: 157px" id="phone" autocomplete="off" placeholder="手机号">
                            </div>
                        </div>
                        <div class="layui-inline" style="margin-top: 5px;">
                            <label class="layui-form-label">所属部门：</label>
                            <div class="layui-input-inline">
                                <div class="xm-select" id="deptId">
                                </div>
                            </div>
                        </div>
                        <div class="layui-inline" style="margin-top:5px;margin-left: 20px">
                            <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                            <button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</button>
                            <button class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
                        </div>

                    </blockquote>

                    <table lay-size="sm" class="layui-hide" id="user_table" lay-filter="user_table"></table>
                    <div class="layui-form-item" style="display:none;">
                        <label class="layui-form-label" style="width: 150px;"></label>
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit="" lay-filter="submitBut" id="submitBut">保存</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['table','laydate', 'form'], function(){
        var admin = layui.admin,
            laydate = layui.laydate,
            table = layui.table,
            form = layui.form;
        form.render();

        var quanju = new Array();//全局
        var huancun = new Array();//缓存

        table.render({
            elem: '#user_table'
            ,url: ctx + '/whiteListController/addWhiteListUser'
            ,title: '人员表'
            ,toolbar: '#topToolbar'
            ,defaultToolbar:[""]
            // ,height : 'full-130'
            ,cols: [
                [
                    {type : 'checkbox'}
                    ,{field:'id', title:'id', hide: true}
                    ,{field:'userName', title:'userName', hide: true}
                    ,{field:'fullName',title:'人员姓名',width:180,align:'center'}
                    ,{field:'idCardNumber',title:'身份证号',width:180,align:'center',templet:function(data){
                        var html = "";
                        if(data.userInfo == null || data.userInfo.idCardNumber == null){
                            return  html;
                        }else {
                            return data.userInfo.idCardNumber
                        }
                    }}
                    ,{field:'phone',title:'手机号',width:180,align:'center'}
                    ,{field:'deptName', title:'所属部门', align: 'center'  ,templet:function(data){
                        var html = "";
                        if (data.dept.deptName==null){
                            return html;
                        }else {
                            return data.dept.deptName
                        }
                    }
                }
                ]
            ]
            ,request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            ,parseData: function(res){ //res 即为原始返回的数据
                console.log(res);
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            ,page: true
            ,done: function(res, curr, count){
                //数据表格加载完成时调用此函数
                //如果是异步请求数据方式，res即为你接口返回的信息。
                //设置全部数据到全局变量
                quanju=res.data;

                //在缓存中找到id ,然后设置data表格中的选中状态
                //循环所有数据，找出对应关系，设置checkbox选中状态
                for(var i=0;i< res.data.length;i++){
                    for (var j = 0; j < huancun.length; j++) {
                        //数据id和要勾选的id相同时checkbox选中
                        if(res.data[i].userName == huancun[j])
                        {
                            //这里才是真正的有效勾选
                            res.data[i]["LAY_CHECKED"]='true';
                            //找到对应数据改变勾选样式，呈现出选中效果
                            var index= res.data[i]['LAY_TABLE_INDEX'];
                            $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true);
                            $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').next().addClass('layui-form-checked');
                        }
                    }
                }
            }
        });

        //复选框选中监听,将选中的id 设置到缓存数组,或者删除缓存数组
        table.on('checkbox(user_table)', function (obj) {
            if(obj.checked==true){
                if(obj.type=='one'){
                    if(!huancun.includes(obj.data.userName)){
                        huancun.push(obj.data.userName);
                    }
                }else{
                    for(var i=0;i<quanju.length;i++){
                        if(!huancun.includes(quanju[i].userName)){
                            huancun.push(quanju[i].userName);
                        }
                    }
                }
            }else{
                if(obj.type=='one'){
                    for(var i=0;i<huancun.length;i++){
                        if(huancun[i]==obj.data.userName){
                            removeByValue(huancun,huancun[i]);//调用自定义的根据值移除函数
                        }
                    }
                }else{
                    for(var i=0;i<huancun.length;i++){
                        for(var j=0;j<quanju.length;j++){
                            if(huancun[i]==quanju[j].userName){
                                removeByValue(huancun,+huancun[i]);//调用自定义的根据值移除函数
                            }
                        }
                    }
                }
            }
        });

        //自定义方法，根据值去移除
        function removeByValue(arr, val) {
            for(var i = 0; i < arr.length; i++) {
                if(arr[i] == val) {
                    arr.splice(i, 1);
                    break;
                }
            }
        }

        var active = {
            reload:function(){
                var deptId = "";
                if(!(dialog_deptId.getValue()=="")){
                    deptId = dialog_deptId.getValue()[0].ID;
                }
                var fullName = $("#fullName").val(),
                    idCardNumber = $("#idCardNumber").val(),
                    phone = $("#phone").val();
                table.reload('user_table',{
                    page:{
                        curr:1
                    },
                    where:{fullName:fullName,
                        idCardNumber: idCardNumber,
                        phone: phone,
                        deptId: deptId}
                })
            }
        };

        $("#search_btn").click(function(){
            var type = 'reload';
            active[type] ? active[type].call(this) : '';
        });
        $("#unset_Btn").click(function(){
            $("#search :input").val("");
            dialog_deptId.reset();
            $.ajax({
                url:ctx + '/deptController/tree',
                method:'get',
                dataType : 'json' ,
                success:function(response){
                    var arr = response.data;//这里的data可能是ajax返回的数据
                    dialog_deptId.update({
                        data: arr
                    });
                },
                error:function (res) {
                }
            });
            layui.form.render();
            table.reload('user_table', {
                where: null
                ,page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
        $(document).keydown(function(event){
            if(event.keyCode == 13){
                var type = 'reload';
                active[type] ? active[type].call(this) : '';
            }
        });

        //监听提交
        form.on('submit(submitBut)', function(data){
            if (huancun.length < 1) {
                layer.msg('请选择需要添加的人员');
                return false;
            }
            names = huancun.join(",");
            data.field.list = names;
            //alert(ids);
            var saveUrl = ctx + '/whiteListController/saveWhiteListUser';
            xadmin.submitForm(saveUrl, data.field, 'whiteList_table');
            return false; //防止提交两次表单
        });
    });

    var dialog_deptId = xmSelect.render({
        el: '#deptId',
        filterable: true,
        model: { label: { type: 'block' }  },
        radio: true,
        clickClose: true,
        tree: {
            show: true,
            strict: false,
            indent: 5,
            expandedKeys: [ -1 ]
        },
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width:'145px'
        },
        prop : {
            name : "deptName",
            value : "ID"
        },
        height: '200px',
        empty : '暂无数据',
        data : []
    });
    $(function(){
        $.ajax({
            url:ctx + '/deptController/tree',
            method:'get',
            dataType : 'json' ,
            success:function(response){
                var arr = response.data;//这里的data可能是ajax返回的数据
                dialog_deptId.update({
                    data: arr
                });
            },
            error:function (res) {
            }
        });
    });

    layui.form.render();
</script>

</body></html>