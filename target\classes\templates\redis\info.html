<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="UTF-8">
    <title>Insert title here</title>
</head>
<link rel="stylesheet" data-th-href="@{/redis/css/info.css}">
<link rel="stylesheet" data-th-href="@{/redis/css/base.css}">
<body>
<div class="card">
    <div class="card-header">
        <h2 class="card-title" style="padding:1rem 0">Redis 详细信息：</h2>
    </div>
    <div class="card-block">
        <table class="table table-condensed table-hover"
               style="word-break: break-all; word-wrap: break-word;">
            <tbody>
            <tr th:each="il:${infoList}">
                <td style="width:30%;font-weight:bold">[[${il.key}]]</td>
                <td style="width:40%">[[${il.description }]]</td>
                <td style="width:30%">[[${il.value}]]</td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>