package com.fwy.corebasic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fwy.common.annotation.DeleteCache;
import com.fwy.common.annotation.FwyCache;
import com.fwy.common.help.QueryHelper;
import com.fwy.common.help.QueryInfo;
import com.fwy.common.help.QueryInfoHelper;
import com.fwy.common.utils.LogUtil;
import com.fwy.corebasic.dao.IDeptDao;
import com.fwy.corebasic.entity.Core_Dept;
import com.fwy.corebasic.help.DeptHelper;
import com.fwy.corebasic.service.IDeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("deptService")
public class DeptServiceImpl extends ServiceImpl<IDeptDao, Core_Dept> implements IDeptService {
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private IDeptDao deptDao;

    @Override
    @FwyCache(prefix ="ssoOracle:dept:list",timeType = "Redis_timeType_Dept")
    public List<Core_Dept> list(String queryinfo, Long deptId,List<Long> depts) {
        QueryInfo queryInfo = QueryInfoHelper.forWebJson(queryinfo);
        String whereSql = QueryHelper.getWhereSql("", queryInfo.getWhereInfos());
        return deptDao.list(whereSql, deptId,depts);
    }
    @Override
    public int getCountByDeptcord(String deptCode) {
        return deptDao.getCountByDeptcord(deptCode);
    }

    @Override
    @Transactional
    @DeleteCache(prefix ="ssoOracle:dept")
    public void save(Core_Dept dept, String parentDeptName, boolean isUpdateOrderCode) throws Exception {


        Core_Dept parentDept = deptDao.getDeptByDeptName(parentDeptName);

        if (ObjectUtils.isEmpty(parentDept)){
            throw new Exception("上级部门不存在");
        }

        Long parentId = parentDept.getId();
        // 3. 循环引用检查（包括间接循环）
        if (dept.getId() != null) {
            validateCircularReference(dept.getId(), parentId);
        }
        // 4. 验证层级规则
        validateDepartmentLevel(parentDept, dept.getLEVEL());

        dept.setParentId(parentDept.getId());

//        Long parentId = deptDao.getDeptByDeptName(parentDeptName);
//        if (parentId == null) {
//            parentId = 0l;
//            dept.setParentId(parentId);
//        } else {
//            dept.setParentId(parentId);
//        }
        if (isUpdateOrderCode) {
            String orderCode = deptDao.getInsertCode(parentId);
            if (orderCode == null) {
                Core_Dept core_Dept = deptDao.getObjectById(parentId);
                if(core_Dept==null){
                    orderCode="00001";
                }else {
                    orderCode=core_Dept.getOrderCode();
                }
                StringBuilder sb = new StringBuilder();
                sb.append(orderCode);
                for (int i = 0; i < DeptHelper.length; i++) {
                    sb.append("0");
                }
                orderCode = sb.toString();
            }
            LogUtil.debug("保存的orderCode:{}", orderCode);
            String insertOrderCode = DeptHelper.getInsertOrderCode(orderCode);
            dept.setOrderCode(insertOrderCode);
        }
        if (dept.getId() == null || dept.getId() == 0) {
            deptDao.save(dept);
        } else {
            deptDao.update(dept);
        }
        redisTemplate.delete("Redis_timeType_Dept_list");
    }

    // 循环引用检查
    private void validateCircularReference(Long deptId, Long parentId) throws Exception {
        if (deptId.equals(parentId)) {
            throw new Exception("上级部门不能是本部门");
        }

        // 检查间接循环引用
        Set<Long> visited = new HashSet<>();
        Long currentParentId = parentId;

        while (currentParentId != null && currentParentId != 0) {
            if (visited.contains(currentParentId)) {
                throw new Exception("检测到部门层级循环引用");
            }
            if (currentParentId.equals(deptId)) {
                throw new Exception("不能将部门设置为其子部门的下级");
            }

            visited.add(currentParentId);
            Core_Dept parent = deptDao.getObjectById(currentParentId);
            currentParentId = parent != null ? parent.getParentId() : null;
        }
    }

    // 验证层级规则
    public void validateDepartmentLevel(Core_Dept parent, Integer level) throws Exception {

        Long parentId = parent.getId();
        // 车管所必须是顶级部门
        if (level == 1) {
            if (parentId != 0) {
                throw new Exception("车管所必须是顶级部门");
            }
            return;
        }

        // 分所的上级必须是车管所
        if (level == 2) {
            if (parent == null || parent.getLEVEL() != 1) {
                throw new Exception("分所必须直属车管所");
            }
            return;
        }

        // 普通部门的上级可以是车管所、分所或其他普通部门
        if (level == 0) {
            if (parent.getLEVEL() != 0 && parent.getLEVEL() != 1 && parent.getLEVEL() != 2) {
                throw new Exception("普通部门上级必须是车管所、分所或普通部门");
            }
            return;
        }

        throw new Exception("未知的部门级别");
    }

    /**
     * 批量删除被选中的部门，查询被选中的id 和所有的子id 全部删除
     */
    @Override
    @Transactional
    @DeleteCache(prefix ="ssoOracle:dept")
    public int[] batchDelete(List<Long> idList) {
        int[] count = new int[] { 0, 0 };
        // 查询所有部门id及子节点ids
        List<Map<String, Object>> deptChildrenIdList = deptDao.findChildren();
        for (Long id : idList) {
            for (Map<String, Object> map : deptChildrenIdList) {
                Long mapId = Long.parseLong(map.get("id").toString());
                if (mapId.equals(id)) {
                    if (map.get("childrenIdStr") != null) {
                        String childrenIdStr = map.get("childrenIdStr").toString();
                        String[] childrenIdArr = childrenIdStr.split(",");
                        List<Long> childrenIdList = new ArrayList<>();
                        for (String childId : childrenIdArr) {
                            childrenIdList.add(Long.parseLong(childId));
                        }
                        // 判断要删除的id列表是否包含子节点id列表
                        childrenIdList.removeAll(idList);
                        if (childrenIdList.size() > 0) {
                            count[1]++;
                            continue;
                        } else {
                            count[0] += deptDao.delete(id);
                        }
                    }
                }
            }
        }
        redisTemplate.delete("Redis_timeType_Dept_list");
        return count;
    }
    @Override
    public List<Core_Dept> getChildlist(Long deptId) {

//        return deptDao.getChildlist(deptId);
        return null;
    }

    @Override
    @Transactional
    @DeleteCache(prefix ="ssoOracle:dept")
    public void move(Long id, Long movetype) throws Exception {
        moveing(id, movetype);
    }

    public void /*List<Core_Dept>*/ moveing(Long id, Long movetype) throws Exception {
        if (movetype == 1) {//上移
            String own = getOwnOrderCode(id).getOrderCode();// 获取自身ordercode
            String min = getMiniOrderCode(id).getOrderCode();// 获取同一父类下最小的ordercode
            if (own.equals(min)) {
                throw new Exception("已经排在此分类第一了！");
            } else {
                Core_Dept ona = getOnAOrderCode(id);
                deptDao.moving(own, ona.getOrderCode(), id);
                deptDao.moving(ona.getOrderCode(), own, ona.getId());
            }
        } else {//下移
            String own = getOwnOrderCode(id).getOrderCode();
            String max = getMaxiOrderCode(id).getOrderCode();// 获取同一父类下最大的ordercode
            if (own.equals(max)) {
                throw new Exception("已经排在此分类最后了！");
            } else {
                Core_Dept next = getNextOrderCode(id);// 获取下一个ordercode
                deptDao.moving(own, next.getOrderCode(), id);
                deptDao.moving(next.getOrderCode(), own, next.getId());
            }
        }
    }

    @Override
    public Core_Dept getMaxiOrderCode(Long id) {
        return deptDao.getMaxiOrderCode(id);
    }

    @Override
    public Core_Dept getMiniOrderCode(Long id) {
        return deptDao.getMiniOrderCode(id);
    }

    @Override
    public Core_Dept getOwnOrderCode(Long id) {
        return deptDao.getOwnOrderCode(id);
    }

    @Override
    public Core_Dept getOnAOrderCode(Long id) {
        return deptDao.getOnAOrderCode(id);
    }

    @Override
    public Core_Dept getNextOrderCode(Long id) {
        return deptDao.getNextOrderCode(id);
    }

    @Override
    @Transactional
    @DeleteCache(prefix ="ssoOracle:dept")
    public void show(Long id, Long isShow) {
        //有子集全部更新
        if (deptDao.isHaveChildren(id) > 0) {
            deptDao.showAll(id, isShow);
        } else {
            deptDao.show(id, isShow);
        }
    }

    @Override
    public Long getDeptIdByDeptName(String parentDeptName) {
        return deptDao.getDeptIdByDeptName(parentDeptName);
    }

    @Override
    @FwyCache(prefix ="ssoOracle:dept:tree",timeType = "Redis_timeType_Dept")
    public List<Core_Dept> getTree(Long id) {
        return deptDao.getTree(id);
    }


    @Override
    @FwyCache(prefix ="ssoOracle:dept:deptTree",timeType = "Redis_timeType_Dept")
    public List<Core_Dept> getDeptTree(Core_Dept dept) {
        return deptDao.getDeptTree(dept);
    }

    @Override
    public String[] getAllDeptId(String code) {
        return deptDao.getAllDeptId(code);
    }

    @Override
    public Integer isChildNode(Long parentId) {
        return deptDao.isChildNode(parentId);
    }

    @Override
    public List<Core_Dept> syslist() {
        return deptDao.syslist();
    }

    @Override
    @FwyCache(prefix ="ssoOracle:dept:deptTreeDataPopUp",timeType = "Redis_timeType_Dept")
    public List<Core_Dept> deptTreeDataPopUp(String selectDeptId, Long deptId) {
        List<String> selectDeptIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(selectDeptId)) {
            selectDeptIdList = Arrays.asList(selectDeptId.split(","));
        } else {
            selectDeptIdList.add("");
        }
        List<Core_Dept> coreDeptList = deptDao.deptTreeDataPopUp(selectDeptIdList, deptId);
        List<Core_Dept> resultDeptList = getChildren(coreDeptList, 0L);
        return resultDeptList;
    }

    @Override
    public Core_Dept getObjectById(Long id) {
        return deptDao.getObjectById(id);
    }

    /**
     * 只删除一个部门
     */
    @Override
    @Transactional
    @DeleteCache(prefix ="ssoOracle:dept")
    public int deleteById(Long id) {
        if (deptDao.isHaveChlidren(id) > 0) {
            return -1;
        }
        int i=deptDao.delete(id);
        redisTemplate.delete("Redis_timeType_Dept_list");
        return i;
    }



    /**
     * description: 获取树状部门数据
     * version: 1.0
     * date: 2020/12/28 17:55
     * author: objcat
     *
     * @param deptList
     * @param parentId
     * @return
     */
    public List<Core_Dept> getChildren(List<Core_Dept> deptList, Long parentId) {
        List<Core_Dept> list = new ArrayList<Core_Dept>();
        for (Core_Dept dept : deptList) {
            if (dept.getParentId().equals(parentId)) {
                list.add(dept);
                dept.setChildren(getChildren(deptList, dept.getId()));
            }
        }
        return list;
    }

    /**
     * 通过deptCode查询code_dept
     * author: fanhuiwu
     * data 2021/4/13
     *
     * @param code
     */
    @Override
    public Core_Dept getObjectByCode(String code) {
        return deptDao.getObjectByCode(code);
    }
    @Override
    public List<Long> getLongChilds(Long deptId) {
        return  deptDao.getLongChilds(deptId);
    }

    @Override
    public List<Long> getBranchIds(Long id) {
        List<Core_Dept> list = new ArrayList<>();
        List<Long> listIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(id)){
            list=deptDao.getBranchList(id);
        }
//        if (ObjectUtils.isEmpty(list) || list.size()==0){
//            return listIds;
//        }
        listIds= list.stream().map(Core_Dept::getId).collect(Collectors.toList());
        listIds.add(id);
        return listIds;
    }

    @Override
    public List<Long> getCurrentBranchIds(Long deptId) {
        List<Core_Dept> list = new ArrayList<>();
        List<Long> listIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(deptId)){
            list=deptDao.getCurrentBranchIds(deptId);
        }
        if (ObjectUtils.isEmpty(list) || list.size()==0){
            return listIds;
        }
        listIds= list.stream().map(Core_Dept::getId).collect(Collectors.toList());
        listIds.add(deptId);
        return listIds;
    }

    @Override
    public List<Core_Dept> getDeptTreeById(Long id) {
        List<Core_Dept> list = deptDao.selectList(new QueryWrapper<>(new Core_Dept()));

        // 2. 构建部门映射和父子关系
        Map<Long, Core_Dept> deptMap = new HashMap<>();
        Map<Long, List<Core_Dept>> parentChildrenMap = new HashMap<>();

        for (Core_Dept dept : list) {
            deptMap.put(dept.getId(), dept);

            Long parentId = dept.getParentId() != null ? dept.getParentId() : 0L;
            parentChildrenMap.computeIfAbsent(parentId, k -> new ArrayList<>())
                    .add(dept);
        }

        // 3. 获取根部门
        Core_Dept root = deptMap.get(id);
        if (root == null) {
            throw new RuntimeException("部门不存在: " + id);
        }

        // 4. 构建树形结构
        buildTree(root, parentChildrenMap);

        // 5. 按排序码排序
        sortTree(root);

        List<Core_Dept>  result = new ArrayList<>();
        result.add(root);
        return result;
    }

    @Override
    public List<Long> getDeptList(Long id) {
        List<Long> subDeptIds = new ArrayList<>();
        findSubDeptIdsRecursive(id, subDeptIds);
        subDeptIds.add(id);
        return subDeptIds;
    }


    /**
     * 递归构建树形结构
     */
    private void buildTree(Core_Dept dept, Map<Long, List<Core_Dept>> parentChildrenMap) {
        List<Core_Dept> children = parentChildrenMap.get(dept.getId());
        if (children == null || children.isEmpty()) {
            return;
        }

        // 设置子部门
        dept.setChildren(children);

        // 递归构建子树
        for (Core_Dept child : children) {
            buildTree(child, parentChildrenMap);
        }
    }

    /**
     * 递归排序树节点
     */
    private void sortTree(Core_Dept dept) {
        if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
            // 按排序码排序
            dept.getChildren().sort(Comparator.comparing(Core_Dept::getOrderCode));

            // 递归排序子树
            for (Core_Dept child : dept.getChildren()) {
                sortTree(child);
            }
        }
    }

    private void findSubDeptIdsRecursive(Long parentId, List<Long> subDeptIds) {
        // 查询当前父部门的直接子部门ID列表
        List<Core_Dept> childDepts = deptDao.getBranchIds(parentId);
        List<Long> childIds = childDepts.stream().map(Core_Dept::getId).collect(Collectors.toList());
        for (Long childId : childIds) {
            subDeptIds.add(childId);           // 添加子部门ID
            findSubDeptIdsRecursive(childId, subDeptIds); // 递归查询孙部门
        }
    }
}