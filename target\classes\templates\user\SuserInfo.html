<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>用户信息</title>
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
    <link rel="stylesheet"
          th:href="@{/plugins/formSelects/formSelects-v4.css}"/>
    <style>

        .layui-form-label {
            width: 150px;
        }

        .layui-input-inline {
            width: 250px;
        }

        .layui-input {
            width: 240px;
        }
    </style>
</head>
<script th:inline="javascript">
    $(function () {
        $("#password").val('');

        var yhyxqsStr = [[${yhyxqs}]];
        var yhyxqStr = [[${yhyxq}]];
        var mmyxqsStr = [[${mmyxqs}]];
        var mmyxqStr = [[${mmyxq}]];

        if (yhyxqsStr == null || yhyxqsStr == '') {
            // 用户有效期始默认当天
            var date = new Date();
            $("#yhyxqs").val(formatDate(date));
        }

        if (yhyxqStr == null || yhyxqStr == '') {
            // 用户有效期止默认30天
            var date = new Date();
            date.setDate(date.getDate() + 30);
            $("#yhyxq").val(formatDate(date));
        }

        if (mmyxqsStr == null || mmyxqsStr == '') {
            // 密码有效期始默认当天
            var date = new Date();
            $("#mmyxqs").val(formatDate(date));
        }

        if (mmyxqStr == null || mmyxqStr == '') {
            // 密码有效期止默认15天
            var date = new Date();
            date.setDate(date.getDate() + 15);
            $("#mmyxq").val(formatDate(date));
        }


    })

    function formatDate(date) {
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        return y + '-' + m + '-' + d;
    };
</script>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title"
                      style="margin-top: 20px;">
                <legend>用户信息</legend>
            </fieldset>

            <form class="layui-form" action="">
                <input type="hidden" name="id" id="id" th:value=${user?.id}>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>用户名：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="userName" lay-verify="required|unique" th:if="${user==null}"
                                   placeholder="请输入用户名" autocomplete="off" th:value="${user?.userName}"
                                   class="layui-input">
                            <input type="text" name="userName" lay-verify="required|unique" th:if="${user!=null}"
                                   placeholder="请输入用户名" autocomplete="off" th:value="${user?.userName}"
                                   class="layui-input" readonly>
                        </div>
                    </div>
                    <div class="layui-inline" th:if="${user==null}">
                        <label class="layui-form-label"><span style="color:red">*</span>密码：</label>
                        <div class="layui-input-inline">
                            <input type="password" name="password" id="password" lay-verify="required|passwordLength"
                                   class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>民警姓名：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="fullName" lay-verify="required|fullnameLength"
                                   placeholder="请输入民警姓名" autocomplete="off" th:value="${user?.fullName}"
                                   class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>身份证号：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="idCardNumber" id="idCardNumber" lay-verify="required|identity|uniqueCard"
                                   placeholder="请输入身份证号" autocomplete="off" th:value="${user?.idCardNumber}"
                                   class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">


                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>警员编号(员工编号)：</label>
                        <div class="layui-input-inline">
                            <input type="text" id="jybh" name="jybh" lay-verify="required|uniqueJybh"
                                   placeholder="请输入警员编号(员工编号)" autocomplete="off" th:value="${Suser?.jybh}"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>角色权限：</label>
                        <div class="layui-input-inline" style="width: 240px">
                            <select name="roleIds" xm-select="role_select" xm-select-radio="" xm-select-search=""
                                    lay-vertype="tips" lay-verify="required">
                            </select>
                            <input type="hidden" th:value="${roleIds}" id="roleIds">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>用户有效期始：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="yhyxqsStr" id="yhyxqs" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(Suser?.yhyxqs, 'yyyy-MM-dd')}"
                                       class="layui-input">
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>用户有效期止：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="yhyxqStr" id="yhyxq" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(Suser?.yhyxq, 'yyyy-MM-dd')}"
                                       class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>密码有效期始：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="mmyxqsStr" id="mmyxqs" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(Suser?.mmyxqs, 'yyyy-MM-dd')}"
                                       class="layui-input">
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>密码有效期止：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="mmyxqStr" id="mmyxq" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(Suser?.mmyxq, 'yyyy-MM-dd')}"
                                       class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>联系电话：</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="phone" lay-verify="required|phone" placeholder="请输入电话号码"
                                   th:value="${user?.phone}" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="margin-left: 0px"><span
                                style="color:red">*</span>所属部门：</label>
                        <div class="layui-input-inline" style="width: 240px">
                            <select name="deptId" xm-select="deptId" xm-select-radio="" xm-select-search=""
                                    lay-vertype="tips" lay-verify="required">
                            </select>
                            <input type="hidden" id="deptId" th:value=${user?.deptId}>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>允许登录IP：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="allowip" lay-verify="required|validip"
                                   placeholder="请输入允许登录IP" autocomplete="off" th:value="${Suser?.allowip}"
                                   class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>创建用户：</label>
                        <div class="layui-input-inline">
                            <input type="text" placeholder="" autocomplete="off" th:value="${userName}"
                                   class="layui-input" disabled="disabled">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">是否警员:</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" value="1" th:attr=" checked=${Suser?.sfjy == '1' ? true : false}"
                                   name="sfjy" id="sfjy" lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">启用状态:</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" value="true"
                                   th:attr="checked=${user?.isStart == true ? true : false}" name="isStart" id="isStart"
                                   lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline" th:if="${session.user != null &&session.user.isSys}">
                        <label class="layui-form-label">是否系统级:</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" value="true" th:attr="checked=${user?.isSys == true ? true : false}"
                                   name="isSys" id="isSys" lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item" style="text-align: center">
                    <button class="layui-btn" lay-submit="" lay-filter="demo1">保存</button>
                    <button class="layui-btn layui-btn-primary" id="cancelBtn">返回</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var formSelects = layui.formSelects;
    layui.use(['form', 'layedit', 'laydate', 'jquery'],
        function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laydate = layui.laydate,
                $ = jQuery = layui.$;

            // 回到列表页面
            $('#cancelBtn').click(function () {
                window.parent.changTabs(ctx + 'userController/userList', '', '用户列表');
            })
            formSelects.data('deptId', 'server', {
                data: {"deptId": $("#deptId").val()},
                keyVal: 'ID',
                keyName: 'deptName',
                direction: 'auto',
                url: ctx + 'deptController/tree',
                beforeSuccess: function (id, url, searchVal, result) {
                    result = result.data;
                    return result;
                }
            }).on('deptId', function (id, vals, val, isAdd, isDisabled) {
                $("#deptId").val(val.ID);
            }, true);

            formSelects.data('role_select', 'server', {
                data: {"roleIds": $("#roleIds").val()},
                keyVal: 'id',
                keyName: 'roleName',
                direction: 'auto',
                url: ctx + 'roleController/roleDialogJsonX',
                beforeSuccess: function (id, url, searchVal, result) {
                    result = result.data;
                    return result;
                }
            });


            //日期
            laydate.render({
                elem: '#yhyxqs'
            });
            laydate.render({
                elem: '#yhyxq'
            });
            laydate.render({
                elem: '#mmyxqs'
            });
            laydate.render({
                elem: '#mmyxq'
            });

            form.on('switch(statusFilter)', function (data) {
                var checked = data.elem.checked;
                if (checked) {
                    $("#isStart").val(true);
                } else {
                    $("#isStart").val(false);
                }
                form.render('switch');//重新渲染下拉框
            });


            form.render();

            form.verify({
                uniqueCard: function (value) {
                    var id = $("#id").val();
                    var checkMsg = '';
                    var url = ctx + "userController/uniqueIdCard?idCardNumber=" + value + "&id=" + id;
                    $.ajax({
                        url: url,
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            if (result) {
                                checkMsg += '身份证重复';
                                return checkMsg;
                            }
                        }, error: function () {
                            layer.msg("身份证验证失败");
                        }
                    });
                    if (checkMsg != '') {
                        return checkMsg;
                    }
                },
                uniqueJybh: function (value) {
                    var orginJybh = [[${Suser==null?'-1':Suser?.jybh}]];
                    var jybh = $("#jybh").val();
                    if(orginJybh != jybh){
                        var id = $("#id").val();
                        var checkMsg = '';
                        var url = ctx + "userController/uniqueJybh?jybh=" + value + "&id=" + id;
                        $.ajax({
                            url: url,
                            datatype: 'json',
                            async: false,
                            success: function (result) {
                                if (result) {
                                    checkMsg += '警员编号重复';
                                    return checkMsg;
                                }
                            }, error: function () {
                                layer.msg("警员编号验证失败");
                            }
                        });
                        if (checkMsg != '') {
                            return checkMsg;
                        }
                    }
                },
                identity: function (value, item) {
                    var aCity = {
                        11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林',
                        23: '黑龙江', 31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西',
                        37: '山东', 41: '河南', 42: '湖北', 43: '湖南', 44: '广东', 45: '广西', 46: '海南',
                        50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏', 61: '陕西', 62: '甘肃',
                        63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门',
                        91: '国外'
                    };
                    var sId = value;
                    var iSum = 0;
                    var info = '';
                    if (sId.length == 15) {
                        if (!/^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$/.test(sId))
                            return "身份证校验不合法";
                        else {
                            if (aCity[parseInt(sId.substr(0, 2), 10)] == null)
                                return "身份证校验不合法";
                        }
                    }
                    if (!/^\d{17}(\d|x)$/i.test(sId))
                        return "身份证校验不合法";
                    sId = sId.replace(/x$/i, "a");
                    if (aCity[parseInt(sId.substr(0, 2), 10)] == null)
                        return "身份证校验不合法";
                    var sBirthday = sId.substr(6, 4) + "/" + Number(sId.substr(10, 2)) + "/"
                        + Number(sId.substr(12, 2));
                    var d = new Date(sBirthday);
                    if (sBirthday != (d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d
                        .getDate()))
                        return false;
                    for (var i = 17; i >= 0; i--)
                        iSum += (Math.pow(2, i) % 11) * parseInt(sId.charAt(17 - i), 11)
                    if (iSum % 11 != 1)
                        return "身份证校验不合法";
                },
                validip: function (value, item) {
                    var reg = /^((0[0-9]|1[0-9]\d{1,2})|(2[0-5][0-5])|(2[0-4][0-9])|(\d{1,2}))\.((0[0-9]|1[0-9]\d{1,2})|(2[0-5][0-5])|(2[0-4][0-9])|(\d{1,2}))\.((0[0-9]|1[0-9]\d{1,2})|(2[0-4][0-9])|(2[0-5][0-5])|(\d{1,2}))\.((0[0-9]|1[0-9]\d{1,2})|(2[0-4][0-9])|(2[0-5][0-5])|(\d{1,2}))$/;

                    if (!reg.test(value)) {
                        return "客户端IP校验不通过";
                    }
                },
                unique: function (value, item) {
                    var id = $("#id").val();
                    var checkMsg = '';
                    var url = ctx + "userController/uniqueData?userName=" + value + "&id=" + id;
                    $.ajax({
                        url: url,
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            if (result) {
                                checkMsg += '登录账户重复';
                                return checkMsg;
                            }
                        }, error: function () {
                            layer.msg("登录账户验证失败");
                        }
                    });
                    if (checkMsg != '') {
                        return checkMsg;
                    }
                    if (value.length < 3) {
                        return "登录账户长度3到32个字符";
                    }
                    if (value.length > 32) {
                        return "登录账户长度3到32个字符";
                    }
                },
                passwordLength: function (value) {
                    // 判断密码长度不低于8位
                    if (!/\S{8,}/.test(value)) {
                        //console.log("密码长度不低于8位");
                        return "密码长度不低于8位";
                    }
                    // 判断密码是否包含数字
                    if (!/.*\d+.*/.test(value)) {
                        //console.log("密码必须包含数字");
                        return "密码必须包含数字";
                    }
                    // 判断密码是否包含字母
                    if (!/.*[a-zA-Z]+.*/.test(value)) {
                        //console.log("密码必须包含字母");
                        return "密码必须包含字母";
                    }
                    // 判断密码是否包含特殊符号
                    if (!/.*[~!@#$%^&*()_+|<>,.?/:;'\[\]{}\"]+.*/.test(value)) {
                        //console.log("密码必须包含特殊符号");
                        return "密码必须包含特殊符号";
                    }
                },
                fullnameLength: function (value) {
                    if (value.length < 2) {
                        return "用户姓名长度2到20个字符";
                    }
                    if (value.length > 20) {
                        return "用户姓名长度2到20个字符";
                    }
                }
            });
            form.on('submit(demo1)', function (data) {
            	
            	var sfjy = $("#sfjy").val();
            	//alert(sfjy);
				if(sfjy == '0'){
					//角色校验
					var roleIds = [];
					$(".xm-select-parent[fs_id='role_select']").find("span[fsw='xm-select']").each(function(){
						var v = $(this).attr("value");
						roleIds.push(v);
					});
					
					$.ajax({
						type : "POST",
						url : ctx + "roleController/checkNoPoliceRole",
						data : {"noPolice":"0","roleIds":roleIds},
						dataType : "json",
						success : function(data2) {
							if (data2.code == '0') {
								var flag = data2.data.flag;
								var conflictInfo = data2.data.conflictInfo;
								if(flag == false){
									layer.msg(conflictInfo, {icon : 5,skin : 'layer-ext-moon'});
									return ;
								}
								if(flag == true){
									var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time: 0});
						            $.ajax({
						                type: "POST",
						                url: "saveSUser",
						                data: data.field,
						                dataType: "json",
						                success: function (data3) {
						                    if (data.code == '0') {
						                        layer.alert(data3.msg, {
						                                icon: 6, skin: 'layer-ext-moon', closeBtn: 0
						                            },
						                            function () {
						                                window.parent.changTabs(ctx + 'userController/userList', '', '用户列表');
						                            });
						                    } else {
						                        layer.msg(data3.msg, {icon: 5, skin: 'layer-ext-moon'});
						                    }
						                    layer.close(loading);
						                },
						                error: function (data3) {
						                    layer.close(loading);
						                    layer.msg(data.msg, {icon: 5, skin: 'layer-ext-moon'});
						                }
						            });
									return false; //防止提交两次表单
								}
							} else {
								 layer.msg(data.msg, {icon : 5,skin : 'layer-ext-moon'}); 
							}
							layer.close(loading);
						},
						error : function(data2) {
						   layer.close(loading);
						   layer.msg(data.msg, {icon : 5,skin : 'layer-ext-moon'});
						}
					});
				}else{
					var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time: 0});
		            $.ajax({
		                type: "POST",
		                url: "saveSUser",
		                data: data.field,
		                dataType: "json",
		                success: function (data) {
		                    if (data.code == '0') {
		                        layer.alert(data.msg, {
		                                icon: 6, skin: 'layer-ext-moon', closeBtn: 0
		                            },
		                            function () {
		                                window.parent.changTabs(ctx + 'userController/userList', '', '用户列表');
		                            });
		                    } else {
		                        layer.msg(data.msg, {icon: 5, skin: 'layer-ext-moon'});
		                    }
		                    layer.close(loading);
		                },
		                error: function (data) {
		                    layer.close(loading);
		                    layer.msg(data.msg, {icon: 5, skin: 'layer-ext-moon'});
		                }
		            });
					return false; //防止提交两次表单
				}
            });
        });
    
    	
</script>
</body>
</html>