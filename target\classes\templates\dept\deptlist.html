<!DOCTYPE html >
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="UTF-8">
    <title></title>
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>
<div class="layui-fluid">
    <div class="layui-card">

        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <input type="hidden" id="topValue" th:value="${top_value}">
                <div class="layui-inline">
                    <label class="layui-form-label w-auto" style="width: unset;padding: 9px 5px 9px 5px;">部门：</label>
                    <div class="layui-input-inline mr0">
                        <input class="layui-input" id="searchValue" autocomplete="off" placeholder="请输入关键字">
                    </div>
                </div>
                <div class="layui-inline" >
                    <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                    <button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</button>
                    <button class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
                </div>
            </blockquote>
            <script type="text/html" id="toolbarDemo">
                <a style="margin-right:5px;" href="javascript:newTab('deptController/addinfo','添加信息')">
                    <button class="layui-btn layui-btn-sm">
                        <i class="layui-icon layui-icon-add"></i>增加
                    </button>
                </a>
                <!--			<a style="margin-right:5px;" href="javascript:void(0);" id="editinfo">-->
                <!--				<button class="layui-btn layui-btn-sm  layui-btn-normal">-->
                <!--					<i class="layui-icon layui-icon-edit"></i>编辑-->
                <!--				</button>-->
                <!--			</a>-->
                <!--			<a href="javascript:void(0);" id="deleteinfo">-->
                <!--				<button class="layui-btn layui-btn-danger layui-btn-sm">-->
                <!--					<i class="layui-icon layui-icon-delete"></i>删除-->
                <!--				</button>-->
                <!--			</a>-->
            </script>

            <table class="layui-table layui-form treeTable" id="tree-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    //回车搜索
    $("blockquote").on('keyup','input,select',function (e) {
        if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#search_btn").trigger("click");//触发搜索按钮的点击事件
        }
    });

    layui.config({
        base:ctx + '/plugins/',
    }).extend({
        treetable: 'treetable-lay/treetable'
    }).use(['treetable','layer','code','form','jquery'],function(){
        var o = layui.$,
            form = layui.form,
            layer = layui.layer,
            treeTable = layui.treetable, $ = layui.$;
        var	re = treeTable.render({
            treeColIndex: 1,
            elem: '#tree-table',
            defaultToolbar: [],
            url: ctx + '/deptController/deptJson',
            treeIdName: 'ID',
            treePidName: 'parentID',
            // treeDefaultClose:true, 是否全部关闭 默认是全部展开的 目前部门数据不多可以全部展开
            treeLinkage: false,
            toolbar: '#toolbarDemo',
            treeSpid: $("#topValue").val(),
            end: function(e){
                form.render();
            },
            cols: [
                [
                    {type:'checkbox',fixed:'left'},
                    {
                        field: 'deptName',
                        title: '名称',
                        width: '300'
                    },
                    {
                        field: 'deptCode',
                        title: '部门编号',
                        width: '150',
                        align: 'center'
                    },
                    {
                        field: 'orderCode',
                        title: '排序码',
                        width: '200',
                        align: 'center'
                    },
                    {
                        field: 'parentName',
                        title: '部门类型',
                        width: '120',
                        align: 'center'
                    },
                    {
                        title: '是否启用',
                        width: '100',
                        align: 'center',
                        templet: function(item){
                            if(item.isShow == 1){
                                return '<input type="checkbox" value='+item.id+' checked="" name="open" lay-skin="switch" lay-filter="switchTest" lay-text="启用|禁用">';
                            }else{
                                return '<input type="checkbox" value='+item.id+' name="close" lay-skin="switch" lay-text="启用|禁用" lay-filter="switchTest">';
                            }
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        width: '320',fixed: 'right',
                        templet: function(item){
                            return '<a class="layui-btn layui-btn-normal layui-btn-xs" onclick="editInfo('+item.id+')">编辑</a><a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delInfo('+item.id+')">删除</a>';
                        }
                    }
                ]
            ]
        });
        form.on('switch(switchTest)', function(data){
            var loading = layer.msg('正在更新', {icon: 16, shade: 0.1, time:0});
            var id = data.value;
            var isShow =this.checked ? '1' : '0';
            $.ajax({
                url : 'show',
                type : "POST",
                async : true,
                cache : false,
                data : {
                    "id" : id,
                    "isShow" : isShow
                },
                success : function(result) {
                    if(result.code==0){
                        layer.closeAll('loading');
                        layer.msg("更新成功!",{icon: 6,skin: 'layer-ext-moon' });
                        location.reload();
                    }else{
                        layer.closeAll('loading');
                        layer.msg("更新失败!",{icon: 5,skin: 'layer-ext-moon' });
                        /* if(isShow==0){
                            data.elem.checked=false;
                        }else{
                            data.elem.checked=true;
                        } */
                    }
                },
                error : function(result) {
                    layer.closeAll('loading');
                    layer.msg("更新失败!",{icon: 5,skin: 'layer-ext-moon' });
                    /* if(isShow==0){
                        data.elem.checked=false;
                    }else{
                        data.elem.checked=true;
                    } */
                }
            });
        });
        //搜索
        $("#search_btn").click(function(){
            var keyword = $('#searchValue').val();
            var searchCount = 0;
            $('#tree-table').next('.treeTable').find('.layui-table-body tbody tr td').each(function () {
                $(this).css('background-color', 'transparent');
                var text = $(this).text();
                if (keyword != '' && text.indexOf(keyword) >= 0) {
                    $(this).css('background-color', 'rgba(250,230,160,0.5)');
                    if (searchCount == 0) {
                        treeTable.expandAll('#tree-table');
                        $('html,body').stop(true);
                        $('html,body').animate({scrollTop: $(this).offset().top - 150}, 500);
                    }
                    searchCount++;
                }
            });
            if (keyword == '') {
                layer.msg("请输入搜索内容", {icon: 5});
            } else if (searchCount == 0) {
                layer.msg("没有匹配结果", {icon: 5});
            }
        });
        $("#unset_Btn").click(function(){
            location.reload();
        })
        o('#deleteinfo').click(function(){
            var ids = treeTable.checked(re).join(',');
            if(ids.length>0){
                layer.confirm('是否确定删除所选信息？', {
                    icon: 3,
                    btn: ['确定','取消'] //按钮
                }, function(){
                    var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
                    $.ajax({
                        url :'deleteDept',
                        type : "POST",
                        async : true,
                        cache : false,
                        data : {
                            "ids" : ids
                        },
                        success : function(data) {
                            if(data.code==0){
                                layer.closeAll('loading');
                                layer.msg(data.msg,{icon: 6,skin: 'layer-ext-moon' });
                                location.reload();
                            }else{
                                layer.closeAll('loading');
                                layer.msg(data.msg,{icon: 5,skin: 'layer-ext-moon' });
                            }
                        },
                        error : function(data) {
                            layer.closeAll('loading');
                            layer.msg("删除失败!",{icon: 5,skin: 'layer-ext-moon' });
                        }
                    });
                }, function(){

                });
            }else{
                layer.msg("请至少选择一行要删除的数据！");
            }
        })
        o('#editinfo').click(function(){
            var ids = treeTable.checked(re).join(',');
            var idlen = ids.split(",");
            if(idlen.length>1){
                layer.msg("请最多选择一行要修改的数据！");
            }else{
                if(idlen.length==1&&idlen==''){
                    layer.msg("请至少选择一行要修改的数据！");
                }else{
                    newTab('deptController/editinfo?deptId='+ids, '修改信息');
                }
            }

        });



        // 刷新重载树表（一般在异步处理数据后刷新显示）
        o('.refresh').click(function(){
            //re.data.push({"id":50,"pid":0,"title":"1-4"},{"id":51,"pid":50,"title":"1-4-1"});
            location.reload();
        })
        o('#tree1').on('click','[data-down]',function(){
            o(this).find('span').length && o(this).parents('.layui-unselect').find('input').val(o(this).text());
        })
        o('.layui-select-title').click(function(){
            o(this).parent().hasClass('layui-form-selected') ? o(this).next().hide() : o(this).next().show(),o(this).parent().toggleClass('layui-form-selected');
        })
        o(document).on("click", function(i) {
            !o(i.target).parent().hasClass('layui-select-title') && !o(i.target).parents('table').length && !(!o(i.target).parents('table').length && o(i.target).hasClass('layui-icon')) && o(".layui-form-select").removeClass("layui-form-selected").find('.layui-anim').hide();
        })
        window.delInfo = function delInfo(id){
            layer.confirm('是否确定删除所选信息？', {
                icon: 3,
                btn: ['确定','取消'] //按钮
            }, function(){
                var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
                $.ajax({
                    url : 'deleteDept',
                    type : "POST",
                    async : true,
                    cache : false,
                    data : {
                        "ids" : id
                    },
                    success : function(data) {
                        if(data.code==0){
                            layer.closeAll('loading');
                            layer.msg(data.msg,{icon: 6,skin: 'layer-ext-moon' });
                            location.reload();
                        }else{
                            layer.closeAll('loading');
                            layer.msg(data.msg,{icon: 5,skin: 'layer-ext-moon' });
                        }
                    },
                    error : function(data) {
                        layer.closeAll('loading');
                        layer.msg("删除失败!",{icon: 5,skin: 'layer-ext-moon' });
                    }
                });
            }, function(){

            });
        }
        window.editInfo = function editInfo(id){
            newTab('deptController/editinfo?deptId='+id, '修改信息');
        }

        window.createStaff = function createStaff(id){
            var url=ctx + '/deptController/createQrCode?deptId='+id;
            $.ajax({
                url : url,
                type : "get",
                async : true,
                cache : false,
                success : function(data) {
                    if(data.code==0){
                        layer.open({
                            type: 1,
                            title:'邀请注册',
                            content: "<img src="+"'" +data.data+"'" +" style='margin: 30px'>" //注意，如果str是object，那么需要字符拼接。
                        });
                    }else{
                        layer.closeAll('loading');
                        layer.msg(data.msg,{icon: 5,skin: 'layer-ext-moon' });

                    }
                },
                error : function(data) {
                    layer.closeAll('loading');
                    layer.msg(data.msg,{icon: 5,skin: 'layer-ext-moon' });
                }
            });

        }
        window.createParams = function createParams(id){
            var submitButId='#subBtn';
            var url = ctx + 'deptController/deptParams?deptId=' + id,
                title = '编辑部门参数';
            xadmin.openWindowFull(url, title,submitButId);


        }
        window.moveinfo = function moveinfo(id,movetype){
            var loading = layer.msg('正在移动', {icon: 16, shade: 0.3, time:0});
            $.ajax({
                url : 'moveDept',
                type : "POST",
                async : true,
                cache : false,
                data : {
                    "id" : id,
                    "movetype" : movetype
                },
                success : function(data) {
                    if(data.code==0){
                        layer.closeAll('loading');
                        layer.msg(data.msg,{icon: 6,skin: 'layer-ext-moon' });
                        location.reload();
                    }else{
                        layer.closeAll('loading');
                        layer.msg(data.msg,{icon: 5,skin: 'layer-ext-moon' });

                    }
                },
                error : function(data) {
                    layer.closeAll('loading');
                    layer.msg(data.msg,{icon: 5,skin: 'layer-ext-moon' });
                }
            });
        }
    })

    //打开新标签页
    function newTab(url,tit){
        if(top.layui.index){
            top.layui.index.openTabsPage(url,tit)
            //parent.layui.index.openTabsPage(url, tit);
        }else{
            window.open(url)
        }
    }

</script>
</body>
</html>
