<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>自助机类型管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script type="text/javascript" th:src="@{/scripts/dic/machineType/list.js}"></script>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" action="">
                <div >
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto" style="width: unset;padding: 9px 1px 9px 0px;">类型名称：</label>
                        <div class="layui-input-inline mr0">
                            <input id="dicName" class="layui-input" type="text" placeholder="请输入自助机类型名称">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                        <button type="reset" class="layui-btn layui-btn-primary" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                    </div>
                </div>
            </form>
            <table class="layui-hide" id="machineType-table" lay-filter="machineType-table"></table>
            <script type="text/html" id="table-toolbar-top">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe608;</i>增加</button>
                    <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="batchDel"><i class="layui-icon">&#xe640;</i>删除</button>
                </div>
            </script>

            <script type="text/html" id="table-toolbar">
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
            </script>
        </div>
    </div>
</div>

</body>
</html>