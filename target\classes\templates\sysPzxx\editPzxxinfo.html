<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	  xmlns:http="http://www.w3.org/1999/xhtml"
	  xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
	<meta charset="utf-8">
	<title>策略信息</title>
	<div th:replace="Importfile::html"></div>
	<script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
	<link rel="stylesheet"
		  th:href="@{/plugins/formSelects/formSelects-v4.css}" />
	<script th:src="@{/scripts/security/main/openWay.js}"></script>
	<style>

		.layui-form-label{
			width:200px;
		}

		.layui-input-block {
			margin-left: 230px;
			min-height: 36px
		}

		 /*不显示秒*/
		            .layui-laydate-content>.layui-laydate-list {
			                padding-bottom: 0px;
			                overflow: hidden;
			            }
		            .layui-laydate-content>.layui-laydate-list>li{
			                width:50%
		            }
		 
		            .merge-box .scrollbox .merge-list {
			                padding-bottom: 5px;
			            }

	</style>
</head>


<form class="layui-form" style="padding:30px;" lay-filter="pzxxFormFilter" >
	<div class="layui-form-item" >
		<div class="layui-row ">

			<label class="layui-form-label" ><span style="color:red;">*</span>是否启用：</label>
			<div class="layui-input-inline">
				<input th:if="${pzxx == null }" type="checkbox" value="0"  id="status" name="status" lay-filter="statusFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.status == 1}" id="status" type="checkbox" value="1" checked="true" name="status" lay-filter="statusFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.status == 0}" id="status" type="checkbox" value="0"   name="status" lay-filter="statusFilter" lay-skin="switch" lay-text="是|否">
			</div>
		</div>
	</div>
    <div class="layui-form-item" >
        <div class="layui-row ">

            <label class="layui-form-label" ><span style="color:red;">*</span>是否开启ip校验：</label>
            <div class="layui-input-inline">
                <input th:if="${pzxx == null }" type="checkbox" value="0"  id="isIpCheck" name="isIpCheck" lay-filter="isIpCheckFilter" lay-skin="switch" lay-text="是|否">
                <input th:if="${pzxx != null && pzxx.isIpCheck == 1}" id="isIpCheck" type="checkbox" value="1" checked="true" name="isIpCheck" lay-filter="isIpCheckFilter" lay-skin="switch" lay-text="是|否">
                <input th:if="${pzxx != null && pzxx.isIpCheck == 0}" id="isIpCheck" type="checkbox" value="0"   name="isIpCheck" lay-filter="isIpCheckFilter" lay-skin="switch" lay-text="是|否">
            </div>
        </div>
    </div>
	<div class="layui-form-item layui-row" >
		<div class=" layui-col-md6">
			<label class="layui-form-label " ><span style="color:red;">*</span>是否启用IP锁定阈值：</label>
			<div class="layui-input-inline">
				<input th:if="${pzxx == null }" type="checkbox" value="0"  id="isIpLock" name="isIpLock" lay-filter="isIpLockFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isIpLock == 1}" id="isIpLock" type="checkbox" value="1" checked="true" name="isIpLock" lay-filter="isIpLockFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isIpLock == 0}" id="isIpLock" type="checkbox" value="0"   name="isIpLock" lay-filter="isIpLockFilter" lay-skin="switch" lay-text="是|否">
			</div>
		</div>
		<div class="layui-col-md6">
				<label class="layui-form-label"><span style="color:red">*</span>IP锁定阈值：</label>
				<div class=" layui-input-inline" >
					<input type="hidden" th:if="${viewType != 'add'}" name="bh" id="bh" th:value="${pzxx.bh}">
					<input th:if="${viewType == 'add'}" type="text" name="ip_fail_count" class="layui-input ip_fail_count" lay-verify="required|verifyNum">
					<input th:if="${viewType == 'edit'}" type="text" name="ip_fail_count" class="layui-input ip_fail_count" lay-verify="required|verifyNum" th:value="${pzxx.ip_fail_count}">
					<input th:if="${viewType == 'detail'}" type="text" name="ip_fail_count" disabled="disabled" class="layui-input layui-disabled ip_fail_count" lay-verify="required" th:value="${pzxx.ip_fail_count}">
				</div>
		</div>
	</div>
	<div class="layui-form-item layui-row ">
		<div class="layui-col-md6" >
			<label class="layui-form-label" ><span style="color:red;">*</span>是否启用密码错误阈值：</label>
			<div class="layui-input-inline">
				<input th:if="${pzxx == null }" type="checkbox" value="0"  id="isMmErr" name="isMmErr" lay-filter="isMmErrFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isMmErr == 1}" id="isMmErr" type="checkbox" value="1" checked="true" name="isMmErr" lay-filter="isMmErrFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isMmErr == 0}" id="isMmErr" type="checkbox" value="0"   name="isMmErr" lay-filter="isMmErrFilter" lay-skin="switch" lay-text="是|否">
			</div>
		</div>

		<div class=" layui-col-md6">
				<label class="layui-form-label "><span style="color:red">*</span>密码错误阈值：</label>
				<div class=" layui-input-inline " >
					<input th:if="${viewType == 'add'}" type="text" name="mm_fail_count" class="layui-input mm_fail_count" lay-verify="required|verifyNum">
					<input th:if="${viewType == 'edit'}" type="text" name="mm_fail_count" class="layui-input mm_fail_count" lay-verify="required|verifyNum" th:value="${pzxx.mm_fail_count}">
					<input th:if="${viewType == 'detail'}" type="text" name="mm_fail_count" disabled="disabled" class="layui-input layui-disabled mm_fail_count" lay-verify="required" th:value="${pzxx.mm_fail_count}">
				</div>
		</div>
	</div>

	<div class="layui-form-item layui-row ">
		<div class="layui-col-md6" >
			<label class="layui-form-label" ><span style="color:red;">*</span>是否启用长期未登录配置：</label>
			<div class="layui-input-inline">
				<input th:if="${pzxx == null }" type="checkbox" value="0"  id="isCqwdlPz" name="isCqwdlPz" lay-filter="isCqwdlPzFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isCqwdlPz == 1}" id="isCqwdlPz" type="checkbox" value="1" checked="true" name="isCqwdlPz" lay-filter="isCqwdlPzFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isCqwdlPz == 0}" id="isCqwdlPz" type="checkbox" value="0"   name="isCqwdlPz" lay-filter="isCqwdlPzFilter" lay-skin="switch" lay-text="是|否">
			</div>
		</div>
		<div class=" layui-col-md6">
				<label class="layui-form-label "><span style="color:red">*</span>长期未登录时间（天）：</label>
				<div class=" layui-input-inline " >
					<input th:if="${viewType == 'add'}" type="text" name="cqwdrsj" class="layui-input cqwdrsj" lay-verify="required|verifyNum">
					<input th:if="${viewType == 'edit'}" type="text" name="cqwdrsj" class="layui-input cqwdrsj" lay-verify="required|verifyNum" th:value="${pzxx.cqwdrsj}">
					<input th:if="${viewType == 'detail'}" type="text" name="cqwdrsj" disabled="disabled" class="layui-input layui-disabled cqwdrsj" lay-verify="required" th:value="${pzxx.cqwdrsj}">
				</div>
		</div>
	</div>
	<div class="layui-form-item layui-row ">
		<div class="layui-col-md6" >
			<label class="layui-form-label" ><span style="color:red;">*</span>是否启用高频访问配置：</label>
			<div class="layui-input-inline">
				<input th:if="${pzxx == null }" type="checkbox" value="0"  id="isGpfwPz" name="isGpfwPz" lay-filter="isGpfwPzFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isGpfwPz == 1}" id="isGpfwPz" type="checkbox" value="1" checked="true" name="isGpfwPz" lay-filter="isGpfwPzFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isGpfwPz == 0}" id="isGpfwPz" type="checkbox" value="0"   name="isGpfwPz" lay-filter="isGpfwPzFilter" lay-skin="switch" lay-text="是|否">
			</div>
		</div>

		<div class=" layui-col-md6" >
				<label class="layui-form-label "><span style="color:red">*</span>高频访问:    每：</label>
				<div class="layui-input-inline">
					<input style="width:40px;display: inline;" class="layui-input fwsj" id="fwsj" name="fwsj" type="text" th:value="${pzxx.fwsj }" lay-verify="required|verifyNum" />分钟访问：
					<input style="width:40px;display: inline;" class="layui-input fwcs"  id="fwcs" name="fwcs" type="text" th:value="${pzxx.fwcs }" lay-verify="required|verifyNum"/>次
				</div>
		</div>
	</div>
	<div class="layui-row ">
		<div class="layui-form-item" >
			<label class="layui-form-label" ><span style="color:red;">*</span>是否启用时间限制：</label>
			<div class="layui-input-inline">
				<input th:if="${pzxx == null }" type="checkbox" value="0"  id="isTime" name="isTime" lay-filter="isTimeFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isTime == 1}" id="isTime" type="checkbox" value="1" checked="true" name="isTime" lay-filter="isTimeFilter" lay-skin="switch" lay-text="是|否">
				<input th:if="${pzxx != null && pzxx.isTime == 0}" id="isTime" type="checkbox" value="0"   name="isTime" lay-filter="isTimeFilter" lay-skin="switch" lay-text="是|否">
			</div>
		</div>
	</div>
	<div class="layui-row ">
		<div class="layui-form-item layui-col-md6" style="clear:none;">
			<div class="layui-inline layui-form-high" >
				<label class="layui-form-label label-width"><span style="color:red;">*</span>系统登入时间【上午】开始：</label>
				<div class=" layui-input-block layui-input-width" >
					<input lay-verify="required"  class="layui-input" type="text" id="drsj1" th:value="${amStart }">
				</div>
			</div>
		</div>

		<div class="layui-form-item layui-col-md6" style="clear:none;">
			<div class="layui-inline layui-form-high" >
				<label class="layui-form-label label-width"><span style="color:red;">*</span>系统登入时间【上午】结束：</label>
				<div class=" layui-input-block layui-input-width" >
					<input lay-verify="required"  class="layui-input" type="text"  id="drsj2" th:value="${amEnd }">
				</div>
			</div>
		</div>

		<input type="hidden" name="drsj" id="drsj" />
	</div>

	<div class="layui-row ">
		<div class="layui-form-item layui-col-md6" style="clear:none;">
			<div class="layui-inline layui-form-high" >
				<label class="layui-form-label label-width"><span style="color:red;">*</span>系统登入时间【下午】开始：</label>
				<div class=" layui-input-block layui-input-width" >
					<input lay-verify="required"  class="layui-input" type="text" id="drsj3" th:value="${pmStart }">
				</div>
			</div>
		</div>

		<div class="layui-form-item layui-col-md6" style="clear:none;">
			<div class="layui-inline layui-form-high" >
				<label class="layui-form-label label-width"><span style="color:red;">*</span>系统登入时间【下午】结束：</label>
				<div class=" layui-input-block layui-input-width" >
					<input lay-verify="required"  class="layui-input" type="text"  id="drsj4" th:value="${pmEnd }">
				</div>
			</div>
		</div>
	</div>



	<button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>

</form>

<script>

	$(function(){
		var status = $("#status").val();
		if(status == '0'){
			$("#isIpLock").attr('disabled', 'disabled');
			$("#isMmErr").attr('disabled', 'disabled');
			$("#isCqwdlPz").attr('disabled', 'disabled');
			$("#isGpfwPz").attr('disabled', 'disabled');
			$("#isTime").attr('disabled', 'disabled');
			$(".mm_fail_count").attr('disabled', 'disabled');
			$(".ip_fail_count").attr('disabled', 'disabled');
			$(".cqwdrsj").attr('disabled', 'disabled');
			$("#isIpCheck").attr('disabled', 'disabled');
			$("#fwsj").attr('disabled', 'disabled');
			$("#fwcs").attr('disabled', 'disabled');
			$("#drsj1").attr('disabled', 'disabled');
			$("#drsj2").attr('disabled', 'disabled');
			$("#drsj3").attr('disabled', 'disabled');
			$("#drsj4").attr('disabled', 'disabled');
		}else{
			if($("#isIpLock").val() == '0'){
				$(".ip_fail_count").attr('disabled', 'disabled');
			}
			if($("#isMmErr").val() == '0'){
				$(".mm_fail_count").attr('disabled', 'disabled');
			}
			if($("#isCqwdlPz").val() == '0'){
				$(".cqwdrsj").attr('disabled', 'disabled');
			}
			if($("#isGpfwPz").val() == '0'){
				$("#fwsj").attr('disabled', 'disabled');
				$("#fwcs").attr('disabled', 'disabled');
			}
			if($("#isTime").val() == '0'){
				$("#drsj1").attr('disabled', 'disabled');
				$("#drsj2").attr('disabled', 'disabled');
				$("#drsj3").attr('disabled', 'disabled');
				$("#drsj4").attr('disabled', 'disabled');
			}
		}
	})

	layui.use('laydate', function(){
		var laydate = layui.laydate;

		//执行一个laydate实例
		var amStart = laydate.render({
			elem: '#drsj1' //指定元素
			,type: 'time'
			,min: '00:00:00'
			,max: '11:59:59'
			,format : 'HH:mm'
			,trigger: 'click'//呼出事件改成click
			,choose: function (datas) {
				amEnd.min = datas; //开始日选好后，重置结束日的最小日期
				amEnd.start = datas //将结束日的初始值设定为开始日
			}
			,done: function (value, date, endDate) { //监听日期被切换
				debugger;
				var time = date.hours * 8 + date.minutes;
			}
		});

		var amEnd = laydate.render({
			elem: '#drsj2' //指定元素
			,type: 'time'
			,min: '00:00:00'
			,max: '11:59:59'
			,format : 'HH:mm'
			,trigger: 'click'//呼出事件改成click
			,choose: function (datas) {
				amStart.max = datas; //开始日选好后，重置结束日的最小日期
				amStart.end = datas //将结束日的初始值设定为开始日
			}
			,change: function (value, date, endDate) { //监听日期被切换

			}
		});

		//执行一个laydate实例
		var pmStart =laydate.render({
			elem: '#drsj3' //指定元素
			,type: 'time'
			,min: '12:00:00'
			,max: '24:00:00'
			,format : 'HH:mm'
			,trigger: 'click'//呼出事件改成click
		});

		var pmStart = laydate.render({
			elem: '#drsj4' //指定元素
			,type: 'time'
			,min: '12:00:00'
			,max: '24:00:00'
			,format : 'HH:mm'
			,trigger: 'click'//呼出事件改成click
		});

	});


	layui.use('form', function(){
		var form = layui.form;
		form.render();

		form.on('switch(statusFilter)', function(data){

			var checked = data.elem.checked;
			if(checked){
				$("#status").val(1);
                $("#isIpCheck").removeAttr('disabled');
				$("#isIpLock").removeAttr('disabled');
				if($("#isIpLock").val() == '1'){
					$(".ip_fail_count").removeAttr('disabled');
				}
				$("#isMmErr").removeAttr('disabled');
				if($("#isMmErr").val() == '1'){
					$(".mm_fail_count").removeAttr('disabled');
				}
				$("#isCqwdlPz").removeAttr('disabled');
				if($("#isCqwdlPz").val() == '1'){
					$(".cqwdrsj").removeAttr('disabled');
				}
				$("#isGpfwPz").removeAttr('disabled');
				if($("#isGpfwPz").val() == '1'){
					$("#fwsj").removeAttr('disabled');
					$("#fwcs").removeAttr('disabled');
				}
				$("#isTime").removeAttr('disabled');
				if($("#isTime").val() == '1'){
					$("#drsj1").removeAttr('disabled');
					$("#drsj2").removeAttr('disabled');
					$("#drsj3").removeAttr('disabled');
					$("#drsj4").removeAttr('disabled');
				}
			}else{

				$("#status").val(0);
                $("#isIpCheck").attr('disabled', 'disabled');
				$("#isIpLock").attr('disabled', 'disabled');
				$("#isMmErr").attr('disabled', 'disabled');
				$("#isCqwdlPz").attr('disabled', 'disabled');
				$("#isGpfwPz").attr('disabled', 'disabled');
				$("#isTime").attr('disabled', 'disabled');
				$(".mm_fail_count").attr('disabled', 'disabled');
				$(".ip_fail_count").attr('disabled', 'disabled');
				$(".cqwdrsj").attr('disabled', 'disabled');
				$("#fwsj").attr('disabled', 'disabled');
				$("#fwcs").attr('disabled', 'disabled');
				$("#drsj1").attr('disabled', 'disabled');
				$("#drsj2").attr('disabled', 'disabled');
				$("#drsj3").attr('disabled', 'disabled');
				$("#drsj4").attr('disabled', 'disabled');

			}
			form.render();//重新渲染下拉框
		});
		form.on('switch(isIpLockFilter)', function(data){
			var checked = data.elem.checked;
			if(checked){
				$("#isIpLock").val(1);
				$(".ip_fail_count").removeAttr('disabled');
			}else{
				$("#isIpLock").val(0);
				$(".ip_fail_count").attr('disabled', 'disabled');
			}
			form.render();
		});
		form.on('switch(isMmErrFilter)', function(data){
			var checked = data.elem.checked;
			if(checked){
				$("#isMmErr").val(1);
				$(".mm_fail_count").removeAttr('disabled');
			}else{
				$("#isMmErr").val(0);
				$(".mm_fail_count").attr('disabled', 'disabled');
			}
			form.render();
		});
		form.on('switch(isCqwdlPzFilter)', function(data){
			var checked = data.elem.checked;
			if(checked){
				$("#isCqwdlPz").val(1);
				$(".cqwdrsj").removeAttr('disabled');
			}else{
				$("#isCqwdlPz").val(0);
				$(".cqwdrsj").attr('disabled', 'disabled');
			}
			form.render();
		});
		form.on('switch(isGpfwPzFilter)', function(data){
			var checked = data.elem.checked;
			if(checked){
				$("#isGpfwPz").val(1);
				$("#fwsj").removeAttr('disabled');
				$("#fwcs").removeAttr('disabled');
			}else{
				$("#isGpfwPz").val(0);
				$("#fwsj").attr('disabled', 'disabled');
				$("#fwcs").attr('disabled', 'disabled');
			}
			form.render();
		});
		form.on('switch(isTimeFilter)', function(data){
			var checked = data.elem.checked;
			if(checked){
				$("#isTime").val(1);
				$("#drsj1").removeAttr('disabled');
				$("#drsj2").removeAttr('disabled');
				$("#drsj3").removeAttr('disabled');
				$("#drsj4").removeAttr('disabled');
			}else{
				$("#isTime").val(0);
				$("#drsj1").attr('disabled', 'disabled');
				$("#drsj2").attr('disabled', 'disabled');
				$("#drsj3").attr('disabled', 'disabled');
				$("#drsj4").attr('disabled', 'disabled');
			}
			form.render();
		});

		form.verify({
			verifyNum: function(value, item){
				var reg = /^[1-9]\d*$/;
				if(value.trim().length !=0){
					var v1 = parseInt(value);
					if(!reg.test(value.trim())){
						return "请输入正整数！";
					}
				}
			}
		})

		//监听提交
		form.on('submit(submitBut)', function(data){

			var id = $("#bh").val();
			var url = ctx + 'sysPzxxController/updatePzxx';

			if(id == null || id=='' || id==undefined ){
				url = ctx + 'sysPzxxController/saveSyspzxx';
			}

			var status = $("input[name='status']").val();
			var isIpLock = $("#isIpLock").val();
			var isMmErr = $("#isMmErr").val();
			var isCqwdlPz = $("#isCqwdlPz").val();
			var isGpfwPz = $("#isGpfwPz").val();
			var isTime = $("#isTime").val();
			var drsj1 = $("#drsj1").val();
			var drsj2 = $("#drsj2").val();
			var amDrsj = drsj1 +  "," + drsj2;

			var drsj3 = $("#drsj3").val();
			var drsj4 = $("#drsj4").val();
			var pmDrsj = drsj3 +  "," + drsj4;
			var drsj = amDrsj + "|" + pmDrsj;
			$("#drsj").val(drsj);
			data.field['drsj'] = drsj;

			var data = data.field;
			var tableName='pzxxTable';
            $.ajax({
                method:"POST",
                url:url,       //提交表单的地址
                data:data,      //提交表单的数据
                success:function(res){
                    if(res.stateType == 0){
                        parent.layer.msg(res.stateMsg, { icon: 1});
                        parent.layui.table.reload(tableName);
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }else{
                        layer.msg(res.stateMsg,{icon: 2});
                    }
                },
                error:function(data){
                    layer.close(loading);
                    layer.msg('操作失败',{icon: 2});
                }
            });

			return false;
		});



	})


</script>
