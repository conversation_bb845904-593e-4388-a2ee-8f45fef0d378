<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="UTF-8">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <script th:src="@{/scripts/security/crypto-js.js}"></script>
    <script th:src="@{/scripts/security/front_aes.js}"></script>
    <script th:src="@{/scripts/security/signature.js}"></script>
<div th:replace="Importfile::html"></div>
</head>
<body>

<form class="layui-form" action="">
<div class="layui-fluid">
<div class="layui-form-item">
<div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">修改密码</div>
<div class="layui-card-body" pad15="">
          
          <div class="layui-form" lay-filter="">
   <div class="layui-form-item">
        <input type="hidden" id="userId" name="userId" th:value="${userId}">
   <div class="layui-inline" th:if="${userId==null}">
    <label class="layui-form-label">当前密码</label>
    <div class="layui-input-inline">
      <input type="text" onfocus="this.type='password'" name="oldpassword" lay-verify="pass" placeholder="请输入密码" autocomplete="off" class="layui-input">
    </div>
   </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">新密码</label>
    <div class="layui-input-inline">
      <input type="text" onfocus="this.type='password'" id="newpassword" name="newpassword" lay-verify="pass|newpassword" placeholder="请输入密码" autocomplete="off" class="layui-input">
    </div>
    <div class="layui-form-mid layui-word-aux">请填写8到16位密码，且包含数字、字母、特殊字符</div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">确认新密码</label>
    <div class="layui-input-inline">
      <input type="text" onfocus="this.type='password'" id="confirmpassword" name="confirmpassword" lay-verify="pass|confirmpassword" placeholder="请输入密码" autocomplete="off" class="layui-input">
    </div>
  </div>

            
   </div> 
      </div> 
         </div> 
            </div> 
            <div class="layui-form-item">
              <div class="layui-input-block">
                <button class="layui-btn" lay-submit="" lay-filter="setmypass">确认修改</button>
              </div>
            </div>
            
            
             </div> 
              </div> 
               </div> 
</form>





 <script>
 //监听提交
 
 layui.use(['form', 'layedit', 'laydate'], function(){
  var form = layui.form
  ,layer = layui.layer
  ,layedit = layui.layedit
  ,laydate = layui.laydate;
  //自定义验证规则
   form.verify({
       newpassword: function (value) {
           var mediumRegex = new RegExp('^(?![a-zA-Z]+$)(?!\\d+$)(?![~!@#$%^&*()_+.]+$)(?![a-zA-Z\\d]+$)(?![a-zA-Z~!@#$%^&*()_+.]+$)(?![\\d~!@#$%^&*()_+.]+$)[a-zA-Z\\d~!@#$%^&*()_+.]+$', "g");  //强
           var message = mediumRegex.test(value);
           if(message == false){
               return "密码输入强度过弱!";
           }
           if (value.length == 0) {
               return "请输入密码";
           }
       },
       confirmpassword: function (value) {
           var newpassword = $('#newpassword').val();
           if (value != newpassword) {
               return "确认密码和新密码请保持一致";
           }
       },
   pass: [
      /^[\S]{8,16}$/
      ,'密码必须8到16位，且不能出现空格'
    ]
    ,content: function(value){
      layedit.sync(editIndex);
    }
  });

  
  
 form.on('submit(setmypass)', function(data){
     //密码加密(使用密文传输)
     data.field.oldpassword = RSAEncryptService(data.field.oldpassword);
     data.field.newpassword = RSAEncryptService(data.field.newpassword);
     data.field.confirmpassword = RSAEncryptService(data.field.confirmpassword);
     var userId=$('#userId').val();
     console.log(userId);
     var url=ctx+'adminPwController/upPassword';

     if(userId==''||userId==null){
         url=ctx+'pwController/upPassword';
     }
	   $.ajax({
			url : url,
			type : 'POST',
			data : data.field,
			async : false,
			cache : false,
			dataType : 'json',
			/* beforeSend: function () {
			        // 禁用按钮防止重复提交
			        $(".layui-btn").attr({ disabled: "disabled" });
			    }, */
			success : function(result) {
				if (result.code != 0) {
					layer.alert(result.msg, {icon: 2});      						
				} else {
                    var userId=$('#userId').val();
				    if(userId==''||userId==null){
                        layer.msg(result.msg, {
                            icon : 1
                        },function(){
                            parent.window.location.href=ctx+'logout';
                        });
                    }else{
                        layer.msg(result.msg, {
                            icon : 1
                        },function () {
                            window.parent.changTabs( ctx +'userController/userList','','用户列表');
                        });
                    }

				}
			},
			error : function(result) {
				layer.alert(result.msg, {
					icon : 5,
					skin : 'layer-ext-moon'
				});
			}
		});   
	   return false;
 });
 
 });
 </script>
</body>
</html>