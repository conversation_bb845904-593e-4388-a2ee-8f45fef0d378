/**
 * 
 */
open = function(url,title,width,height,saveUrl,formData,form){
    var flag = true;
	layer.open({
		type:2,
		title: title,
		area: [width, height],
		fixed: false, //不固定
        maxmin: true,
        content: [url],
        btn: ['确定', '取消'], 
        yes: function(index,layero){
            if (flag){
                flag=false;
                var submit = layero.find('iframe').contents().find("#subBtn");
                submit.click();
                setTimeout(function () {
                    flag=true;
                },1000);
            }
        },
        btn2: function(){
          layer.closeAll();
        },
        zIndex: layer.zIndex, //重点1
        success: function(layero){
           layer.setTop(layero); //重点2
        }
	});
}
/**
 * 保存的接口地址, 表格数据的地址
 */
commint = function(saveUrl,formData,form){
	
};
//*****
//  删除函数  
   deleteDemo =  function(url){
			
      }
   
 