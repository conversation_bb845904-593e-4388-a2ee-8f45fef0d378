/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.2 (2020-08-17)
 */
!function(u){"use strict";var n,t,e,o,r=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=tinymce.util.Tools.resolve("tinymce.util.Tools"),c=function(n,t){var e,o=n.dom,r=n.selection.getSelectedBlocks();r.length&&(e=o.getAttrib(r[0],"dir"),i.each(r,function(n){o.getParent(n.parentNode,'*[dir="'+t+'"]',o.getRoot())||o.setAttrib(n,"dir",e!==t?t:null)}),n.nodeChanged())},f=function(n){return function(){return n}},d=f(!1),l=f(!0),m=function(){return a},a=(n=function(n){return n.isNone()},{fold:function(n,t){return n()},is:d,isSome:d,isNone:l,getOr:e=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:f(null),getOrUndefined:f(undefined),or:e,orThunk:t,map:m,each:function(){},bind:m,exists:d,forall:l,filter:m,equals:n,equals_:n,toArray:function(){return[]},toString:f("none()")}),s=function(e){var n=f(e),t=function(){return r},o=function(n){return n(e)},r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:l,isNone:d,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return s(n(e))},each:function(n){n(e)},bind:o,exists:o,forall:o,filter:function(n){return n(e)?r:a},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(d,function(n){return t(e,n)})}};return r},g={some:s,none:m,from:function(n){return null===n||n===undefined?a:s(n)}},h=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:f(n)}},y={fromHtml:function(n,t){var e=(t||u.document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw u.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return h(e.childNodes[0])},fromTag:function(n,t){var e=(t||u.document).createElement(n);return h(e)},fromText:function(n,t){var e=(t||u.document).createTextNode(n);return h(e)},fromDom:h,fromPoint:function(n,t,e){var o=n.dom();return g.from(o.elementFromPoint(t,e)).map(h)}},v=(o="function",function(n){return typeof n===o}),p=("undefined"!=typeof u.window?u.window:Function("return this;")(),function(t){return function(n){return n.dom().nodeType===t}}),T=p(3),w=p(9),N=p(11),D=v(u.Element.prototype.attachShadow)&&v(u.Node.prototype.getRootNode)?function(n){return y.fromDom(n.dom().getRootNode())}:function(n){return w(n)?n:(t=n,y.fromDom(t.dom().ownerDocument));var t},O=function(n){var t=D(n);return N(t)?g.some(t):g.none()},C=function(n){return y.fromDom(n.dom().host)},S=function(n){var t,e,o=T(n)?n.dom().parentNode:n.dom();return o!==undefined&&null!==o&&null!==o.ownerDocument&&O(y.fromDom(o)).fold(function(){return o.ownerDocument.body.contains(o)},(t=S,e=C,function(n){return t(e(n))}))},L=function(n,t){return(e=n).style!==undefined&&v(e.style.getPropertyValue)?n.style.getPropertyValue(t):"";var e},R=function(n){return"rtl"===(e="direction",o=(t=n).dom(),""!==(r=u.window.getComputedStyle(o).getPropertyValue(e))||S(t)?r:L(o,e))?"rtl":"ltr";var t,e,o,r},A=function(t,o){return function(e){var n=function(n){var t=y.fromDom(n.element);e.setActive(R(t)===o)};return t.on("NodeChange",n),function(){return t.off("NodeChange",n)}}};!function P(){r.add("directionality",function(n){var t,e;(t=n).addCommand("mceDirectionLTR",function(){c(t,"ltr")}),t.addCommand("mceDirectionRTL",function(){c(t,"rtl")}),(e=n).ui.registry.addToggleButton("ltr",{tooltip:"Left to right",icon:"ltr",onAction:function(){return e.execCommand("mceDirectionLTR")},onSetup:A(e,"ltr")}),e.ui.registry.addToggleButton("rtl",{tooltip:"Right to left",icon:"rtl",onAction:function(){return e.execCommand("mceDirectionRTL")},onSetup:A(e,"rtl")})})}()}(window);