<?xml version="1.0" encoding="UTF-8" ?>   
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IConfigDao">
	<resultMap type="com.fwy.corebasic.entity.Core_Config" id="configMap">
		<result property="configName" column="configname" javaType="String"
			jdbcType="VARCHAR" />
		<result property="configJson" column="configjson" javaType="String"
			jdbcType="VARCHAR" />
        <result property="configJson" column="configjson" javaType="String"
                jdbcType="VARCHAR" />
	</resultMap>
	<select id="getSysBaseInfo" resultType="com.fwy.corebasic.entity.Core_Config">
		select * from core_config
	</select>
	<update id="save" parameterType="com.fwy.corebasic.entity.Core_Config">
		update core_config set
		CONFIGNAME=#{configName},CONFIGJSON=#{configJson} where
		CONFIGNAME=#{configName}
	</update>

	<select id="queryOne" resultType="com.fwy.corebasic.entity.Core_Config">
		select * from core_config
		<where>
			<if test="configName!=null and configName!=''">
				and CONFIGNAME = #{configName,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<update id="update" parameterType="com.fwy.corebasic.entity.Core_Config">
		update core_config
		<set>
			<if test="configJson != null">
				CONFIGJSON = #{configJson,jdbcType=VARCHAR},
			</if>
		</set>
		where CONFIGNAME=#{configName}
	</update>
</mapper>