<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>api接口日志</title>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <input type="hidden" id="treeDeptId">
                <div >
                    <div class="layui-inline">
                        <label class="layui-form-label" >开始日期：</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="beginTime" autocomplete="off" placeholder="请输入开始日期">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">结束日期：</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="endTime" autocomplete="off" placeholder="请输入结束日期">
                        </div>
                    </div>
                        <div class="layui-inline" >
                            <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                            <button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</button>
                            <button class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
                        </div>
                </div>
            </blockquote>
        <script type="text/html" id="test-table-toolbar-barDemo">
            <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
        </script>
        <table class="layui-hide" id="apilogList_table" lay-filter="apilogList_table"></table>
    </div>
</div>
</div>
<script>
    //回车搜索
    $("blockquote").on('keyup','input,select',function (e) {
        if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#search_btn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.use(['table','laydate'], function(){
        var admin = layui.admin,laydate = layui.laydate
        var  table = layui.table;
        //日期
        laydate.render({
            elem: '#beginTime'
            //,type: 'datetime'
        });
        laydate.render({
            elem: '#endTime'
            //,type: 'datetime'
        });
        table.render({
                toolbar : '#topToolbar',
                elem : '#apilogList_table',
                even : false,
                title : 'apilog列表',
                defaultToolbar:[],
                // height : 'full-100',
                url : ctx + '/apiLogController/apiLogList',
                page : true,
                cols : [ [
                    {
                        field: 'jkcs',
                        width: 80,
                        title: '参数',
                        toolbar: '#test-table-toolbar-barDemo',
                        align:'center'
                    },
                    {
                        field:'id',
                        title:'id',
                        align: 'center'
                    },
                    {
                        field: 'module',
                        title: '功能模块',
                        width:160,
                        align: 'center'
                    },
                    {
                        field: 'type',
                        title: '操作类型',
                        width:120,
                        align: 'center'
                    },
                    {
                        field: 'description',
                        title: '操作描述',
                        width: 120,
                        align: 'center'
                    },
                    {
                        field: 'requParam',
                        title: '请求参数',
                        width:200,
                        align: 'center'
                    }, {
                        field: 'respParam',
                        title: '返回参数',
                        width:200,
                        align: 'center'
                    }, {
                        field: 'userId',
                        title: '操作员id',
                        width:200,
                        align: 'center'
                    }, {
                        field: 'userName',
                        title: '操作员名称',
                        width:200,
                        align: 'center'
                    },{
                        field: 'method',
                        title: '操作方法',
                        width:200,
                        align: 'center'
                    },{
                        field: 'uri',
                        title: '请求uri',
                        width:200,
                        align: 'center'
                    },{
                        field: 'ip',
                        title: '请求ip',
                        width:200,
                        align: 'center'
                    },
                    {
                        field: 'createTime',
                        title: '操作时间',
                        width:200,
                        align: 'center'
                    },
                    {
                        field: 'ver',
                        title: '操作版本号',
                        width:200,
                        align: 'center'
                    }
                ] ],
                request : {
                    pageName : 'pageNum', //页码的参数名称，默认：page
                    limitName : 'pageSize' //每页数据量的参数名，默认：limit
                },
                parseData : function(res) { //res 即为原始返回的数据
                    return {
                        "code" : res.code, //解析接口状态
                        "msg" : res.msg, //解析提示文本
                        "count" : res.data.total, //解析数据长度
                        "data" : res.data.list
                        //解析数据列表
                    }
                },
                done : function(res, curr, count) {
                }

            });
        /*var active = {
            reload:function(){
                var deptId = "";
                if(!(dialog_deptId.getValue()=="")){
                    deptId = dialog_deptId.getValue()[0].ID;
                }
                table.reload('apilogList_table',{
                    page:{
                        curr:1
                    }

                })
            }
        };*/
        //搜索及重置按钮
        $("#search_btn").click(function () {
            debugger;
            var beginTime = $("#beginTime").val();
            var endTime = $("#endTime").val();
            table.reload('apilogList_table', {
                where: { //设定异步数据接口的额外参数，任意设
                     beginTime: beginTime
                    , endTime: endTime
                }
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
        //搜索及重置按钮
        $("#unset_Btn").click(function () {
            $("#beginTime").val("");
            $("#endTime").val("");
            table.reload('apilogList_table', {
                where: null
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            }); //只重载数据
        });
        $(document).keydown(function(event){
            if(event.keyCode == 13){
                var type = 'reload';
                active[type] ? active[type].call(this) : '';
            }
        });
        //监听行工具事件
        table.on('tool(apilogList_table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.open({
                    type: 2,
                    title: "查看api日志详情",
                    shadeClose: true,
                    btn:"返回",
                    area: ['99%', '95%'],
                    offset: [ //为了演示，随机坐标
                        0.1 * ($(window).height() - 400)
                    ]
                    , content: ctx + 'apiLogController/apiLogDetial?ID=' + data.id
                });
            }
        });
    });

</script>

</body></html>