<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>业务类型管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script type="text/javascript" th:src="@{/scripts/dic/business/businessList.js}"></script>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <a style="margin-right:5px;" href="javascript:newTab('dicBusinessController/addBusiness','添加业务')">
				<button class="layui-btn layui-btn-sm">
					<i class="layui-icon layui-icon-add"></i>增加
				</button>
			</a>
            <table class="layui-table layui-form treeTable" id="tree-table"></table>
        </div>
    </div>
</div>
<script th:inline="javascript">
	var ctx = [[${basePath}]];
	var top_value = [[${top_value}]];
	businessList(ctx,top_value);
</script>
</body>
</html>
