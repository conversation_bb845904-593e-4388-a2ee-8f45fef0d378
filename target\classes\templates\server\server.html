<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<script th:replace="Importfile::html"></script>
    <meta charset="utf-8">
    <title>预警提示</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1">
</head>
<script th:src="@{/scripts/security/main/openWay.js}"></script>
<script th:replace="loading::loading"></script>
<style>
    .layui-fluid {
        padding: 15px; !important;
    }
    .layadmin-carousel {
        height: 200px !important;
        background-color: #fff
    }
    .layuiadmin-card-list p.layuiadmin-big-font {
        font-size: 30px;
        color: #666;
        line-height: 30px;
        padding: 5px 0 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
    }

    .layuiadmin-card-list p {
        cursor: pointer;
    }

    .layui-icon {
        display: inline-block;
        width: 100%;
        height: 60px;
        line-height: 60px;
        text-align: center;
        border-radius: 2px;
        font-size: 30px;
        background-color: #F8F8F8;
        color: #333;
        transition: all .3s;
        -webkit-transition: all .3s
    }

    .layadmin-backlog-body {
        display: block;
        padding: 10px 15px;
        background-color: #f8f8f8;
        color: #999;
        border-radius: 2px;
        transition: all .3s;
        -webkit-transition: all .3s;
    }

    cite {
        font-style: normal;
        font-size: 30px;
        font-weight: 300;
        color: #009688;
    }

    /*删格化5等份*/
    .layui-col-lg2-4,.layui-col-md2-4,.layui-col-sm2-4,.layui-col-xs2-4{position:relative;display:block;box-sizing:border-box}
    .layui-col-xs2-4{float:left}
    .layui-col-xs2-4{width:16.666666%}
    .layui-col-xs-offset2-4{margin-left:16.666666%}
    @media screen and (min-width:768px){
        .layui-col-sm2-4{float:left}
        .layui-col-sm2-4{width:16.666666%}
        .layui-col-sm-offset2-4{margin-left:16.666666%}
    }
    @media screen and (min-width:992px){
        .layui-col-md2-4{float:left}
        .layui-col-md2-4{width:16.666666%}
        .layui-col-md-offset2-4{margin-left:16.666666%}
    }
    @media screen and (min-width:1200px){
        .layui-col-lg2-4{float:left}
        .layui-col-lg2-4{width:16.666666%}
        .layui-col-lg-offset2-4{margin-left:16.666666%}
    }
    p.layuiadmin-big-font{
        font-size: 25px !important;
    }
    cite{
        font-size: 20px !important;
    }
</style>
<body onload="load()">
<div th:replace="loading::demo"></div>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm2-4 layui-col-md2-4">
            <div class="layui-card">
                <div class="layui-card-header">
                    连接线程数 <span style="margin-left: 60%;"
                               class="layui-badge layui-bg-blue layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="nowProcesses">0</p>
                </div>
            </div>
        </div>
        <div class="layui-col-sm2-4 layui-col-md2-4">
            <div class="layui-card">
                <div class="layui-card-header">
                    缓存区命中率 <span style="margin-left: 60%;"
                               class="layui-badge layui-bg-green layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font"  id="cacheHitRate">0</p>
                </div>
            </div>
        </div>
        <div class="layui-col-sm2-4 layui-col-md2-4">
            <div class="layui-card">
                <div class="layui-card-header">
                    共享池命中率 <span style="margin-left: 60%;"
                               class="layui-badge layui-bg-cyan layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="sharedPoolHitRate">0</p>

                </div>
            </div>
        </div>
        <div class="layui-col-sm2-4 layui-col-md2-4">
            <div class="layui-card">
                <div class="layui-card-header" title="目前状态为中止人员数据">
                    日志缓冲区占用率 <span style="margin-left: 60%;"
                               class="layui-badge layui-bg-orange layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="logOccupancy">0</p>

                </div>
            </div>
        </div>
        <div class="layui-col-sm2-4 layui-col-md2-4">
            <div class="layui-card">
                <div class="layui-card-header">
                    锁(LOCK)情况 <span style="margin-left: 60%; background-color: #9466e2;color: ButtonFace"
                                    class="layui-badge  layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="lockNumber">0</p>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-col-sm12">
        <div class="layui-row layui-col-space12">
            <div class="layui-col-sm12">
                <div class="layui-card" style="height: 350px">
                    <div class="layui-card-header" >
                        <span>表空间使用情况</span>
                        <span id="tableUsed" style="margin-left: 20px;"></span>
                    </div>
                    <div class="layui-card-body" style="min-height: 350px;">
                        <div id="main1" class="layui-col-sm12" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">CPU使用情况</div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <colgroup>
                            <col width="50%">
                        	<col width="50%">
                        </colgroup>
                        <thead>
                        <tr>
                            <th>属性</th>
                            <th>值</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>核心数</td>
                            <td id="CpuNumber">0个</td>
                        </tr>
                        <tr>
                            <td>用户使用率</td>
                            <td id="CpuUserRate">0%</td>
                        </tr>
                        <tr>
                            <td>系统使用率</td>
                            <td id="CpuSysRate">0%</td>
                        </tr>
                        <tr>
                            <td>当前空闲率</td>
                            <td id="CpuFreeRate">0%</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>

        <div class="layui-col-md6">

            <div class="layui-card">
                <div class="layui-card-header">内存</div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <colgroup>
                           <col width="30%">
                        	<col width="35%">
                        	<col>
                        </colgroup>
                        <thead>
                        <tr>
                            <th>属性</th>
                            <th>内存</th>
                            <th>JVM</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>总内存</td>
                            <td id="MemTotal">0</td>
                            <td id="JvmTotal">0</td>
                        </tr>
                        <tr>
                            <td>已使用</td>
                            <td id="MemUsed">0</td>
                            <td id="JvmUsed">0</td>
                        </tr>
                        <tr>
                            <td>剩余</td>
                            <td id="MemFree">0</td>
                            <td id="JvmFree">0</td>
                        </tr>
                        <tr>
                            <td>使用率</td>
                            <td id="MemUsedRate">0%</td>
                            <td id="JvmUsedRate">0%</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>

        <div class="layui-col-sm12">
            <div class="layui-row layui-col-space12">
                <div class="layui-col-sm12">
                    <div class="layui-card">
                        <div class="layui-card-header">服务器信息</div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <colgroup>
                                   <col width="25%">
                                <col width="25%">
                                <col width="25%">
                                <col >
                                </colgroup>
                                <thead>
                                <tr>
                                    <th>服务器名称</th>
                                    <th>服务器IP</th>
                                    <th>操作系统</th>
                                    <th>系统架构</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td id="computerName">0</td>
                                    <td id="computerIp">0</td>
                                    <td id="osName"></td>
                                    <td id="osArch"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm12">
            <div class="layui-row layui-col-space12">
                <div class="layui-col-sm12">
                    <div class="layui-card">
                         <div class="layui-card-header">java虚拟机信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <colgroup>
                                <col width="15%">
                                <col width="35%">
                                <col width="15%">
                                <col>
                            </colgroup>
                            <thead>
                            <tbody>
                            <tr>
                                <td>java名称</td>
                                <td id="javaName">0</td>
                                <td>java版本</td>
                                <td id="javaVersion"></td>
                            </tr>
                            <tr>
                                <td>启动时间</td>
                                <td id="StartTime">0</td>
                                <td>运行时长</td>
                                <td id="RunTime"></td>
                            </tr>
                            <tr>
                                <td >安装路径</td>
                                <td id="home" colspan=3>0</td>
                             
                            </tr>
                            <tr>
                                <td >项目路径</td>
                                <td id="userDir" colspan=3>0</td>
                          
                            </tr>
                            </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm12">
            <div class="layui-row layui-col-space12">
                <div class="layui-col-sm12">
                    <div class="layui-card">
                        <div class="layui-card-header">磁盘状态</div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <colgroup>
                                     <col width="10%">
                                <col width="10%">
                                <col width="20%">
                                <col width="15%">
                                <col width="15%">
                                <col width="15%">
                                <col >
                                </colgroup>
                                <thead>
                                <tr>
                                    <th>盘符路径</th>
                                    <th>盘符类型</th>
                                    <th>文件类型</th>
                                    <th>总大小</th>
                                    <th>剩余大小</th>
                                    <th>已经使用量</th>
                                    <th>资源的使用率</th>
                                </tr>
                                </thead>
                                <tbody id="tbbody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script th:src="@{/plugins/echarts/echarts.min.js}"></script>
<script>
    var myChart = echarts.init(document.getElementById('main1'));
    $.ajax({
        url: ctx + '/serverController/findSystemDate',
        success:function(response){
            if(response.code === 0){
                var data = response.data;
                $("#nowProcesses").html(data.nowProcesses);
                $("#cacheHitRate").html(Number(data.cacheHitRate * 100).toFixed(1) + '%');
                $("#sharedPoolHitRate").html(Number(data.sharedPoolHitRate * 100).toFixed(1) + '%');
                $("#logOccupancy").html(Number(data.logOccupancy * 100).toFixed(1) + '%');
                $("#lockNumber").html(data.lockNumber);
            }
        },
        error:function(data){

        }
    });

    $.ajax({
        url: ctx + '/serverController/findTableSpaceData',
        success:function(response){
            var data = response.data;
            if(response.code === 0){
                var dataX = [],
                    dataUsed = [],
                    dataFree = [];
                var html = '使用超出80%的表空间:';
                for(var i = 0;i < data.length;i++){
                    dataX.push(data[i].tableSpaceName);
                    dataUsed.push(data[i].used);
                    dataFree.push(data[i].free);
                    var rate = data[i].used / data[i].total;
                    if (rate > 0.8){
                        html += data[i].tableSpaceName + '&nbsp;&nbsp;&nbsp;';
                    }
                    $("#tableUsed").html(html);
                }
               var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                        }
                    },
                    legend: {
                        data: [ '已使用', '空闲']
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: dataX
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value'
                        }
                    ],
                    series: [
                        {
                            name: '已使用',
                            type: 'bar',
                            stack: '表格',
                            data: dataUsed
                        },
                        {
                            name: '空闲',
                            type: 'bar',
                            stack: '表格',
                            data: dataFree
                        }
                    ]
                };
                myChart.setOption(option);
            }
        },
        error:function(data){
            console.log(data);
        }
    });

    $.ajax({
        url: ctx + '/serverController/testData',
        success:function(response){
            if(response.code === 0){
                var data = response.data;
                $("#CpuNumber").html(data.cpu.cpuNum);
                var free = data.cpu.free * 100 / data.cpu.total;
                $("#CpuUserRate").html(Number(data.cpu.used * 100 / data.cpu.total).toFixed(1) + '%');
                $("#CpuSysRate").html(Number(data.cpu.sys * 100 / data.cpu.total).toFixed(1) + '%');
                $("#CpuFreeRate").html(Number(free).toFixed(1) + '%');
                if(free < 20){
                    $("#CpuFreeRate").attr({style:"color:red"});
                }
                var memUsedRate = data.mem.used * 100 / data.mem.total;
                var jvmUsedRate = Number( 100 - data.jvm.free * 100 / data.jvm.total).toFixed(1);
                $("#MemTotal").html(data.mem.total);
                $("#JvmTotal").html(data.jvm.total);
                $("#MemUsed").html(data.mem.used);
                $("#JvmUsed").html(data.jvm.total - data.jvm.free);
                $("#MemFree").html(data.mem.free);
                $("#JvmFree").html(data.jvm.free);
                $("#MemUsedRate").html(Number(memUsedRate).toFixed(1) + '%');
                $("#JvmUsedRate").html(Number(jvmUsedRate).toFixed(1) + '%');
                if(memUsedRate > 80){
                    $("#MemUsedRate").attr({style:"color:red"});
                }
                if(jvmUsedRate > 80){
                    $("#jvmUsedRate").attr({style:"color:red"});
                }
                $("#computerName").html(data.sys.computerName);
                $("#computerIp").html(data.sys.computerIp);
                $("#osName").html(data.sys.osName);
                $("#osArch").html(data.sys.osArch);

                $("#javaName").html(data.jvm.name);
                $("#javaVersion").html(data.jvm.version);
                $("#StartTime").html(data.jvm.startTime);
                $("#RunTime").html(data.jvm.runTime);
                $("#home").html(data.jvm.home);
                $("#userDir").html(data.sys.userDir);
                for (var i=0;i<data.sysFiles.length;i++){
                    $("#tbbody").append(
                    '<tr>'+
                        '<td>'+data.sysFiles[i].dirName +'</td>'+
                        '<td>'+data.sysFiles[i].sysTypeName +'</td>'+
                        '<td>'+data.sysFiles[i].typeName +'</td>'+
                        '<td>'+data.sysFiles[i].total +'</td>'+
                        '<td>'+data.sysFiles[i].free +'</td>'+
                        '<td>'+data.sysFiles[i].used +'</td>'+
                        '<td>'+data.sysFiles[i].usage +'%</td>'+
                    +'</tr>'
                    );
                }
            }
        },
        error:function(data){
            console.log(data);
        }
    });
</script>
</html>