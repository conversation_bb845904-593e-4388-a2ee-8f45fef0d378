<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	  xmlns:http="http://www.w3.org/1999/xhtml"
	  xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
	<meta charset="utf-8">
	<title>角色管理</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<div th:replace="Importfile::html"></div>
	<script th:src="@{/scripts/security/main/openWay.js}"></script>
	<script th:src="@{/plugins/formSelects/xm-select.js}"></script>
	<style>
		.label-width{
			width:100px;
		}
		.input-width{
			width:75% !important;
		}
	</style>
</head>
<body>

<div id="" class="layui-layer-content" style="overflow: visible;">
	<form id="modelAuthForm" lay-filter="modelAuthForm" class="layui-form">
		<div class="layui-form-item">
			<input name="id" id="id" type="hidden" th:value="${role?.id}">
			<input name="roleParent" id="roleParent" type="hidden" th:value="${role?.parentId}">
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"  style="width: 100px"><span style="color:red">*</span>角色名称：</label>
			<div class="layui-input-inline " style="width: 262px">
				<input name="roleName" type="text" class="layui-input" maxlength="50" placeholder="请输入角色名称"
					   lay-vertype="tips" oninput="del(this,'blank|char')" lay-verify="required|roleName" th:value="${role?.roleName}">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"  style="width: 100px">父级角色：</label>
			<div class="layui-input-inline" style="width: 300px;">
				<div class="xm-select" id="parentId">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"  style="width: 100px"><span style="color:red">*</span>权限列表：</label>
			<div class="layui-input-inline " style="width: 260px">
				<div class="xm-select" id="authIdsSelect">
				</div>
				<input type="hidden" id="authIds" th:value="${authIds}" >
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width: 100px">角色权限范围：</label>
				<div class="layui-input-inline" style="width: 300px">
					<input type="hidden" id="roleRange" th:value="${role?.roleRange}">
					<div class="xm-select" id="roleRanges">

					</div>
				</div>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 100px;">是否系统级：</label>
			<div class="layui-input-inline">
				<input type="checkbox" value="1" th:attr="checked=${role?.isSys==1?true:false}" name="isSys" lay-skin="switch" lay-text="是|否">
			</div>
		</div>
        <div class="layui-form-item" id="deptDiv" style="display: none">
            <div class="layui-inline">
                <label class="layui-form-label"  style="width: 100px">指定部门：</label>

                <div class="layui-input-inline" style="width: 300px">
                    <input type="hidden" id="appointDepts" th:value="${role?.depts}">
                    <div class="xm-select" id="appointDept">

                    </div>
                </div>
            </div>
        </div>
		<button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>
	</form>
</div>
<script>





	layui.use('form', function(){
		var form = layui.form;
		form.verify({
			roleName: function(value, item){
				if(top.window.parent.getBytes(value) >20){
					return '角色名称不能超过'+20+'个字符的长度';
				}
			}
		});
		form.render();
		//监听提交
		form.on('submit(submitBut)', function(data){
			var saveUrl = ctx + 'roleController/saveRole',
					tableName='role_table';
			var authNames = dialog_auth.getValue('nameStr');
			data.field.authNames = authNames;
			var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
			$.ajax({
				method:"POST",
				url:saveUrl,       //提交表单的地址
				data:data.field,      //提交表单的数据
				success:function(res){
					if(res.code == 0){
						var index = parent.layer.getFrameIndex(window.name);
						parent.layer.close(index);
						parent.layer.msg(res.msg, { icon: 1});
						parent.layui.table.reload(tableName);
					}else{
						layer.msg(res.msg,{icon: 2});
					}
				},
				error:function(data){
					layer.close(loading);
					layer.msg('操作失败',{icon: 2});
				}
			});
			return false;
			/*xadmin.submitForm(saveUrl,data,tableName);
               return false; */
		});
	})

	//父级角色
	var dialog_role = xmSelect.render({
		el: '#parentId',
		filterable: true,
		name: 'parentId',
		tips: '请选择',
		model: {label: {type: 'block'}},
		template:function(item) {
			return '<p title="' + item.name + '">' + item.name + '</p>';
		},
		on: function (data) {
			var arr = data.arr;
			var change = data.change;
		},
		radio: true,//单选多选
		tree: {
			show: true,
			strict: false, //是否父子结构，父子结构父节点不会被选中
			indent: 30,//间距
			expandedKeys: [-1],
			clickCheck: true,
			clickExpand: true,//点击展开
		},
		clickClose: true,//点击关闭
		autoRow: true,
		style: {
			paddingLeft: '10px',
			position: 'relative',
			width: '250px'
		},
		prop: {
			name: "roleName",
			value: "id"
		},
		height: '200px',
		empty: '暂无数据',
		data: [],
		direction: 'auto'
	});
	//角色权限范围
	var dialog_roleRange = xmSelect.render({
		el: '#roleRanges',
		filterable: true,
		name: 'roleRange',
		tips: '请选择',
		model: {label: {type: 'block'}},
		template:function(item) {
			return '<p title="' + item.name + '">' + item.name + '</p>';
		},
		on: function (data) {
			var arr = data.arr;
			var change = data.change;
			//当选中指定部门的权限范围时 需显示部门列表
			if (arr.length>0){
				var dicValue = arr[0].dicValue;
				if(dicValue==3){
					queryDept();
					$("#deptDiv").css("display","inline-block");
				}else {
					$("#deptDiv").css("display","none");
				}
			}
		},
		radio: true,//单选多选
		tree: {
			show: true,
			strict: false, //是否父子结构，父子结构父节点不会被选中
			indent: 30,//间距
			expandedKeys: [-1],
			clickCheck: true,
			clickExpand: true,//点击展开
		},
		clickClose: true,//点击关闭
		autoRow: true,
		style: {
			paddingLeft: '10px',
			position: 'relative',
			width: '250px'
		},
		prop: {
			name: "disCription",
			value: "dicValue"
		},
		height: '200px',
		empty: '暂无数据',
		data: [],
		direction: 'auto'
	});

	//指定部门
	var dialog_dept = xmSelect.render({
		el: '#appointDept',
		filterable: true,
		name: 'appointDepts',
		tips: '请选择',
		model: {label:
					{type: 'block',block: {
							//最大显示数量, 0:不限制
							showCount: 1,
							//是否显示删除图标
							showIcon: true
						}}},

		template:function(item) {
			return '<p title="' + item.name + '">' + item.name + '</p>';
		},
		on: function (data) {
			var arr = data.arr;
			var change = data.change;
		},
		radio: false,//单选多选
        toolbar: { //开启工具栏
            show: true,
            list: ['ALL', 'CLEAR'],
        },
		tree: {
			show: true,
			strict: false, //是否父子结构，父子结构父节点不会被选中
			indent: 30,//间距
			expandedKeys: [-1],
			clickCheck: true,
			clickExpand: true,//点击展开
		},
		clickClose: false,//点击关闭
		autoRow: true,
		style: {
			paddingLeft: '10px',
			position: 'relative',
			width: '250px'
		},
		prop: {
			name: "deptName",
			value: "ID"
		},
		height: '200px',
		empty: '暂无数据',
		data: [],
		direction: 'auto'
	});
	//权限列表
	var dialog_auth = xmSelect.render({
		el: '#authIdsSelect',
		filterable: true,
		name: 'authIds',
		tips: '请选择',
		model: {label: {type: 'block',block: {
					//最大显示数量, 0:不限制
					showCount: 1,
					//是否显示删除图标
					showIcon: true
				}}},
		layVerify: 'required',
		layVerType: 'msg',

		template:function(item) {
			return '<p title="' + item.name + '">' + item.name + '</p>';
		},
		on: function (data) {
			var arr = data.arr;
			var change = data.change;
		},
		radio: false,//单选多选
		tree: {
			show: true,
			strict: false, //是否父子结构，父子结构父节点不会被选中
			indent: 30,//间距
			expandedKeys: [-1],
			clickCheck: true,
			clickExpand: true,//点击展开
		},
		clickClose: false,//点击关闭
		autoRow: true,
		style: {
			paddingLeft: '10px',
			position: 'relative',
			width: '250px'
		},
		prop: {
			name: "authName",
			value: "id"
		},
		height: '200px',
		empty: '暂无数据',
		data: [],
		direction: 'auto'
	});

	function queryDept(){
		//查询指定部门
		$.ajax({
			url: ctx + '/deptController/localTree2',
			data: {deptIds: $("#appointDepts").val()},
			method: "get",
			dataType: 'json',
			success: function (response) {
				dialog_dept.update({
					data: response.data
				})
			},
			error: function (res) {
			}
		});
	}

	$(function () {

		//权限列表
		$.get(ctx + 'authController/authDialogJson?authIds='+$("#authIds").val(),function (res) {
			dialog_auth.update({
				data:res.data,
			})
		})

		if ($("#roleRange").val()==3){
			queryDept();
			$("#deptDiv").css("display","inline-block");
		}

		//查询父级角色树
		$.ajax({
			url: ctx + 'roleController/localTree',
			data: {id:$("#id").val(),roleId: $("#roleParent").val()},
			method: "get",
			dataType: 'json',
			success: function (response) {
				dialog_role.update({
					data: response.data
				})
			},
			error: function (res) {
			}
		});
		//查询角色权限范围
		$.ajax({
			url: ctx + '/dataDetailController/formSelectByCode',
			data: {dicCode: "roleRange", "id": $("#roleRange").val()},
			method: "get",
			dataType: 'json',
			success: function (response) {
				dialog_roleRange.update({
					data: response.data
				})
			},
			error: function (res) {
			}
		});

	})

</script>
</body>
</html>