<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.intermediary.dao.ISuspectedPersonnelRecordDao">
    <resultMap id="suspectPersonnel" type="com.fwy.intermediary.entity.SuspectedPersonnelRecord">
        <result column="code" property="code"/>
        <result column="person_code" property="personCode"/>
        <result column="full_name" property="fullName"/>
        <result column="idcard_num" property="idcardNum"/>
        <result column="oss_url" property="ossUrl"/>
        <result column="end_day" property="endDay"/>
        <result column="state_id" property="stateId"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="findByCondition" parameterType="SuspectedPersonnelCondition" resultMap="suspectPersonnel">
        select * from suspected_personnel_record where state_id != 3
            <if test="stateId != null">
               and state_id = #{stateId}
            </if>
            <if test="fullName != null &amp;&amp; fullName != ''">
                and full_name like concat('%', #{fullName}, '%')
            </if>
            <if test="deptIds != null and deptIds.size() != 0">
                and dept_id in
                <foreach collection="deptIds" item="deptId" index="index"
                        open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="idcardNum != null &amp;&amp; idcardNum != ''">
                and idcard_num = #{idcardNum}
            </if>
            <if test="createUser != null &amp;&amp; createUser != ''">
                and create_user like concat('%', #{createUser}, '%')
            </if>
            <if test="updateUser != null &amp;&amp; updateUser != ''">
                and update_user like concat('%', #{updateUser}, '%')
            </if>
            <if test="startDate != null">
                and create_time >= #{startDate}
            </if>
            <if test="endDate != null">
                <!--&amp;lt;小于-->
                and create_time &lt;= #{endDate}
            </if>
            <if test="updateTime != null">
                and update_time >= #{startDate}
            </if>
    </select>
    <select id="findByPersonCode" resultMap="suspectPersonnel">
        select *
        from suspected_personnel_record
        where person_code = #{personCode} limit 1;
    </select>
    <select id="ifExists" resultType="java.lang.Integer" parameterType="java.lang.String">
        select exists
                   (select person_code from suspected_personnel_record
                    where person_code = #{code,jdbcType=VARCHAR})
    </select>
</mapper>