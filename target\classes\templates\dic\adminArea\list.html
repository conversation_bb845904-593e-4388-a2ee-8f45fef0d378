<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
>
<head>
    <meta charset="utf-8">
    <title>行政区划管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>

    <style>
        .demo-side {
            position: relative;
            transition: all .3s;
        }

        .demo-side.show {
            padding-left: 120px;
            position: relative;
        }

        .demo-side:before {
            content: "假设这是侧边栏, treeTable使用了与layui数据表格完全不同的列宽分配方式, 由浏览器来分配, 所以当容器宽度变化后会自动适应, 对单页面系统非常友好";
            position: absolute;
            left: 0;
            top: 50%;
            margin-top: -90px;
            width: 105px;
            visibility: hidden;
            line-height: 1.5;
        }

        .demo-side.show:before {
            visibility: visible;
        }

        .layui-layer-page .layui-layer-content {
            overflow: visible !important;
        }

    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <div class="layui-inline">
                    <label class="layui-form-label w-auto" style="width: unset;padding: 9px 5px 9px 5px;">名称：</label>
                    <div class="layui-input-inline mr0">
                        <input class="layui-input" id="searchValue" autocomplete="off" placeholder="请输入名称">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</button>
                    <button class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
                </div>
            </blockquote>
            <script type="text/html" id="top_toolbar">
                <a style="margin-right:5px;" href="javascript:void(0);" id="addinfo">
                    <button class="layui-btn layui-btn-sm">
                        <i class="layui-icon layui-icon-add"></i>增加
                    </button>
                </a>
            </script>
            <script type="text/html" id="tbBar">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
            <table class="layui-table" id="tree-table"></table>
        </div>
    </div>

</div>
<script>

    var jquery;
    layui.config({
        base: ctx + 'plugins/'
    }).extend({
        treeTable: 'treeTable/treeTable'
    }).use(['treeTable', 'layer', 'code', 'form', 'jquery'], function () {
        var o = layui.$,
            form = layui.form,
            layer = layui.layer,
            treeTable = layui.treeTable,
            $ = layui.$;
        var options = {
            defaultToolbar: ['filter'],
            elem: '#tree-table',
            toolbar: '#top_toolbar',
            tree: {
                iconIndex: 0,
                isPidData: true,
                idName: 'id',
                pidName: 'parentId'
            },
            reqData: function (data, callback) {  // 懒加载也可以用url方式，这里用reqData方式演示
                setTimeout(function () {  // 延迟加载一下
                    var url = ctx + 'administrativeAreaController/adminAreaJson';
                    url += '?parentId=' + ((data ? (data.id) : ''));
                    $.get(url, function (res) {
                        callback(res.data);
                    });
                }, 0);
            },
            cols: [
                [
                    {
                        field: 'mc',
                        title: '名称',
                        width: 400,
                        fixed: 'left'
                    },
                    {
                        field: 'dm',
                        title: '行政区划代码',
                        width: 180,
                        align: 'center'
                    },
                    {
                        field: 'sfyx',
                        title: '是否显示',
                        width: 100,
                        align: 'center',
                        templet: function (item) {
                            if (item.sfyx == 1) {
                                return '<input type="checkbox" value=' + item.id + ' checked="" name="open" lay-skin="switch" lay-filter="switchTest" lay-text="启用|禁用">';
                            } else {
                                return '<input type="checkbox" value=' + item.id + ' name="close" lay-skin="switch" lay-text="启用|禁用" lay-filter="switchTest">';
                            }
                        }
                    },
                    {
                        field: 'orderCode',
                        title: '排序码',
                        width: 180,
                        align: 'center'
                    },
                    // {field:'createTime', title:'创建时间'},
                    {
                        title: '操作',
                        align: 'center',
                        toolbar: '#tbBar',
                        width: 120,

                    }
                ]
            ]
        };
        var re = treeTable.render(options);
        // 工具列点击事件
        treeTable.on('tool(tree-table)', function (obj) {
            var event = obj.event;
            if (event === 'del') {
                delInfo(obj);
            } else if (event === 'edit') {
                editInfo(obj);
            }
        });
        // 监听状态操作
        form.on('switch(switchTest)', function (data) {
            var loading = layer.msg('正在更新', {icon: 16, shade: 0.1, time: 0});
            var id = data.value;
            var isShow = this.checked ? '1' : '0';
            $.ajax({
                url: ctx + 'administrativeAreaController/updateIsShow',
                type: "POST",
                async: true,
                cache: false,
                data: {
                    "id": id,
                    "sfyx": isShow
                },
                success: function (result) {
                    layer.close(loading);
                    if (result.stateType == 0) {
                        layer.msg("更新成功!", {icon: 6, skin: 'layer-ext-moon'});
                    } else {
                        layer.msg("更新失败!", {icon: 5, skin: 'layer-ext-moon'});
                    }
                },
                error: function (result) {
                    layer.close(loading);
                    layer.msg("更新失败!", {icon: 5, skin: 'layer-ext-moon'});
                }
            });
        });
        $("blockquote").on('keydown','input,select',function (e) {
            if (e.keyCode === 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                $('#search_btn').trigger("click");//触发搜索按钮的点击事件
            }
        })
        //搜索功能
        $("#search_btn").click(function () {
            //treeTable 的搜索功能
            var keywords = $.trim($('#searchValue').val());
            if (keywords == null || keywords === '') {
                layer.msg("请输入关键字进行搜索", {icon: 6});
                return;
            }
            //开启反向查找行政区划
            var parentId, data, url;
            url = ctx + 'administrativeAreaController/adminAreaJsonSearch?keyWords=' + keywords;
            $.get(url, function (res) {
                data = res.data;
                re.refresh("", data);
                re.filterData(keywords);
            })
        });
        $("#unset_Btn").click(function () {
            $('#searchValue').val("");
            re.refresh();
        })

        function delInfo(obj) {
            layer.confirm('是否确定删除所选信息？', {
                icon: 3,
                btn: ['确定', '取消'] //按钮
            }, function () {
                var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                $.ajax({
                    url: ctx + 'administrativeAreaController/deleteById',
                    type: "POST",
                    async: true,
                    cache: false,
                    data: {
                        "id": obj.data.id
                    },
                    success: function (data) {
                        layer.close(loading);
                        if (data.stateType == 0) {
                            layer.msg("删除成功!", {icon: 6, skin: 'layer-ext-moon'});
                            obj.del();
                        } else {

                            layer.msg("删除失败!", {icon: 5, skin: 'layer-ext-moon'});
                        }
                    },
                    error: function (data) {
                        layer.close(loading);
                        layer.msg("删除失败!", {icon: 5, skin: 'layer-ext-moon'});
                    }
                });
            }, function () {

            });
        }
        window.refresh=function refresh(){
            re.refresh();
        }

        //弹窗进行修改和更新操作
        $('#addinfo').click(function () {
            var url = ctx + 'administrativeAreaController/addinfo',
                title = '添加信息';
            var submitButId = '#submit';
            layer.open({
                type: 2,
                title: title,
                area: [800 + 'px', 500 + 'px'],
                fixed: false, //不固定
                maxmin: true,
                content: url,
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var submit = layero.find('iframe').contents().find(submitButId);
                    submit.click();
                    return false;
                },
                btn2: function () {
                    layer.closeAll();
                },
                zIndex: layer.zIndex, //重点1
                success: function (layero, index) {
//	             layer.setTop(layero); //重点2
//	        	 layer.iframeAuto(index);//弹窗自适应
                }
            });
        });

        function editInfo(obj) {
            var url = ctx + 'administrativeAreaController/editinfo?id=' + obj.data.id,
                title = '修改信息';
            var submitButId = '#submit';
            layer.open({
                type: 2,
                title: title,
                area: [800 + 'px', 550 + 'px'],
                fixed: false, //不固定
                maxmin: true,
                content: url,
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var submit = layero.find('iframe').contents().find(submitButId);
                    submit.click();
                    //等待提交完成之后刷新当前页面的数据
                    // setTimeout(function () {
                    //     var url = ctx + 'administrativeAreaController/getObjectById?id=' + obj.data.id;
                    //     $.get(url, function (res) {
                    //         if (res.code === 0) {
                    //             //
                    //             obj.update(res.data);
                    //         }
                    //     });
                    // }, 500);
                    return false;
                },
                btn2: function () {
                    layer.closeAll();
                },
                zIndex: layer.zIndex, //重点1
                success: function (layero, index) {
//	             layer.setTop(layero); //重点2
//	        	 layer.iframeAuto(index);//弹窗自适应
                }
            });
        }


    })


</script>


</body>
</html>
