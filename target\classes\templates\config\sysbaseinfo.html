<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>系统设置</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>
</head>
<body>
  
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
           
            <fieldset class="layui-elem-field layui-field-title">
			  <legend>基本信息</legend>
			</fieldset>
			
			
		<form class="layui-form">
			<div class="layui-form-item">
			    <label class="layui-form-label">系统名称</label>
			    <div class="layui-input-block">
			      <input type="text" name="sysName" id="sysName" th:value="${application.sysBaseInfo?.sysName}"  autocomplete="off" class="layui-input" style="width:300px">
			    </div> 
		    </div>
			<div class="layui-form-item">
				<label class="layui-form-label">英文名称</label>
				<div class="layui-input-block">
					<input type="text" name="englishName" id="englishName" th:value="${application.sysBaseInfo?.englishName}"  autocomplete="off" class="layui-input" style="width:300px">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">当前版本</label>
				<div class="layui-input-block">
					<input type="text" name="version" id="version" th:value="${application.sysBaseInfo?.version}"  autocomplete="off" class="layui-input" style="width:300px">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">关于版权</label>
				<div class="layui-input-block">
					<input type="text" name="aboutVersion" id="aboutVersion" th:value="${application.sysBaseInfo?.aboutVersion}"  autocomplete="off" class="layui-input" style="width:300px">
				</div>
			</div>
			<div class="layui-form-item layui-form-text">
			    <label class="layui-form-label">版本信息</label>
			    <div class="layui-input-block">
			      <textarea name="copyrightInfo" id="copyrightInfo" th:text="${application.sysBaseInfo?.copyrightInfo}" class="layui-textarea" style="width:400px; height:150px"></textarea>
			    </div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
				  <button class="layui-btn" lay-submit lay-filter="dosubmit"><i class="layui-icon layui-icon-ok"></i>确定</button>
				</div>
		  	</div>
		</form>
		<script>
			$(function () {

            });
			layui.use(['form','layer','jquery'] , function(){
	  		var $ = layui.jquery; 
    		var layer = layui.layer;
    		var form = layui.form;
    		var upload = layui.upload;

			form.on("submit(dosubmit)",function(data){
				var saveUrl = ctx + 'configController/saveSysBaseInfo',
				   data = data.field;
				   $.ajax({
				        url:saveUrl,       //提交表单的地址
				        data:data,      //提交表单的数据
					   success: function (res) {
						   if (res.stateType == 1) {
							   layer.msg("保存失败", {icon: 2});
						   } else {
							   var confirm = layer.confirm("保存成功", {
								   icon: 1,
								   btn: ["确定"],
								   title: "提示"
							   }, function () {
								   layer.close(confirm);
								   top.$("#sysTitle").html($("#sysName").val());
								   top.$("#sysTitle1").html($("#sysName").val());
							   }, function () {
								   // alert("取消")
							   });
						   }
					   },
				        error:function(){
				        }
				   });
				return false;
			});
 		});
		</script>
				
			  </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  

</body></html>