<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IExceptionLogDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fwy.corebasic.entity.ExceptionLog">
        <result column="ID" property="id" />
        <result column="REQU_PARAM" property="requParam" />
        <result column="NAME" property="name" />
        <result column="MESSAGE" property="message" />
        <result column="USER_ID" property="userId" />
        <result column="USER_NAME" property="userName" />
        <result column="METHOD" property="method" />
        <result column="URI" property="uri" />
        <result column="IP" property="ip" />
        <result column="VER" property="ver" />
        <result column="CREATE_TIME" property="createTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, REQU_PARAM, NAME, MESSAGE, USER_ID, USER_NAME, METHOD, URI, IP, VER, CREATE_TIME
    </sql>
    <insert id="save" useGeneratedKeys="true">
        insert into exception_log
        VALUES (
        #{id},
        #{requParam},
        #{name},
        #{message},
        #{userId},
        #{userName},
        #{method},
        #{uri},
        #{ip},
        #{ver},
        #{createTime}
        )
    </insert>
</mapper>
