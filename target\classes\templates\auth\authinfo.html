<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>权限管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .label-width {
            width: 100px;
        }

        .input-width {
            width: 75% !important;
        }
    </style>
</head>
<body>

<div id="" class="layui-layer-content" style="overflow: visible;">
    <form id="modelAuthForm" lay-filter="modelAuthForm" class="layui-form">
        <div class="layui-form-item">
            <input name="id" id="id" type="hidden" th:value="${authInfo?.id}">
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span style="color:red">*</span>权限名称</label>
            <div class="layui-input-inline input-width">
                <input name="authName" type="text" class="layui-input" maxlength="50" placeholder="请输入权限名称"
                       lay-vertype="tips" oninput="del(this,'blank|char')" lay-verify="required|authName"
                       th:value="${authInfo?.authName}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span style="color:red">*</span>所属动作列表</label>
            <div class="layui-input-inline">
                <input name="actionNames" id="actionNames" type="hidden" th:value="${actionNames}">
                <div class="xm-select" id="actionValues">
                </div>
            </div>
        </div>
        <button style="display:none" lay-submit lay-filter="subBtn" id="subBtn"></button>
    </form>
</div>
<script>


    layui.use('form', function () {
        var form = layui.form;
        form.verify({
            authName: function (value, item) {
                if (top.window.parent.getBytes(value) > 20) {
                    return '权限名称不能超过' + 20 + '个字符的长度';
                }
            }
        });
        //监听提交
        form.on('submit(subBtn)', function (data) {
            var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time: 0});
            data.field.actionNames = dialog_action.getValue('nameStr');
            $.ajax({
                url: ctx + 'authController/saveAuth',
                method: 'post',
                data: data.field,
                dataType: 'JSON',
                success: function (res) {
                    if (res.code != 0) {
                        layer.close(loading);
                        layer.alert(res.msg, {icon: 2});
                    } else {
                        //关闭弹出层
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        parent.layer.msg(res.msg, {icon: 1});
                        //父级页面表格重载
                        parent.layui.table.reload("auth-table");
                    }
                },
                error: function (res) {
                    layer.close(loading);
                    layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                }
            });
            return false;
        });
    })

    function reloadXmselect() {
        //动作列表
        jQuery.get(ctx + 'authController/actionTree?id=' + $("#id").val(), function (res) {
            dialog_action.update({
                data: res.data,
            })
        });

    }

    jQuery(function () {
        reloadXmselect();
    });
    //动作列表 下拉框初始化
    var dialog_action = xmSelect.render({
        el: '#actionValues',
        filterable: true,
        name: 'actionValues',
        tips: '请选择',
        // layVerify: 'required|uniqueDept',
        layVerify: 'required',
        layVerType: 'msg',
        model: {
            label: {
                type: 'block', block: {
                    //最大显示数量, 0:不限制
                    showCount: 3,
                    //是否显示删除图标
                    showIcon: true,
                }
            }
        },
        template: function (item) {
            // alert(JSON.stringify(item.name))
            return item.name + '<span style="position: absolute; right: 0; color: #A0A0A0; font-size: 12px;">' + item.value + '</span>';
        },
        on: function (data) {
            //arr:  当前多选已选中的数据
            var arr = data.arr;
            //change, 此次选择变化的数据,数组
            var change = data.change;
            //isAdd, 此次操作是新增还是删除
            var isAdd = data.isAdd;
            // if (isAdd){
            //
            //    var value = dialog_dept.getValue();
            //     console.log(value)
            // }

            // alert('已有: ' + arr.length + ' 变化: ' + change.length + ', 状态: ' + isAdd)
        },
        // cascader: {
        //     //是否显示级联模式
        //     show: true,
        //     //间距
        //     indent: 200,
        //     //是否严格遵守父子模式
        //     strict: true,
        // },
        // showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
        // tree //开启树结构
        radio: false,//单选多选
        tree: {
            show: true,
            strict: true, //是否父子结构，父子结构父节点不会被选中
            indent: 30,//间距
            expandedKeys: [-1],
            clickCheck: true,
            clickExpand: true,//点击展开
        },
        toolbar: { //开启工具栏
            show: true,
            list: ['ALL', 'CLEAR'],
        },
        clickClose: false,//点击关闭
        autoRow: true,
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width: '513px'
        },
        prop: {
            name: "display",
            value: "controllerAction"
        },
        height: '200px',
        empty: '暂无数据',
        data: [],
        direction: 'auto',
    });

</script>
</body>
</html>