<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.dic.dao.DataTypeDao">
     
     <select id="findAll" resultType="com.fwy.dic.entity.DataType">
         select ID,
                DICTYPE,
                DICCODE,
                ORDERCODE,
                date_format(CREATETIME, '%Y-%m-%d') as createtime,
                date_format(UPDATETIME, '%Y-%m-%d') as updatetime,
                ISSTART,
                REMARKS
         from dic_datatype
         <where>
             <if test="dicType != null">
                 DICTYPE like concat('%',#{dicType},'%')
             </if>
         </where>

         order by ORDERCODE
     </select>
     
     <insert id="addDataType" parameterType="com.fwy.dic.entity.DataType" useGeneratedKeys="true">
         insert into dic_datatype (ID, DICTYP<PERSON>, DICCODE, ORDERCODE, CREATETIME,
                                   UPDATETIME, ISSTART, REMARKS)
         values (#{id}, #{dicType}, #{dicCode}, #{orderCode}, now(), now(),
                 #{isStart},
                 #{remarks,jdbcType=VARCHAR})
     </insert>
     
     <update id="updateDataType" parameterType="com.fwy.dic.entity.DataType">
          update dic_datatype
             <set>
		          <if test="dicType != null">
		             DICTYPE = #{dicType},
		          </if>
		          <if test="dicCode != null">
		              DICCODE = #{dicCode},
		          </if>
		       <!--    <if test="orderType != null">
		               ordertype = #{orderType},
		          </if> -->
		          <if test="isStart != null">
		              ISSTART = #{isStart},
		           </if>
		           <if test="remarks != null">
			          REMARKS =  #{remarks},
			       </if>
		           UPDATETIME = now()
		      </set>
		      where ID = #{id}
     </update>
     
     <!--<delete id="deleteDic_DateType" parameterType="com.fwy.dic.entity.DataType">
            delete from DIC_DATATYPE 
              where id = #{id}
     </delete>-->
      <delete id="delDataTypedetailByDicCode">
            delete from dic_datadetail
              where DICCODE = #{dicCode}
     </delete>
     <select id="findById" resultType="com.fwy.dic.entity.DataType">
         select * from dic_datatype where id = #{id}
     </select>

     <select id="getMaxOrderCode" resultType="String">
    	 select Max(ORDERCODE) from DIC_DATATYPE
     </select>

     <delete id="deleteDataType" parameterType="Long">
        delete from dic_datatype
        where ID = #{id}
     </delete>
     
     <update id="changeStart" parameterType="com.fwy.dic.entity.DataType">
         update dic_datatype
         <set>
            ISSTART = #{isStart}
         </set>
         where ID = #{id}
     </update>
     
     <select id="uniqueData" resultType="com.fwy.dic.entity.DataType">
         select ID,DICCODE from dic_datatype where DICCODE = #{dicCode}
     </select>
     
     <select id="findAllDicCode" resultType="String">
        select DICCODE from dic_datatype
        group by DICCODE
     </select>

    <select id="uniqueDicType" resultType="com.fwy.dic.entity.DataType">
        select DICTYPE,ID from dic_datatype
        where DICTYPE = #{dicType}
    </select>

    <select id="selectByCodeCount" resultType="java.lang.Integer">
        select count(1) from dic_datatype
        where DICCODE = #{dicCode} AND ISSTART = 1
    </select>
</mapper>