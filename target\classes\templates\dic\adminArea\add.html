<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
>
<head>
    <meta charset="UTF-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>

    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style type="text/css">
        .layui-layer-page .layui-layer-content {
            overflow: visible !important;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title"
                      style="margin-top: 20px;">
                <legend>行政区划信息</legend>
            </fieldset>

            <form class="layui-form" action="">
                <input type="hidden" name="id" th:value="${administrativeArea==null?'':administrativeArea.id}"/>
                <input type="hidden" id="orderCode"
                       th:value="${administrativeArea==null?'':administrativeArea.orderCode}"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>名称：</label>
                        <div class="layui-input-inline">
                            <input oninput="del(this,'blank|char')" type="tel" name="mc" lay-verify="required|mcLength"
                                   autocomplete="off" class="layui-input" placeholder="请输入区划名称"
                                   th:value="${administrativeArea==null?'':administrativeArea.mc}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 110px"><span
                                style="color:red">*</span>行政区划代码：</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="dm" oninput="del(this,'blank|char')" lay-verify="required|number" maxlength="6"
                                   autocomplete="off" class="layui-input" placeholder="请输入区划代码"
                                   th:value="${administrativeArea==null?'':administrativeArea.dm}">
                        </div>
                    </div>

                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">上级区县：</label>

                        <div class="layui-input-inline">
                            <input id="parentId" type="hidden" th:value="${administrativeArea?.parentId}">
                            <div class="xm-select" id="parentIdSelect">
                            </div>
                        </div>

                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-hide" lay-submit="" id="submit" lay-filter="demo1">保存</button>

                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'layedit', 'laydate', 'jquery'],
        function () {
            var form = layui.form, layer = layui.layer, layedit = layui.layedit, laydate = layui.laydate,
                $ = jQuery = layui.$;

            form.verify({
                mcLength : function(value){
                    if(top.window.parent.getBytes(value) > 50){
                        return "行政区划名称大于50个字符";
                    }
                }
            })

            //监听提交
            form.on('submit(demo1)', function (data) {
                $.ajax({
                    type: "POST",
                    url: ctx + 'administrativeAreaController/saveAdminArea',
                    data: data.field,
                    dataType: "json",
                    success: function (data) {
                        console.log(data)
                        if (data.code == 0) {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            parent.window.refresh();
                            parent.layer.alert(data.msg, {
                                icon: 6,
                                skin: 'layer-ext-moon' //样式类名
                            });
                        } else {
                            parent.layer.msg(data.msg, {
                                icon: 5,
                                skin: 'layer-ext-moon'
                            });
                        }
                    },
                    error: function (data) {
                        parent.layer.msg(data.msg, {
                            icon: 5,
                            skin: 'layer-ext-moon'
                        });
                    }
                });
                return false;
            });


            function reloadXmselect() {
                $.ajax({
                    url: ctx + 'administrativeAreaController/selectTreeJson',
                    data: {"id": $("#parentId").val()},
                    method: "get",
                    dataType: 'json',
                    success: function (response) {
                        dialog_Admin.update({
                            data: response.data
                        })
                    },
                    error: function (res) {
                    }
                });
            }

            $(function () {
                reloadXmselect();
            });
            //上级行政区划 下拉框初始化
            var dialog_Admin = xmSelect.render({
                el: '#parentIdSelect',
                filterable: true,
                name: 'parentId',
                tips: '请选择',
                // layVerify: 'required|uniqueDept',
                // layVerify: 'required',
                // layVerType: 'msg',
                model: {label: {type: 'block'}},
                template: function(item) {
                    // alert(JSON.stringify(item.name))
                    return '<p title="' + item.name + '">' + item.name + '</p>';
                },
                on: function (data) {
                    //arr:  当前多选已选中的数据
                    var arr = data.arr;
                    //change, 此次选择变化的数据,数组
                    var change = data.change;
                    //isAdd, 此次操作是新增还是删除
                    var isAdd = data.isAdd;
                    // if (isAdd){
                    //
                    //    var value = dialog_dept.getValue();
                    //     console.log(value)
                    // }

                    // alert('已有: ' + arr.length + ' 变化: ' + change.length + ', 状态: ' + isAdd)
                },
                // cascader: {
                //     //是否显示级联模式
                //     show: true,
                //     //间距
                //     indent: 200,
                //     //是否严格遵守父子模式
                //     strict: true,
                // },
                // showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
                // tree //开启树结构
                radio: true,//单选多选
                tree: {
                    show: true,
                    strict: false, //是否父子结构，父子结构父节点不会被选中
                    indent: 30,//间距
                    expandedKeys: [-1],
                    clickCheck: true,
                    clickExpand: true,//点击展开
                },
                clickClose: false,//点击关闭
                autoRow: true,
                style: {
                    paddingLeft: '10px',
                    position: 'relative',
                    width: '528px'
                },
                prop: {
                    name: "mc",
                    value: "id"
                },
                height: '200px',
                empty: '暂无数据',
                data: [],
                direction: 'down',//下拉
            });


        });


</script>
</body>
</html>