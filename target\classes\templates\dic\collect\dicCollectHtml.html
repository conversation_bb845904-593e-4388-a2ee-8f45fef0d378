<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>字典汇总表管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <script>
        var loading = layer.load(1, {
            shade: [0.1,'#fff'] //0.1透明度的白色背景
        });
    </script>
</head>
<body>  
  <div class="layui-fluid">
        <div class="layui-card">
          <div class="layui-card-body">
          <form class="layui-form" action="">
              <div class="layui-form-item" style="background-color:#eee;padding: 10px; margin-bottom: 0px;border-left: 5px solid #009688;">
                  <div class="layui-inline">
                      <label class="layui-form-label">代码</label>
                      <div class="layui-input-inline">
                          <input id="itemcode" class="layui-input" type="text" autocomplete="off" placeholder="请输入代码">
                      </div>
                  </div>
                  <div class="layui-inline">
                      <label class="layui-form-label">名称</label>
                      <div class="layui-input-inline">
                          <input id="itemname" class="layui-input" type="text" autocomplete="off" placeholder="请输入名称">
                      </div>
                  </div>
                  <div class="layui-inline">
                      <label class="layui-form-label">字典表名</label>
                      <div class="layui-input-inline">
                          <input id="dicName" class="layui-input" type="text" autocomplete="off" placeholder="请输入字典表名称">
                      </div>
                  </div>
                  <div class="layui-inline">
                      <div class="layui-input-block">
                          <button id="searchBtn" lay-event="search_btn" class="layui-btn">
                              <i class="layui-icon">&#xe615;</i>查询</button>
                          <button type="reset" class="layui-btn layui-btn-primary" id="unsetBtn">
                              <i class="layui-icon">&#xe669;</i>重置</button>
                      </div>
                  </div>
              </div>
			</form>
		<table class="layui-hide" id="collect-table" lay-filter="collect-table"></table>
        <script type="text/html" id="table-toolbar-top">
              <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe608;</i>增加</button>
                  <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="export" id="export"><i class="layui-icon">&#xe601;</i>导出</button>
              </div>
        </script>
        <script type="text/html" id="table-toolbar">
			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="oneDel">删除</a>
        </script> 
      </div>
    </div>
  </div>
  <script>
      layui.config({
          base: ctx+'plugins/soulTable/'
      }).extend({
          soulTable: 'soulTable'
      });

      layui.use(['table','layer','jquery', 'form', 'soulTable'] , function(){
          var admin = layui.admin;
          var $ = layui.jquery;
          var layer = layui.layer;
          var form = layui.form;
          var table = layui.table;
          var soulTable = layui.soulTable;
          var myTable=table.render({
              elem: '#collect-table'
              ,url: ctx + 'dicCollectController/dicCollectJson'
              ,defaultToolbar: ['filter', 'print']
              ,toolbar: '#table-toolbar-top'
              ,title: '字典汇总表'
              ,cols: [
                  [
                      {type: 'checkbox'}
                      ,{field: 'id', title: 'ID',align:'center',hide:true}
                      ,{title:'操作',width:120,toolbar: '#table-toolbar', align: 'center'}
                      ,{field: 'itemCode', title: '代码', width:200,align: 'center'}
                      ,{field:'itemName', title:'名称',width:300, align: 'center'}
                      ,{field:'dicName', title:'字典表名', align: 'center'}
                  ]
              ]
              ,request: {
                  pageName: 'pageNum' //页码的参数名称，默认：page
                  ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
              }
              ,parseData: function(res){ //res 即为原始返回的数据
                  return {
                      "code": res.code, //解析接口状态
                      "msg": res.msg, //解析提示文本
                      "count": res.data.total, //解析数据长度
                      "data": res.data.list //解析数据列表
                  }
              }
              ,page: true
              ,done: function () {
                  soulTable.render(this);
                  layer.close(loading);
              }
          });
          form.render();

          //搜索及重置按钮
          $("#searchBtn").click(function(){
              var itemname =$("#itemname").val();
              var itemcode =$("#itemcode").val();
              var dicName =$("#dicName").val();
              table.reload('collect-table',{
                  where: { //设定异步数据接口的额外参数，任意设
                      itemname: itemname,
                      itemcode: itemcode,
                      dicName: dicName
                  }
                  ,page: {
                      curr: 1 //重新从第 1 页开始
                  }
              });
              return false;
          })
          //搜索及重置按钮
          $("#unsetBtn").click(function(){
              location.reload();
              table.reload('collect-table', {
                  where: null
                  ,page: {
                      curr: 1 //重新从第 1 页开始
                  }
              }); //只重载数据
              return false;
          })
          //监听行工具事件
          table.on('tool(collect-table)', function(obj){
              var data = obj.data;
              var id = data.id;
              var dicName = data.dicName;

              if(obj.event === 'edit'){
                  layer.open({
                      type: 2,
                      title: "编辑字典汇总表",
                      shadeClose: true,
                      area: ['70%', '75%'],
                      btn: ['保存','关闭'],
                      content: ctx + 'dicCollectController/collectEditHtml?id='+ id,
                      yes: function(index, layero){ //当前层索引、当前层DOM对象
                          //提交表单
                          var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                          submit.click();// 触发提交监听
                          return false;
                      }
                  });
              }
              if(obj.event === 'oneDel'){
                  layer.confirm('是否删除',{btn:['确定','取消'],title:'提示'},function(){
                      var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
                      $.ajax({
                          method:"POST",
                          url : ctx + 'dicCollectController/oneDelCollect',
                          data : {"id":id,"dicName":dicName},
                          async : true,
                          cache : false,
                          success : function(res){
                              layer.close(loading);
                              if(res.stateType == 0){
                                  table.reload("collect-table");
                                  layer.msg(res.stateMsg,{icon: 1});
                              }else{
                                  layer.msg(res.stateMsg,{icon: 2});
                              }
                          },
                          error : function(res){
                              layer.close(loading);
                              layer.msg('操作失败',{icon: 2});
                          }
                      })
                  })
              }
          });

          //头工具栏事件
          table.on('toolbar(collect-table)', function(obj){
              var checkStatus = table.checkStatus(obj.config.id);
              var data = JSON.stringify(checkStatus.data);
              switch(obj.event){
                  case 'add':
                      layer.open({
                          type: 2,
                          title: "增加字典汇总",
                          shadeClose: true,
                          area: ['70%', '75%'],
                          btn: ['保存','关闭'],
                          content: ctx + 'dicCollectController/collectAddHtml',
                          yes: function(index, layero){ //当前层索引、当前层DOM对象
                              //提交表单
                              var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                              submit.click();// 触发提交监听
                              return false;
                          }
                      });
                      break;
                  //导出excel
                  case 'export':
                      soulTable.export(myTable, {
                          filename: '字典汇总表.xlsx',
                          head:{ // 表头样式
                              family: 'Calibri', // 字体
                              size: 14, // 字号
                              color: '009688', // 字体颜色
                              bgColor: 'C7C7C7' // 背景颜色
                          }
                      });
                      break;
              };
          });
      });
  </script>
</body>
</html>