<!DOCTYPE html >
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="UTF-8">
    <title>部门参数配置列表</title>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/security/main/openWay.js}" charset="utf-8"></script>
    <style>
        #search{
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <blockquote class="layui-elem-quote quoteBox" id="search">

                        <div class="layui-inline layui-form">
                            <label class="layui-form-label">部门名称：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" id="deptName" autocomplete="off" placeholder="请输入关键字">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</button>
                            <button type="reset" class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
                        </div>
                    </blockquote>
                    <table class="layui-hide" id="deptParamsTable" lay-filter="deptParamsTable"></table>

                    <script type="text/html" id="topToolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i class="layui-icon">&#xe608;</i>增加</button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i class="layui-icon">&#xe642;</i>编辑</button>
                            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i class="layui-icon">&#xe640;</i>删除</button>
                        </div>
                    </script>

                    <script type="text/html" id="activeBar">
                        <a class="layui-btn layui-btn-xs"  lay-event="edit">编辑</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    </script>

                    <script type="text/html" id="activeToolbar">
                        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    </script>

                </div>
            </div>
        </div>
    </div>
</div>
    <script th:inline="javascript">
        layui.use(['layer', 'element', 'table', 'form','laydate'], function () {
            let admin = layui.admin,
                table = layui.table;
            let layer = layui.layer,
                form = layui.form,
                element = layui.element;
            let $ = layui.$;
            form.render();
            table.render({
                elem: '#deptParamsTable'
                ,url: ctx + '/deptParamController/findDeptParamsList'
                ,toolbar: '#topToolbar'
                ,defaultToolbar:[]
                ,height:'full-50'
                ,cols: [
                    [
                        {type: 'checkbox', fixed: 'left'}
                        ,{field:'id', title:'ID', hide: true}
                        ,{field:'deptName', title:'部门名称',merge:true,align:'center', width:220}
                        ,{field:'address', title:'部门地址',align:'center', width:180}
                        ,{field:'everyDayTotalNumber', title:'单日放号总量',align:'center', width:120}
                        ,{field:'everyOneTakeNumber', title:'单人单日可取号数',align:'center', width:140}
                        ,{field:'timeModelName',title:'时间模板',align:'center',width:120,templet:function(data){
                            if(data.timeModelName == null){
                                return '---'
                            }else{
                                return   data.timeModelName;
                            }
                        }}
                        ,{field:'createTime',title:'创建时间',align:'center'}
                    ]
                ]
                ,request: {
                    pageName: 'pageNum' //页码的参数名称，默认：page
                    ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
                }
                ,limit:20
                ,parseData: function(res){ //res 即为原始返回的数据
                    console.log(res);
                    return {
                        "code": res.code, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.data.total, //解析数据长度
                        "data": res.data.list //解析数据列表
                    }
                }
                ,page: true
            });
            let active = {
                reload:function(){
                    let deptName = $("#deptName").val();
                    table.reload('deptParamsTable',{
                        page:{
                            curr:1
                        },
                        where:{deptName:deptName}
                    })
                }
            };
            //头工具栏事件
            table.on('toolbar(deptParamsTable)', function(obj){
                let checkStatus = table.checkStatus(obj.config.id);
                let data = checkStatus.data,
                    width=870,
                    height=400,
                    subButton='#submitBut';
                switch(obj.event){
                    case 'add_btn':
                        let url = ctx + '/deptParamController/goAddDeptParams';
                        let title = '新增部门配置参数';
                        xadmin.openWindowFull(url,title,subButton);
                        break;
                    case 'edit_btn':
                        if(data.length == 1){
                            let url = ctx + '/deptParamController/editDeptParam?id='+data[0].id;
                            let title = '编辑部门配置参数';
                            xadmin.openWindowFull(url,title,subButton);
                        }else{
                            layer.msg('请选择一条数据')
                        }
                        break;
                    case 'delete_btn':
                        if(data.length == 1){
                            let url = ctx + '/deptParamController/deleteDeptParams?id=' + data[0].id;
                            xadmin.deleteDemo(url,'deptParamsTable');
                        }else{
                            layer.msg('请选择一条数据');
                        }
                        break;
                }
            });
            table.on('tool(deptParamsTable)', function(obj){
                let data = obj.data;
                if(obj.event === 'del'){
                    // 删除函数
                    let url = ctx + '/deptParamController/deleteProcess?id=' + data.id;
                    xadmin.deleteDemo(url,table,'deptParamsTable');
                } else if(obj.event === 'edit'){
                    let urlstr = ctx + '/deptParamController/editPayConfig?id='+data.id;
                    let tit = '编辑支付管理数据',width=870,height=400,subButton='#submitBut';
                    xadmin.openWindow(urlstr,tit,width,height,subButton);
                }
            });
            $("#search_btn").click(function(){
                console.log(11);
                let type = 'reload';
                active[type] ? active[type].call(this) : '';
            });
            $("#unset_Btn").click(function(){
                $(":input").val("");
                table.reload('deptParamsTable', {
                    where: null
                    ,page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
            });
            $(document).keydown(function(event){
                if(event.keyCode == 13){
                    let type = 'reload';
                    active[type] ? active[type].call(this) : '';
                }
            });
        });
    </script>
</body>
</html>