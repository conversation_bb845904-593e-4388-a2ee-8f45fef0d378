_jsload2&&_jsload2('pcommon', 'function mi(a){this.P=a;this.qw=[];this.Of=s;this.Ll=new ec(s,{of:"api"});this.Fz()}var ni={none:["-1",""],transit:["0","m_transit.png"],hotel:["1","m_hotel.png"],catering:["2","m_catering.png"],movie:["3","m_movie.png"],transit:["4","m_transit.png"],indoor_scene:["5","m_indoor_scene.png"]};B.Tm(function(a){new mi(a)}); z.extend(mi.prototype,{Fz:function(){var a=this;pano=a.P;pano.addEventListener("visible_poi_type_changed",function(b){a.L_(b.visiblePOIType)});pano.addEventListener("position_changed",function(){a.Of&&a.$I()})},L_:function(a){this.Of=a;this.$I()},ZQ:function(){for(var a=this.qw.length-1;0<=a;a--)this.P.Qb(this.qw[a]);this.qw=[]},$I:function(){this.ZQ();if("inter"!==pano.ft()){var a=ni[this.Of],b=this.P.ga(),c=this.P.Xb(),e=this;this.Ll.lE(b,200,a[0],function(b){var g=e.P.Xb();if(g===c)for(var i=s, i=s,k=0,m=b.length;k<m;k++)if(i=b[k],15>k||i.panoInfo&&i.panoInfo.panoId==g)i=new he(i.position,{icon:G.qa+"panorama/"+a[1],title:i.title,altitude:i.altitude,panoInfo:i.panoInfo}),e.P.Ga(i),e.qw.push(i)})}}});function bj(a,b){this.P=a;this.C=b;this.Kq=s;this.Ll=new ec(s,{of:"api"});this.nB=[];this.ls();this.$l()}B.Tm(function(a){function b(){var b=a.ft();a.j.indoorSceneSwitchControl==t||"street"==b?c&&c.U():"inter"==b&&(c||(c=new bj(a,a.C)),c.E0(a.Xb()),c.show())}a.addEventListener("scene_type_changed",b);a.addEventListener("indoor_default_switch_mode_changed",b);var c=s}); z.extend(bj.prototype,{$l:function(){this.xQ();this.Fz()},Mp:function(a){function b(){e.P.Pc({heading:c.heading,pitch:c.pitch});e.P.removeEventListener("position_changed",b)}var c=this.nB[a];this.P.vc(c.panoId);var e=this;this.P.addEventListener("position_changed",b)},next:function(){this.Mp(++this.Kq)},QZ:function(){this.Mp(--this.Kq)},ls:function(){var a=this.MJ=L("div"),b=a.style;b.position="absolute";b.zIndex="2000";b.width="100%";b.top=b.left="0px";this.C.appendChild(a);this.kg=L("a");this.kg.style.left= "2%";this.kg.href="javascript: void(0);";this.kg.className="pano_switch_left";a.appendChild(this.kg);this.Qh=L("a");this.Qh.style.right="2%";this.kg.href="javascript: void(0);";this.Qh.className="pano_switch_right";H()&&(this.Qh.style.height="34px",this.Qh.style.width="34px",this.Qh.style.borderRadius="17px",this.kg.style.height="34px",this.kg.style.width="34px",this.kg.style.borderRadius="17px");a.appendChild(this.Qh);this.kg.style.top=this.Qh.style.top=this.P.fh().height/2-14+"px"},E0:function(a){var b= this,c=this.P.Xb();this.Ll.Kx(a,function(a){var f=b.P.Xb();f===c&&(b.nB=a,b.dP(f))})},U:function(){this.Qa=t;this.MJ.style.display="none"},show:function(){this.Qa=q;this.MJ.style.display="block"},jh:w("Qa"),dP:function(a){for(var b=this.nB,c=b.length-1;0<=c;c--)b[c].panoId==a&&(this.Kq=c);z.D.Pb(this.kg,"pano_switch_disable");z.D.Pb(this.Qh,"pano_switch_disable");1===this.Kq?z.D.Ua(this.kg,"pano_switch_disable"):this.Kq==b.length-1&&z.D.Ua(this.Qh,"pano_switch_disable")},xQ:function(){var a=this; ia.M(this.kg,"click",function(b){z.D.it(b.target,"pano_switch_disable")||a.QZ()});ia.M(this.Qh,"click",function(b){z.D.it(b.target,"pano_switch_disable")||a.next()})},Fz:function(){var a=this,b=a.P;b.addEventListener("position_changed",function(){if(a.jh()){var c=b.Xb();a.dP(c)}})}});function cj(a){de.call(this);this.P=a;this.Ll=new ec(s,{of:"api"});this.Iq={admission:"",photoDate:"",roadName:"",providerUrl:"",providerName:""};this.Tr=s;this.Dr=[];this.$A=q;this.na()}B.Tm(function(a){new cj(a)});z.lang.ta(cj,de,"PanoramaCopyright"); z.extend(cj.prototype,{na:function(){this.C=this.Jj(1900);this.P.La();this.P.La().appendChild(this.C);var a=this;this.Ll.CL(function(b){a.Dr=b;a.Aa()});this.ba(this.P);this.P.j.copyrightControlOptions.logoVisible==t&&this.Px()},Aa:function(){var a=this.rS(),b=[];b.push(\'<div style="width: 1000px; overflow:hidden;">\');if(this.$A){b.push(\'<a target="_blank" title="\\u5230\\u767e\\u5ea6\\u5730\\u56fe\\u67e5\\u770b\\u6b64\\u533a\\u57df" href=\'+this.P.yL()+\' style="outline:none;float:left;margin-left:3px">\');var c= G.qa+"copyright_logo.png";H()?(c=G.qa+"copyright_logo_hd.png",b.push("<img style=\'border:none;width:68px;height:25px;vertical-align:bottom;\' src=\'"+c+"\' />")):6==z.ca.ia?b.push("<div style=\'float: left;cursor:pointer;width:77px;height:32px;filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="+c+")\'></div>"):b.push("<img style=\'border:none;width:77px;height:32px;vertical-align:bottom;\' src=\'"+c+"\' />");b.push("</a>")}b.push(\'<div style="float:left;\');H()?b.push(\'margin-top:8px;font-family:sans-serif;">\'): (b.push("margin-top:13px;"),z.platform.AM?b.push(\'font-family:sans-serif;">\'):b.push(\'">\'));a.roadName&&b.push(\'<span style="margin-left: 5px;">\'+a.roadName+"</span><span>|</span>");a.providerName&&(a.providerUrl?b.push(\'<span style="margin-left: 5px;">Data \\u00a9</span><span><a style="text-decoration: none;color: rgb(54, 54, 54);" target="_blank" href="\'+a.providerUrl+\'" style="display:inline;">\'+a.providerName+"</a></span>"):b.push(\'<span style="margin-left: 5px;">Data \\u00a9</span><span>\'+a.providerName+ "</span>"));b.push(\'<span style="margin-left: 5px;">\'+a.admission+"</span>");b.push(\'<span style="margin-left: 5px;">\\u62cd\\u6444\\u65e5\\u671f:\'+a.photoDate+"</span>");b.push("</div>");b.push("</div>");this.C.innerHTML=b.join("")},Jj:function(a){var b=L("div"),c=b.style;c.overflow="hidden";c.position="absolute";c.bottom=c.left="0";c.zIndex=a||"0";c.width="100%";c.fontSize="11px";c.height=H()?"27px":"34px";c.x5="none";c.WebkitTextSizeAdjust="none";c.WebkitUserSelect="none";c.visibility="hidden";c.fontFamily= "sans-serif";c.color="rgb(54, 54, 54)";c.lineHeight="20px";return b},ba:function(a){var b=this;a.addEventListener("copyright_changed",function(a){a.copyright&&(b.Tr=a.copyright,b.Aa())});a.addEventListener("visible_changed",function(){b.C.style.visibility=a.kE()?"visible":"hidden"});a.addEventListener("copyright_options_changed",function(){a.j.copyrightControlOptions.logoVisible==t?b.Px():b.NF()})},rS:function(){if(this.Tr){for(var a in this.Iq)this.Iq[a]=this.Tr[a];a=this.aY(this.Tr.dataProviderIndex); this.Iq.providerName=a.name+this.Tr.username;this.Iq.providerUrl=a.url}return this.Iq},aY:function(a){for(var b={name:"",url:"",id:""},c=this.Dr.length-1;0<=c;c--)if(this.Dr[c].id==a){var b=this.Dr[c],e;for(e in b)b[e]=z.trim(this.Dr[c][e])}return b},Px:function(){/baidu\\.com/.test(document.domain)&&(this.$A=t,this.Aa())},NF:function(){this.$A=q;this.Aa()}}); ');