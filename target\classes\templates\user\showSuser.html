<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	  xmlns:http="http://www.w3.org/1999/xhtml"
	  xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
	<meta charset="utf-8">
	<title>用户信息</title>
	<div th:replace="Importfile::html"></div>
	<script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
	<link rel="stylesheet"
		  th:href="@{/plugins/formSelects/formSelects-v4.css}" />
	<style>

		.layui-form-label{
			width: 150px;
		}
		.layui-input-inline{
			width: 250px;
		}
		.layui-input{
			width: 240px;
		}
	</style>
</head>
<script th:inline="javascript" >
	$(function () {
		$("#password").val('');

		var yhyxqsStr = [[${yhyxqs}]];
		var yhyxqStr = [[${yhyxq}]];
		var mmyxqsStr = [[${mmyxqs}]];
		var mmyxqStr = [[${mmyxq}]];

		if(yhyxqsStr==null || yhyxqsStr == ''){
			// 用户有效期始默认当天
			var date=new Date();
			$("#yhyxqs").val(formatDate(date));
		}

		if(yhyxqStr==null || yhyxqStr == ''){
			// 用户有效期止默认30天
			var date=new Date();
			date.setDate(date.getDate()+30);
			$("#yhyxq").val(formatDate(date));
		}

		if(mmyxqsStr==null || mmyxqsStr == ''){
			// 密码有效期始默认当天
			var date=new Date();
			$("#mmyxqs").val(formatDate(date));
		}

		if(mmyxqStr==null || mmyxqStr == ''){
			// 密码有效期止默认15天
			var date=new Date();
			date.setDate(date.getDate()+15);
			$("#mmyxq").val(formatDate(date));
		}


	})
	function formatDate(date) {
		var y = date.getFullYear();
		var m = date.getMonth() + 1;
		m = m < 10 ? '0' + m : m;
		var d = date.getDate();
		d = d < 10 ? ('0' + d) : d;
		return y + '-' + m + '-' + d;
	};
</script>
<body>
<div class="layui-fluid">
	<div class="layui-card">
		<div class="layui-card-body">
			<fieldset class="layui-elem-field layui-field-title"
					  style="margin-top: 20px;">
				<legend>用户信息</legend>
			</fieldset>

			<form class="layui-form" action="">
				<input type="hidden" name="id" id="id" th:value=${user?.id}>
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>用户名：</label>
						<div class="layui-input-inline">
							<input type="text" name="userName" lay-verify="required|unique" th:if="${user!=null}"
								   placeholder="请输入用户名" autocomplete="off" th:value="${user?.userName}" class="layui-input" readonly >
						</div>
					</div>
					<div class="layui-inline" th:if="${user==null}">
						<label class="layui-form-label"><span style="color:red">*</span>密码：</label>
						<div class="layui-input-inline">
							<input type="password" name="password" lay-verify="required|passwordLength"
								   class="layui-input">
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>民警姓名：</label>
						<div class="layui-input-inline">
							<input type="text" name="fullName" lay-verify="required|fullnameLength"
								   placeholder="请输入民警姓名" autocomplete="off" th:value="${user?.fullName}" class="layui-input" readonly>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>身份证号：</label>
						<div class="layui-input-inline">
							<input type="text" name="idCardNumber" lay-verify="required|identity|uniqueCard"
								   placeholder="请输入身份证号" autocomplete="off" th:value="${user?.idCardNumber}" class="layui-input" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>警员编号(员工编号)：</label>
						<div class="layui-input-inline">
							<input type="text" name="jybh" lay-verify="required"
								   placeholder="请输入警员编号(员工编号)" autocomplete="off" th:value="${user?.jybh}" class="layui-input" readonly>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>角色权限：</label>
						<div class="layui-input-inline" style="width: 240px">
							<select name="roleNames" xm-select="role_select" >
							</select>
							<input type="hidden" name="roleIds" th:value="${roleIds}" id="roleIds">
						</div>
					</div>
				</div>

				<div class="layui-form-item">

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>联系电话：</label>
						<div class="layui-input-inline">
							<input type="tel" name="phone" lay-verify="required|phone" placeholder="请输入电话号码"
								   th:value="${user?.phone}" class="layui-input" readonly>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="margin-left: 0px"><span style="color:red">*</span>所属部门：</label>
						<div class="layui-input-inline" style="width: 240px">
							<input type="text" name="deptName" lay-verify="required"
								   autocomplete="off" th:value="${user.dept.deptName}" class="layui-input" disabled="disabled">
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>最后登录时间：</label>
						<div class="layui-input-inline">
							<input type="text" name="fullName" lay-verify="required|fullnameLength"
								   placeholder="" autocomplete="off" th:value="${lasttime}" class="layui-input" readonly>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>最后登录IP：</label>
						<div class="layui-input-inline">
							<input type="text" name="idCardNumber" lay-verify="required|identity|uniqueCard"
								   placeholder="" autocomplete="off" th:value="${user?.lastLoginIP}" class="layui-input" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>用户有效期始：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="yhyxqsStr" id="yhyxqs" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(user?.yhyxqs, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>用户有效期止：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="yhyxqStr" id="yhyxq" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(user?.yhyxq, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>密码有效期始：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="mmyxqsStr" id="mmyxqs" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(user?.mmyxqs, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>密码有效期止：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="mmyxqStr" id="mmyxq" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(user?.mmyxq, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>客户端IP：</label>
						<div class="layui-input-inline">
							<input type="text" name="allowIp" lay-verify="validip"
								   placeholder="请输入客户端IP" autocomplete="off" th:value="${user?.allowIp}" class="layui-input" readonly>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>创建用户：</label>
						<div class="layui-input-inline">
							<input type="text" placeholder="" autocomplete="off" th:value="${user?.createBy}" class="layui-input" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">是否警员:</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="1" th:attr=" checked=${user?.sfjy == 1 ? true : false}" name="sfjy" id="sfjy" lay-skin="switch" lay-text="是|否" readonly>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">启用状态:</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="true" th:attr="checked=${user?.isStart== true ? true : false}" name="isStart" id="isStart" lay-skin="switch" lay-text="启用|停用" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline" th:if="${session.user != null &&session.user.isSys}">
						<label class="layui-form-label">是否系统级:</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="true" th:attr="checked=${user?.isSys == true ? true : false}" name="isSys" id="isSys" lay-skin="switch" lay-text="是|否" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item" style="text-align: center;">

						<button class="layui-btn layui-btn-primary"  id="cancelBtn" >返回</button>

				</div>
			</form>
		</div>
	</div>
</div>

<script>
	var formSelects = layui.formSelects;


	layui.use([ 'form', 'layedit', 'laydate', 'jquery'  ],
			function() {
				var form = layui.form,
						layer = layui.layer,
						layedit = layui.layedit,
						laydate = layui.laydate,
						$ = jQuery = layui.$;
				// 回到列表页面
				$('#cancelBtn').click(function(){
					window.parent.changTabs( ctx +'userController/userList','','用户列表');
				})
				formSelects.data('role_select','server',{
					data: {"roleIds": $("#roleIds").val()},
					keyVal:'roleName',
					keyName:'roleName',
					direction: 'auto',
					url : ctx + 'roleController/roleDialogJsonX',
					beforeSuccess: function(id, url, searchVal, result){
						result = result.data;

						return result;
					}
				}).on('role_select', function(id, vals, val, isAdd, isDisabled){

					var ids='';
					for(var i = 0;i < vals.length;i++){
						if(i != vals.length - 1){
							ids += vals[i].id + ','
						}else{
							ids += vals[i].id;
						}
					}
					$("#roleIds").val(ids);
				}, true);
				formSelects.disabled();

				form.render();


			});
</script>
</body>
</html>