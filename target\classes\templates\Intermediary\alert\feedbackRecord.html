<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>处理预警记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .page-container {
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .collapseList {
            margin-bottom: 20px;
            border-width: 1px;
            border-radius: 2px;
        }
        .layui-colla-content {
            padding: 20px;
        }
        .layui-form{
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        /* 调整三列布局样式 */
        .form-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .left-column {
            flex: 0 0 300px;
            padding: 10px;
        }

        .middle-column,
        .right-column {
            flex: 1;
            padding: 10px;
            min-width: 250px;
        }

        .layui-form-item {
            margin-bottom: 15px;
            width: 100%;
        }

        .input-wrapper .layui-input,
        .layui-form-select {
            width: 200px;
        }

        .input-wrapper img {
            height: 120px;
        }

        .layui-form-item .layui-form-label {
            width: 100px;
            text-align: right;
        }

        .layui-form-item .layui-input-block {
            margin-left: 110px;
            display: flex;
            align-items: center;
            min-height: 38px;
        }

        .layui-form-item .layui-form-text {
            line-height: 38px;
            display: inline-block;
            vertical-align: middle;
        }

        .form-row {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .layui-form-item.button-container {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .table-container {
            margin-top: 15px;
        }

        #layer-photos-demo {
            margin: 0;
            padding: 0;
        }

        .img-div {
            margin-bottom: 10px;
        }

        .image-preview {
            height: 120px;
            width: auto !important;
        }

        .trend-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin-bottom: 24px;
            padding: 0 0 16px 0;
        }
        .trend-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px 0 24px;
        }
        .trend-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        /* 添加表格容器高度限制样式 */
        .table-container-fixed {
            max-height: 400px;
            overflow-y: auto;
            padding: 0 16px;
        }

        #layer-photos-demo {
            display: flex;
            gap: 10px;
        }

        .img-div {
            margin-bottom: 0;  /* 移除底部间距，因为现在是横向排列 */
        }
    </style>
</head>
<body>
<div class="page-container">
    <div class="collapseList" lay-filter="collapse">
        <div class="layui-colla-item">
            <!--            <h2 class="layui-colla-title">预警信息</h2>-->
            <div class="layui-colla-content layui-show">

                <div class="trend-card">
                    <div class="trend-card-header">
                        <span class="trend-title">预警信息</span>
                    </div>
                    <form class="layui-form" id="form" lay-filter="edit-form">
                        <input type="hidden" name="code" autocomplete="off"
                               th:value="${alertRecord.code}">
                        <input type="hidden" name="alertType" autocomplete="off"
                               th:value="${alertRecord.alertType}">
                        <input type="hidden" name="alertObject" autocomplete="off"
                               th:value="${alertRecord.alertObject}">

                        <div class="form-container">
                            <!-- 左列 - 图片 -->
                            <div class="left-column">
                                <label class="layui-form-label">图像</label>
                                <div class="layui-form-item">
                                    <div class="layadmin-shortcut">
                                        <ul class="layui-row" id="layer-photos-demo" onclick="show_img()">
                                            <li style="display: block;margin-bottom: 10px;" id="imageli-1">
                                                <div class="img-div">
                                                    <input type="hidden" name="ossUrl" id="ossUrl" autocomplete="off" th:value="${alertRecord.ossUrl}">
                                                    <img id="image-preview" class="image-preview" style="display: none" onclick="previewImg()" onerror="replaceWithErrorImage(this)">
                                                </div>
                                            </li>
                                            <li style="display: block;" id="imageli-2">
                                                <div class="img-div">
                                                    <input type="hidden" name="ossUrl2" id="ossUrl2" autocomplete="off" th:value="${smallUrl}">
                                                    <img id="image-preview2" class="image-preview" style="display: none" onclick="previewImg()" onerror="replaceWithErrorImage(this)">
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- 中间列 -->
                            <div class="middle-column">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">预警类型</label>
                                    <div class="layui-input-block input-wrapper">
                                        <span class="layui-form-text">[[${alertRecord.alertType == 1 ? "疑似" : "黑名单"}]]</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">预警对象</label>
                                    <div class="layui-input-block input-wrapper">
                                        <span class="layui-form-text">[[${alertRecord.alertObject == 1 ? "人员" : "车辆"}]]</span>
                                    </div>
                                </div>
                                <div class="layui-form-item" th:if="${alertRecord.alertType == 1}">
                                    <label class="layui-form-label">姓名</label>
                                    <div class="layui-input-block input-wrapper">
                                        <input type="text" name="fullName" autocomplete="off" placeholder="请输入姓名"
                                               class="layui-input" th:value="${alertRecord.fullName}">
                                    </div>
                                </div>
                            </div>

                            <!-- 右列 -->
                            <div class="right-column">
                                <div class="layui-form-item" th:if="${alertRecord.alertType == 1}">
                                    <label class="layui-form-label">身份证号</label>
                                    <div class="layui-input-block input-wrapper">
                                        <input type="text" name="idcardNum" lay-verify="idcard" autocomplete="off" placeholder="请输入身份证号"
                                               class="layui-input" th:value="${alertRecord.idcardNum}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">状态</label>
                                    <div class="layui-input-block input-wrapper">
                                        <select name="stateId" lay-verify="required" id="stateId">
                                            <option value="">请选择状态</option>
                                            <option value="1" th:selected="${alertRecord.stateId == 1}">未处理</option>
                                            <option value="2" th:selected="${alertRecord.stateId == 2}">已处理</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item" th:if="${alertRecord.code != null}">
                                    <label class="layui-form-label">预警时间</label>
                                    <div class="layui-input-block">
                                        <span class="layui-form-text">[[${#dates.format(alertRecord.createTime, 'yyyy-MM-dd HH:mm:ss')}]]</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">处理备注</label>
                                    <div class="layui-input-block input-wrapper">
                                        <input type="text" name="operateFeedback" id="operateFeedback" autocomplete="off"
                                               lay-verify="required" class="layui-input" th:value="${alertRecord.operateFeedback}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div >
                            <div >
                                <button style="display: none" class="layui-btn" lay-submit lay-filter="submit" id="subBtn">保存</button>
                                <button style="display: none" type="reset" class="layui-btn layui-btn-primary" onclick="resetForm()">重置</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="trend-card">
                    <div class="trend-card-header">
                        <span class="trend-title">历史预警记录</span>
                    </div>
                    <div class="table-container-fixed">
                        <table class="layui-hide" id="detail-table" lay-filter="detail-table"></table>
                    </div>
                </div>

                <!--                <div class="table-container">-->
                <!--                    <table class="layui-hide" id="detail-table" lay-filter="detail-table"></table>-->
                <!--                </div>-->
            </div>
        </div>
    </div>
</div>

<script th:inline="javascript">
    let ossUrl = /*[[${alertRecord.ossUrl}]]*/ null;
    let personCode = /*[[${alertRecord.personCode}]]*/ null;
    console.log(personCode);
    if (ossUrl != null) {
        $("#image-preview").attr("src", imageBase + ossUrl);
        $("#image-preview").show();
    }else{
        $("#image-preview").attr("src");
        $("#image-preview").hide();
    }

    let ossUrlSmall = /*[[${smallUrl}]]*/ null;
    if (ossUrlSmall != null) {
        $("#image-preview2").attr("src", imageBase + ossUrlSmall);
        $("#image-preview2").show();
    }else{
        $("#image-preview2").attr("src");
        $("#image-preview2").hide();
    }
</script>
<script>
    var formModified = false;

    layui.use(['form', 'layer', 'table', 'element'], function () {
        var form = layui.form;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;

        $("body").on('change', function () {
            formModified = true; // 表单被修改
        });
        form.on('change', function () {
            formModified = true; // 表单被修改
        });
        // 自定义身份证号校验规则
        form.verify({
            idcard: function (value, item) {
                if (value != null && value != "") {
                    if (value.length !== 18) {
                        return '身份证号长度必须为18位';
                    }
                    if (!/^(\d{17}[\dXx])$/i.test(value)) {
                        return '身份证号格式不正确';
                    }
                }
            }
        });

        // 表单提交监听
        form.on('submit(submit)', function (data) {
            if(!formModified){
                // layer.msg("数据未修改")
                // return false;
            }
            var formData = new FormData();

            // 将其他字段数据也添加到formData中
            for (var key in data.field) {
                formData.append(key, data.field[key]);
            }
            $.ajax({
                url: ctx + 'alertRecordController/feedback',
                type: 'POST',
                data: JSON.stringify(data.field),
                contentType: 'application/json',
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg('保存成功');
                        // 关闭弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        // 刷新父页面数据
                        // parent.location.reload();
                    } else {
                        layer.msg(res.msg);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Ajax 请求发生错误:', error);
                }
            });

            return false; // 阻止表单提交
        });

        // 渲染下半部分表格
        table.render({
            elem: '#detail-table'
            , url: ctx + 'portraitCollectionRecordController/findRecordByCondition' // 这里需要后端提供相应的接口
            , cols: [[
                {field: 'deptName', title: '部门', align: 'center'}
                , {field: 'cameraLocation', title: '记录地点', align: 'center'}
                , {field: 'createTime', title: '记录时间', align: 'center'}
            ]
            ]
            , request: {
                pageName: 'currentPage' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , limit: 10
            , limits: [10, 20, 30, 50]
            ,parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true,
            where: {
                personCode: personCode,
                recordStatus: 1
            },
            method: 'post', // 设置请求方法为 POST
            contentType: 'application/json', // 设置请求内容类型为 JSON
        });

        form.render();

        // 监听折叠面板
        element.on('collapse(collapse)', function(data){
            // 当展开时重新渲染表格，解决表格宽度不正确的问题
            if(data.show){
                table.resize('detail-table');
            }
        });
    });

    // 重置表单及图像
    function resetForm() {
        var form = layui.form;
        var $ = layui.jquery;

        // 重置表单项
        form.val("edit-form", {});
        $("#ossUrl").val(ossUrl);
        // 重置图像
        if (ossUrl != null) {
            $("#image-preview").attr("src", imageBase + ossUrl);
            $("#image-preview").show();
        }else{
            $("#image-preview").attr("src");
            $("#image-preview").hide();
        }

        if (ossUrlSmall != null) {
            $("#image-preview2").attr("src", imageBase + ossUrlSmall);
            $("#image-preview2").show();
        }else{
            $("#image-preview2").attr("src");
            $("#image-preview2").hide();
        }
        formModified = false;
    }

    //显示大图片
    window.onload = function () {
        show_img()
    }

    function show_img() {
        var $ = layui.jquery;
        layer.photos(
            {
                photos: '#layer-photos-demo',
                anim: 5,
                tab: function (pic, layero) {
                    $(document).on("mousewheel", ".layui-layer-photos", function (ev) {
                        var oImg = this;
                        var ev = event || window.event;//返回WheelEvent
                        //ev.preventDefault();
                        var delta = ev.detail ? ev.detail > 0 : ev.wheelDelta < 0;
                        var ratioL = (ev.clientX - oImg.offsetLeft) / oImg.offsetWidth,
                            ratioT = (ev.clientY - oImg.offsetTop) / oImg.offsetHeight,
                            ratioDelta = !delta ? 1 + 0.1 : 1 - 0.1,
                            w = parseInt(oImg.offsetWidth * ratioDelta),
                            h = parseInt(oImg.offsetHeight * ratioDelta),
                            l = Math.round(ev.clientX - (w * ratioL)),
                            t = Math.round(ev.clientY - (h * ratioT));
                        $(".layui-layer-photos").css({
                            width: w, height: h
                            , left: l, top: t
                        });
                        $("#layui-layer-photos").css({width: w, height: h});
                        $("#layui-layer-photos>img").css({width: w, height: h});
                    });
                }
            });
    }
</script>
<script th:inline="javascript">
    function replaceWithErrorImage(imageElement) {
        document.getElementById("imageli-1").style.width = document.getElementById("imageli-2").style.width;
        imageElement.src = [[${#httpServletRequest.getContextPath()}]] + '/images/none.jpg';
        // if (imageElement.id === "image-preview") {
        //     var imageli1 = document.getElementById("imageli-1");
        //     imageli1.style.maxWidth = imageli1.style.maxWidth;
        // }
    }
</script>

</body>
</html>