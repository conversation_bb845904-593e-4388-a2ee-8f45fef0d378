<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
>
<head>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>

</head>
<body>


<fieldset class="layui-elem-field layui-field-title"
          style="margin-top: 20px;">
    <legend>登录信息</legend>
</fieldset>
<form class="layui-form" action="">
    <ul class="layui-timeline" style="padding-left: 30px">
        <li class="layui-timeline-item">
            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
            <div class="layui-timeline-content layui-text">
                <div class="layui-timeline-title">
                    本次登录信息： 时间：<span th:text="${#dates.format(newLog.recordTime,'yyyy-MM-dd HH:mm:ss')}"></span> 来源：<span
                        th:text="${newLog.recordIp}"></span>
                </div>
            </div>
        </li>
        <li class="layui-timeline-item" th:if="${oldLog!=null}">
            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
            <div class="layui-timeline-content layui-text">
                <div class="layui-timeline-title">
                    上次登录信息： 时间：<span th:text="${#dates.format(oldLog.recordTime,'yyyy-MM-dd HH:mm:ss')}"></span> 来源：<span
                        th:text="${oldLog.recordIp}"></span>
                </div>
            </div>
        </li>
    </ul>
</form>

<fieldset class="layui-elem-field layui-field-title"
          style="margin-top: 20px;">
    <legend>用户信息</legend>
</fieldset>
<form class="layui-form" action="">
    <ul class="layui-timeline" style="padding-left: 30px">
        <li class="layui-timeline-item">
            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
            <div class="layui-timeline-content layui-text">
                <div class="layui-timeline-title">
                    密码有效期：<span th:text="${#dates.format(user?.userInfo?.mmyxq,'yyyy-MM-dd')}"></span> 剩余天数：<span
                        th:text="|${mmDays}天|"></span>
                </div>
            </div>
        </li>
        <li class="layui-timeline-item">
            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
            <div class="layui-timeline-content layui-text">
                <div class="layui-timeline-title">
                    用户有效期：<span th:text="${#dates.format(user?.userInfo?.yhyxq,'yyyy-MM-dd')}"></span> 剩余天数：<span
                        th:text="|${yhDays}天|"></span>
                </div>
            </div>
        </li>
    </ul>
</form>
<div th:if="${failLog!=null&&failLog.size()>0}">
<fieldset class="layui-elem-field layui-field-title"
          style="margin-top: 20px;">
    <legend>登录失败记录</legend>
</fieldset>
<form class="layui-form" action="">
    <ul class="layui-timeline" style="padding-left: 30px">
        <li class="layui-timeline-item" th:each="log,status:${failLog}">
            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
            <div class="layui-timeline-content layui-text">
                <div class="layui-timeline-title">
                    时间：<span th:text="${#dates.format(log?.recordTime,'yyyy-MM-dd HH:mm:ss')}"></span>
                    来源：<span th:text="${log?.recordIp}"></span>
                    原因：<span th:text="${log?.actionDisplay}"></span>
                </div>
            </div>
        </li>
    </ul>
</form>
</div>
</body>
</html>
