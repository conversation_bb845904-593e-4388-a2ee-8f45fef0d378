<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IAuthDao">
    <resultMap id="authMap" type="com.fwy.corebasic.entity.Core_Auth">
        <id property="id" column="id" javaType="Long" jdbcType="INTEGER"/>
        <result property="authName" column="authName" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <collection property="actions"
                    ofType="com.fwy.corebasic.entity.Core_ActionForAuth" javaType="ArrayList">
            <id property="id" column="a_id" javaType="Long" jdbcType="INTEGER"/>

            <result property="authId" column="authId" javaType="Long"
                    jdbcType="INTEGER"/>
            <result property="controlName" column="controName" javaType="String"
                    jdbcType="VARCHAR"/>
            <result property="actionName" column="actionName" javaType="String"
                    jdbcType="VARCHAR"/>
            <result property="display" column="display" javaType="String"
                    jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <!-- 获取权限列表 -->
    <select id="list" parameterType="String" resultType="com.fwy.corebasic.entity.Core_Auth">
        select *
        from core_auth ${whereSql}
    </select>

    <select id="getAuthListByIds" resultMap="authMap">
        select *
        from core_auth ${ids}
    </select>

    <!-- 增加权限 -->
    <insert id="add" parameterType="com.fwy.corebasic.entity.Core_Auth" useGeneratedKeys="true" keyProperty="id">
        insert into core_auth(ID, AUTHNAME)
        values (#{id}, #{authName})
    </insert>

    <!-- 修改权限 -->
    <update id="update" parameterType="com.fwy.corebasic.entity.Core_Auth">
        update core_auth
        set AUTHNAME=#{authName}
        where id = #{id}
    </update>

    <!-- 删除权限 -->
    <delete id="delete" parameterType="Long">
        delete
        from core_auth
        where id = #{id}
    </delete>

    <delete id="removeAction" parameterType="Long">
        delete
        from core_actionforauth
        where authid = #{id}
    </delete>

    <insert id="addAction" useGeneratedKeys="true" parameterType="com.fwy.corebasic.entity.Core_ActionForAuth">
        insert into core_actionforauth(ID, AUTHID, CONTRONAME, ACTIONNAME, DISPLAY)
        values (#{id}, #{authId,jdbcType=INTEGER}, #{controlName,jdbcType=VARCHAR},
                #{actionName,jdbcType=VARCHAR}, #{display,jdbcType=VARCHAR})
    </insert>
    <insert id="batchInsertAction">
        insert into core_actionforauth(ID, AUTHID, CONTRONAME, ACTIONNAME, DISPLAY)
        values
        <foreach collection="list"   separator="," item="auth">
            (#{auth.id},
            #{auth.authId,jdbcType=INTEGER},
            #{auth.controlName,jdbcType=VARCHAR},
            #{auth.actionName,jdbcType=VARCHAR},
            #{auth.display,jdbcType=VARCHAR}
            )
        </foreach>

    </insert>

    <select id="getObjectById" parameterType="Long" resultMap="authMap">
        select core_auth.*,
               a.ID as
                       a_id,
               a.AUTHID,
               a.CONTRONAME,
               a.ACTIONNAME,
               a.DISPLAY
        from core_auth
                     left join core_actionforauth a on core_auth.ID = a.AUTHID
        where core_auth.ID =
              #{id}
    </select>

    <!-- 根据名称获取权限列表 -->
    <select id="listByCondition" parameterType="String" resultType="com.fwy.corebasic.entity.Core_Auth">
        select *
        from core_auth
            <where>
             <if test="authName != null and authName != ''">
            and AUTHNAME like concat('%',#{authName},'%')
        </if>
        </where>
    </select>

    <delete id="batchDeleteAuth" parameterType="String">
        delete
        from core_auth
                where ID in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="batchRemoveAction" parameterType="String">
        delete
        from core_actionforauth
                where AUTHID in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>
</mapper>