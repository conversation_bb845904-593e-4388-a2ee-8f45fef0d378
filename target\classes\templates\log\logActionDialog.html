<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>操作日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}">
    <link>
    <script type="text/javascript" th:src="@{/admin/layui/layui.js}"></script>
</head>
<body>
<div class="layui-fluid">
    <table class="layui-hide" id="log-table" lay-filter="log-table"></table>
</div>
<script>
    layui.use(['table','layer','form','jquery', 'laydate'] , function(){
        var admin = layui.admin;
        var $ = layui.jquery;
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var table = layui.table;
        table.render({
            elem: '#log-table'
            ,url: ctx + 'logController/logActionDialogJson'
            ,title: '操作日志表'
            ,cols: [
                [
                    {title: '编号', width:50, type:'ID',hide:true,align:'center'}
                    ,{field:'actionname', width : 160,title:'动作描述',align:'center'}
                    ,{field:'countLog', width : 160,title:'数量统计',align:'center'}
                ]
            ]
            ,request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            ,parseData: function(res){ //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            ,page: true
        });
    });
</script>
</body>
</html>