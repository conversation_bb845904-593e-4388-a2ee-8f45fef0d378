<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>处理预警记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .layui-form{
            padding-top:30px;
        }
        .input-wrapper .layui-input,
        .layui-form-select {
            width: 250px;
        }

        .input-wrapper img {
            height: 150px;
        }

        .layui-form-item .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-block {
            margin-left: 140px;
        }

        .layui-form-item .layui-form-text {
            line-height: 38px;
            display: inline-block;
            vertical-align: middle;
        }



    </style>
</head>
<body>
<form class="layui-form" id = "form" lay-filter="edit-form">
    <input type="hidden" name="code" autocomplete="off"
           th:value="${alertRecord.code}">
    <input type="hidden" name="alertType" autocomplete="off"
           th:value="${alertRecord.alertType}">
    <input type="hidden" name="alertObject" autocomplete="off"
           th:value="${alertRecord.alertObject}">
    <div class="layui-form-item">
        <label class="layui-form-label">预警类型</label>
        <div class="layui-input-block input-wrapper">
            <span class="layui-form-text">[[${alertRecord.alertType == 1 ? "疑似" : "黑名单"}]]</span>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">预警对象</label>
        <div class="layui-input-block input-wrapper">
            <span class="layui-form-text">[[${alertRecord.alertObject == 1 ? "人员" : "车辆"}]]</span>
        </div>
    </div>

    <div class="layui-form-item" th:if="${alertRecord.alertType == 1}">
        <label class="layui-form-label">车牌号</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" name="carNum" autocomplete="off" placeholder="请输入车牌号"
                   class="layui-input" th:value="${alertRecord.carNum}">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block input-wrapper">
            <select name="stateId" lay-verify="required" id="stateId">
                <option value="">请选择状态</option>
                <option value="1" th:selected="${alertRecord.stateId == 1}">未处理</option>
                <option value="2" th:selected="${alertRecord.stateId == 2}">已处理</option>
            </select>
        </div>
    </div>

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label">图像</label>-->
<!--        <div class="layui-input-block input-wrapper" id="image-previewDiv">-->
<!--            <input type="hidden" name="ossUrl" id="ossUrl" autocomplete="off"-->
<!--                   th:value="${alertRecord.ossUrl}">-->
<!--            <img id="image-preview" class="image-preview" style="display: none;"-->
<!--                 onclick="show_img()">-->
<!--        </div>-->
<!--    </div>-->


    <div class="layui-form-item">
        <label class="layui-form-label ">图像：</label>
        <div class="layadmin-shortcut">
            <ul class="layui-row" style="margin-left: 140px;padding: 0px" id="layer-photos-demo"
                onclick="show_img()">
                <li>
                    <div class="img-div">
                        <input type="hidden" name="ossUrl" id="ossUrl" autocomplete="off"
                               th:value="${alertRecord.ossUrl}">
                        <img id="image-preview" class="image-preview" style="display: none;margin-top: -20px;max-height: 45%;max-width: 40%"
                             onclick="previewImg()" onerror="replaceWithErrorImage(this)">
                    </div>
                </li>
            </ul>
        </div>
    </div>

<!--    <div class="layui-form-item" th:if="${alertRecord.code != null}">-->
<!--        <label class="layui-form-label">最后操作用户</label>-->
<!--        <div class="layui-input-block">-->
<!--            <span id="updateUser" class="layui-form-text">[[${alertRecord.updateUser}]]</span>-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item" th:if="${alertRecord.code != null}">-->
<!--        <label class="layui-form-label">最后操作时间</label>-->
<!--        <div class="layui-input-block">-->
<!--            <span id="updateTime" class="layui-form-text">[[${#dates.format(alertRecord.updateTime, 'yyyy-MM-dd HH:mm:ss')}]]</span>-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item" th:if="${alertRecord.code != null}">
        <label class="layui-form-label">预警时间</label>
        <div class="layui-input-block">
            <span class="layui-form-text">[[${#dates.format(alertRecord.createTime, 'yyyy-MM-dd HH:mm:ss')}]]</span>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">处理备注</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" name="operateFeedback" id="operateFeedback" autocomplete="off"
                   lay-verify="required" class="layui-input" th:value="${alertRecord.operateFeedback}">
<!--            <input type="text" name="operateFeedback" id="operateFeedback" autocomplete="off"-->
<!--                   class="layui-input" th:value="${alertRecord.operateFeedback}">-->
        </div>
    </div>
    <!-- 添加其他表单项 -->

    <div class="layui-form-item">
        <div class="layui-input-block">
            <button style="display: none" class="layui-btn" lay-submit lay-filter="submit" id="subBtn">保存</button>
            <button  style="display: none" type="reset" class="layui-btn layui-btn-primary" onclick="resetForm()">重置</button>
        </div>
    </div>
</form>
<script th:inline="javascript">
    let ossUrl = /*[[${alertRecord.ossUrl}]]*/ null;
    if (ossUrl != null) {
        $("#image-preview").attr("src", imageBase + ossUrl);
        $("#image-preview").show();
    }else{
        $("#image-preview").attr("src");
        $("#image-preview").hide();
    }
</script>
<script>
    var formModified = false;

    layui.use(['form', 'layer'], function () {
        var form = layui.form;
        var layer = layui.layer;

        $("body").on('change', function () {
            formModified = true; // 表单被修改
        });
        form.on('change', function () {
            formModified = true; // 表单被修改
        });

        // 表单提交监听
        form.on('submit(submit)', function (data) {
            debugger
            if(!formModified){
                // layer.msg("数据未修改")
                // return false;
            }
            var formData = new FormData();

            // 将其他字段数据也添加到formData中
            for (var key in data.field) {
                formData.append(key, data.field[key]);
            }

            $.ajax({
                // url: ctx + 'intermediary/carAlertRecordController/feedback',
                url: ctx + 'carAlertRecordController/feedback',
                type: 'POST',
                data: JSON.stringify(data.field),
                contentType: 'application/json',
                success: function (res) {
                    if (res.code === 200) {
                        layer.msg('保存成功');
                        // 关闭弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        // 刷新父页面数据
                    } else {
                        layer.msg(res.msg);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Ajax 请求发生错误:', error);
                }
            });

            return false; // 阻止表单提交
        });

        form.render();
    });

    // 重置表单及图像
    function resetForm() {
        var form = layui.form;
        var $ = layui.jquery;

        // 重置表单项
        form.val("edit-form", {});
        $("#ossUrl").val(ossUrl);
        // 重置图像
        if (ossUrl != null) {
            $("#image-preview").attr("src", ctx + ossUrl);
            $("#image-preview").show();
        }else{
            $("#image-preview").attr("src");
            $("#image-preview").hide();
        }
        formModified = false;
    }

    //显示大图片
    window.onload = function () {
        show_img()
    }

    function show_img() {
        var $ = layui.jquery;
        layer.photos(
            {
                photos: '#layer-photos-demo',
                anim: 5,
                tab: function (pic, layero) {
                    $(document).on("mousewheel", ".layui-layer-photos", function (ev) {
                        var oImg = this;
                        var ev = event || window.event;//返回WheelEvent
                        //ev.preventDefault();
                        var delta = ev.detail ? ev.detail > 0 : ev.wheelDelta < 0;
                        var ratioL = (ev.clientX - oImg.offsetLeft) / oImg.offsetWidth,
                            ratioT = (ev.clientY - oImg.offsetTop) / oImg.offsetHeight,
                            ratioDelta = !delta ? 1 + 0.1 : 1 - 0.1,
                            w = parseInt(oImg.offsetWidth * ratioDelta),
                            h = parseInt(oImg.offsetHeight * ratioDelta),
                            l = Math.round(ev.clientX - (w * ratioL)),
                            t = Math.round(ev.clientY - (h * ratioT));
                        $(".layui-layer-photos").css({
                            width: w, height: h
                            , left: l, top: t
                        });
                        $("#layui-layer-photos").css({width: w, height: h});
                        $("#layui-layer-photos>img").css({width: w, height: h});
                    });
                }
            });
    }

</script>
</body>
</html>