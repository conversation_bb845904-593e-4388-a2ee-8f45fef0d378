<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>操作日志</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>
  <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
  <script th:src="@{/plugins/formSelects/formSelects-v4.js}">
  </script>
  <style>
  	.label-width{
  		width:100px;
  	}
  	.input-width{
  		width:75% !important;
  	}
  </style>
</head>
<body>

	<div id="" class="layui-layer-content" style="overflow: visible;">
			<fieldset class="layui-elem-field layui-field-title"
					style="margin-top: 20px;">
					<legend>日志详情</legend>
			</fieldset>
	   		<div class="layui-form-item">
	            <label class="layui-form-label label-width">操作区域</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.areaName}" disabled="disabled">
	            </div>
	        </div> 
	        <div class="layui-form-item">
	            <label class="layui-form-label label-width"> 操作控制器</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.controName}" disabled="disabled">
	            </div>
	        </div> 
	        <div class="layui-form-item">
	            <label class="layui-form-label label-width">操作动作</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.actionName}" disabled="disabled">
	            </div>
	        </div> 
	        <div class="layui-form-item">
	            <label class="layui-form-label label-width">参数json字符串</label>
	            <div class="layui-input-inline input-width">
	                  <textarea name="copyrightInfo" id="copyrightInfo" th:text="${logInfo?.parameterJson}" class="layui-textarea" style="width:510px; height:20px" disabled="disabled"></textarea>
	            </div>
	        </div> 
	         <div class="layui-form-item">
	            <label class="layui-form-label label-width">操作时间</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${#dates.format(logInfo?.recordTime, 'yyyy-MM-dd HH:mm:ss')}" disabled="disabled">
	            </div>
	        </div>
			<div class="layui-form-item" >
				<label class="layui-form-label label-width">操作用户</label>
				<div class="layui-input-inline input-width"  >
					<input name="authName" type="text" class="layui-input" maxlength="50"
						   lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.user.fullName}" disabled="disabled">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label label-width">所属部门</label>
				<div class="layui-input-inline input-width">
					<input name="authName" type="text" class="layui-input" maxlength="50"
						   lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.user.dept.deptName}" disabled="disabled">
				</div>
			</div>
	         <div class="layui-form-item">
	            <label class="layui-form-label label-width"> 操作IP</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.recordIp}" disabled="disabled">
	            </div>
	        </div>
	         <div class="layui-form-item">
	            <label class="layui-form-label label-width">控制器说明</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.controDisplay}" disabled="disabled">
	            </div>
	        </div>
	         <div class="layui-form-item">
	            <label class="layui-form-label label-width"> 动作说明</label>
	            <div class="layui-input-inline input-width">
	                  <input name="authName" type="text" class="layui-input" maxlength="50" 
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.actionDisplay}" disabled="disabled">
	            </div>
	        </div>
	</div>
</body>
</html>