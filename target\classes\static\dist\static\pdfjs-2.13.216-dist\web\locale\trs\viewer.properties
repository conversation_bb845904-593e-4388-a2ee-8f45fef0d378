# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=<PERSON>jinâ gunâj rukùu
previous_label=Sa gachin
next.title=Pajinâ 'na' ñaan
next_label=Ne' ñaan

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ñanj
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=si'iaj {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=Nagi'iaj li'
zoom_out_label=Nagi'iaj li'
zoom_in.title=Nagi'iaj niko'
zoom_in_label=Nagi'iaj niko'
zoom.title=dàj nìko ma'an
presentation_mode.title=Naduno' daj ga ma
presentation_mode_label=Daj gà ma
open_file.title=Na'nïn' chrû ñanj
open_file_label=Na'nïn
print.title=Nari' ña du'ua
print_label=Nari' ñadu'ua
download.title=Nadunïnj
download_label=Nadunïnj
bookmark.title=Daj hua ma (Guxun' nej na'nïn' riña ventana nakàa)
bookmark_label=Daj hua ma

# Secondary toolbar and context menu
tools.title=Rasun
tools_label=Nej rasùun
first_page.title=gun' riña pajina asiniin
first_page_label=Gun' riña pajina asiniin
last_page.title=Gun' riña pajina rukù ni'in
last_page_label=Gun' riña pajina rukù ni'inj
page_rotate_cw.title=Tanikaj ne' huat
page_rotate_cw_label=Tanikaj ne' huat
page_rotate_ccw.title=Tanikaj ne' chînt'
page_rotate_ccw_label=Tanikaj ne' chint

cursor_text_select_tool.title=Dugi'iaj sun' sa ganahui texto
cursor_text_select_tool_label=Nej rasun arajsun' da' nahui' texto
cursor_hand_tool.title=Nachrun' nej rasun
cursor_hand_tool_label=Sa rajsun ro'o'

scroll_vertical.title=Garasun' dukuán runūu
scroll_vertical_label=Dukuán runūu
scroll_horizontal.title=Garasun' dukuán nikin' nahui
scroll_horizontal_label=Dukuán nikin' nahui
scroll_wrapped.title=Garasun' sa nachree
scroll_wrapped_label=Sa nachree

spread_none.title=Si nagi'iaj nugun'un' nej pagina hua ninin
spread_none_label=Ni'io daj hua pagina
spread_odd.title=Nagi'iaj nugua'ant nej pajina
spread_odd_label=Ni'io' daj hua libro gurin
spread_even.title=Nakāj dugui' ngà nej pajinâ ayi'ì ngà da' hùi hùi
spread_even_label=Nahuin nìko nej

# Document properties dialog box
document_properties.title=Nej sa nikāj ñanj…
document_properties_label=Nej sa nikāj ñanj…
document_properties_file_name=Si yugui archîbo:
document_properties_file_size=Dàj yachìj archîbo:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Si yugui:
document_properties_author=Sí girirà:
document_properties_subject=Dugui':
document_properties_keywords=Nej nuguan' huìi:
document_properties_creation_date=Gui gurugui' man:
document_properties_modification_date=Nuguan' nahuin nakà:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Guiri ro'
document_properties_producer=Sa ri PDF:
document_properties_version=PDF Version:
document_properties_page_count=Si Guendâ Pâjina:
document_properties_page_size=Dàj yachìj pâjina:
document_properties_page_size_unit_inches=riña
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=nadu'ua
document_properties_page_size_orientation_landscape=dàj huaj
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Da'ngà'a
document_properties_page_size_name_legal=Nuguan' a'nï'ïn
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Nanèt chre ni'iajt riña Web:
document_properties_linearized_yes=Ga'ue
document_properties_linearized_no=Si ga'ue
document_properties_close=Narán

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Duyichin'

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Nadunā barrâ nù yi'nïn
toggle_sidebar_label=Nadunā barrâ nù yi'nïn
findbar_label=Narì'

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.

# Find panel button title and messages
find_input.title=Narì'
find_previous_label=Sa gachîn
find_next_label=Ne' ñaan
find_highlight=Daran' sa ña'an 
find_match_case_label=Match case
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} si'iaj {{total}} guña gè huaj
find_match_count[two]={{current}} si'iaj {{total}} guña gè huaj
find_match_count[few]={{current}} si'iaj {{total}} guña gè huaj
find_match_count[many]={{current}} si'iaj {{total}} guña gè huaj
find_match_count[other]={{current}} of {{total}} matches
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Doj ngà da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[one]=Doj ngà da' {{limit}} sa nari' dugui'i
find_match_count_limit[two]=Doj ngà da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[few]=Doj ngà da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[many]=Doj ngà da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[other]=Doj ngà da' {{limit}} nej sa nari' dugui'i
find_not_found=Nu narì'ij nugua'anj

# Error panel labels
error_more_info=Doj nuguan' a'min rayi'î nan
error_less_info=Dòj nuguan' a'min rayi'î nan
error_close=Narán
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Message: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Naru'ui': {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Archîbo: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Lînia: {{line}}

# Predefined zoom values
page_scale_actual=Dàj yàchi akuan' nín
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
password_ok=Ga'ue
password_cancel=Duyichin'

