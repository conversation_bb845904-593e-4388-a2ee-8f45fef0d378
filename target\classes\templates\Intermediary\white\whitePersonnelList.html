<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>白名单人员列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <style>
        /*固定input宽度*/
        .layui-input, .layui-textarea {
            display: block;
            width: 180px;
            padding-left: 10px;
        }

        /*#white-table tr {*/
        /*    height: 90px;*/
        /*    line-height: 90px;*/
        /*    vertical-align: middle;*/
        /*}*/

        /*tbody .layui-table-cell {*/
        /*    height: 90px;*/
        /*    line-height: 90px;*/
        /*    vertical-align: middle;*/
        /*    vertical-align: middle;*/
        /*}*/
    </style>
</head>

<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">
                <div class="layui-inline">
                    <input type="text" name="startDate" id="startDate" placeholder="开始日期" autocomplete="off"
                           class="layui-input">
                </div>
                <div class="layui-inline">
                    <input type="text" name="endDate" id="endDate" placeholder="结束日期" autocomplete="off"
                           class="layui-input">
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">地点：</label>
                    <div class="layui-input-inline">
                        <div class="xm-select-demo" id="typeSelect"></div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                    <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                    <button class="layui-btn" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                </div>
            </blockquote>
            <table class="layui-hide" id="white-table" lay-filter="white-table"></table>
            <button id="importExcel" class="layui-btn layui-btn-sm" style="display: none"></button>

            <script type="text/html" id="table-toolbar">

                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe608;</i>新增
                    </button>
                    <button class="layui-btn layui-btn-sm" lay-event="import" id="import"><i class="layui-icon">&#xe62f;</i>人员导入
                    </button>
                    <button class="layui-btn layui-btn-sm" lay-event="downLoad"><i class="layui-icon">&#xe601;</i>模板下载
                    </button>
                    <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del"><i class="layui-icon">&#xe640;</i>删除
                    </button>
                </div>
            </script>

            <script type="text/html" id="table-toolbar-bar">
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
        </div>
    </div>
</div>
<script type="text/html" id="state-column">
    {{ d.stateId == '1' ? '有效' : '无效' }}
</script>
<script>

    var deptId = [[${deptId}]];
    console.log(deptId);

    //回车搜索
    $("blockquote").on('keyup', 'input,select', function (e) {
        if (e.keyCode == 13) {//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });

    layui.use(['laydate', 'table', 'upload'], function () {
        var admin = layui.admin
            , table = layui.table;
        var laydate = layui.laydate;
        var upload = layui.upload;
        // 渲染日期选择器
        laydate.render({
            elem: '#startDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            max: new Date().toLocaleString('zh-CN', {hour12: false}).replace(/\//g, '-').replace(',', '')
        });

        laydate.render({
            elem: '#endDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            max: new Date().toLocaleString('zh-CN', {hour12: false}).replace(/\//g, '-').replace(',', ''),

        });
        var tableIns = table.render({
            elem: '#white-table'
            , id: 'white-table'
            , defaultToolbar: [{
                title: '导出'
                ,layEvent: 'exports1'
                ,icon: 'layui-icon-export'
            }
                , 'print','filter']
            , height: 'full-50'
            , url: ctx + 'whitePersonnelRecordController/findByCondition'
            , toolbar: '#table-toolbar'
            , title: '白名单人员列表'
            , cols: [
                [
                    {type: 'checkbox'}
                    , {field: 'fullName', title: '名称', align: 'center'}
                    , {field: 'stateId', title: '状态', align: 'center', templet: '#state-column'}
                    , {field: 'createTime', title: '登记时间', align: 'center'}
                    , {fixed: 'right', title: '操作', toolbar: '#table-toolbar-bar', align: 'center'}
                ]
            ]
            , request: {
                pageName: 'currentPage' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , limit: 10
            , limits: [5, 10, 20, 50]// 可选的每页显示条目数选项
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true,
            method: 'post', // 设置请求方法为 POST
            contentType: 'application/json', // 设置请求内容类型为 JSON
        })
        //头工具栏事件
        table.on('toolbar(white-table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'add':
                    var data = checkStatus.data;
                    layer.open({
                        type: 2,
                        title: "添加白名单人员",
                        shadeClose: true,
                        area: ['700px', '500px'],
                        btn: ['保存', '取消'],
                        content: ctx + 'whitePersonnelRecordController/insertDialog',
                        yes: function (index, layero) { //当前层索引、当前层DOM对象
                            //提交表单
                            var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                            submit.click();// 触发提交监听
                            return false;
                        }
                    });
                    break;
                case 'del':
                    var data = checkStatus.data;
                    if (data.length < 1) {
                        layer.msg('请至少选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    //获取选中的行id
                    var ids = [];
                    for (var i = 0; i < data.length; i++) {
                        ids.push(data[i].code);
                    }
                    layer.confirm('您确定要删除吗？', function (index) {
                        var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                        $.ajax({
                            url: ctx + 'whitePersonnelRecordController/deleteByCodeList',
                            method: 'post',
                            data: JSON.stringify(ids),
                            contentType: "application/json",
                            dataType: 'JSON',
                            success: function (res) {
                                layer.close(loading);
                                if (res.code != 0) {
                                    layer.alert(res.msg, {icon: 2});
                                } else {
                                    table.reload('white-table'); //只重载数据
                                    layer.msg(res.msg, {icon: 1});
                                }
                            },
                            error: function (res) {
                                layer.close(loading);
                                layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                            }
                        });
                        layer.close(index);
                    });
                    break;
                case 'import':
                    $("#importExcel").click();
                    break;
                case 'downLoad':
                    var url = ctx + "excel/白名单人员信息导入模板.xls";
                    window.open(url);
                    break;
                case 'exports1':
                    var data = checkStatus.data;
                    if (data.length === 0){
                        layer.confirm('未勾选数据，是否导出当前页面数据', {
                            title: '操作确认',  // 弹窗标题
                            icon: 0,          // 图标类型（0-信息，1-成功，2-错误，3-问号）
                            btn: ['确定', '取消'] // 按钮文本
                        }, function(index){
                            // 用户点击"确定"后的回调
                            var currentData = table.cache['white-table'];
                            table.exportFile('white-table', currentData, 'xls');
                            layer.close(index); // 关闭弹窗
                        }, function(index){
                            // 用户点击"取消"后的回调
                            layer.close(index); // 关闭弹窗
                            console.log("操作取消");
                        });
                    }else {
                        layer.confirm('已勾选数据，是否导出勾选数据', {
                            title: '操作确认',  // 弹窗标题
                            icon: 0,          // 图标类型（0-信息，1-成功，2-错误，3-问号）
                            btn: ['确定', '取消'] // 按钮文本
                        }, function(index){
                            // 用户点击"确定"后的回调
                            table.exportFile('white-table', data, 'xls');
                            layer.close(index); // 关闭弹窗
                        }, function(index){
                            // 用户点击"取消"后的回调
                            layer.close(index); // 关闭弹窗
                            console.log("操作取消");
                        })
                    }
                    break;
            }
            ;
        });
        //批量导入
        var uploadInst = upload.render({
            elem: '#importExcel'
            , url: ctx + 'whitePersonnelRecordController/importExcel'
            , data: {
                type: 2,
            }
            , accept: 'file' //普通文件
            , exts: 'xls|xlsx' //只允许上传excel
            , size: 5120
            , done: function (res) {
                table.reload('white-table'); //只重载数据
                if (res.stateType == 0) {
                    layer.closeAll('loading'); //关闭loading
                    layer.msg(res.stateMsg, {icon: 1});
                } else {
                    layer.closeAll('loading'); //关闭loading
                    layer.alert(res.stateMsg, {icon: 2, maxWidth: 700, closeBtn: 0}, function (index) {
                        layer.close(index);
                    });
                }
            }, before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                layer.msg('正在导入，请勿刷新或关闭该页面...', {icon: 16, shade: 0.3, time: 0});
            }, error: function (index, upload) {
                table.reload('white-table'); //只重载数据
                layer.closeAll('loading'); //关闭loading
                layer.alert('导入失败，请重试！', {icon: 2});
            }
        });

        //监听行工具事件
        table.on('tool(white-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('您确定要删除吗？', function (index) {
                    var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                    $.ajax({
                        url: ctx + 'whitePersonnelRecordController/deleteById/' + data.code,
                        method: 'post',
                        // data: {id: data.id},
                        dataType: 'JSON',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code != 0) {
                                layer.alert(res.msg, {icon: 2});
                            } else {
                                table.reload('white-table'); //只重载数据
                                layer.msg(res.msg, {icon: 1});
                            }
                        },
                        error: function (res) {
                            layer.close(loading);
                            layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                        }
                    });
                    layer.close(index);
                });
            } else if (obj.event === 'edit') {
                layer.open({
                    type: 2,
                    title: "编辑白名单信息",
                    shadeClose: true,
                    area: ['700px', '500px'],
                    btn: ['保存', '取消'],
                    content: ctx + 'whitePersonnelRecordController/whitePersonnelDialog/' + data.code,
                    yes: function (index, layero) { //当前层索引、当前层DOM对象
                        //提交表单
                        var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                        submit.click();// 触发提交监听
                        return false;
                    }
                });
            }
        });

        //搜索及重置按钮
        $("#searchBtn").click(function () {

            const carModel = []
            typeSelect.getValue().forEach(data => {
                carModel.push(data.ID)
            })

            tableIns.where = {};

            table.reload('white-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    startDate: $("#startDate").val(),
                    endDate: $("#endDate").val(),
                    deptIds: carModel
                },
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                method: 'post',
                contentType: 'application/json', // 设置请求的内容类型为 JSON
                done: function(res) {
                    // 数据加载完成后的回调
                    console.log('表格重载完成', res);
                    // this.where = {};
                    tableIns = this;
                }
            }); //只重载数据
        })
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#startDate").val("");
            $("#endDate").val("");
            table.reload('white-table', {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                method: 'post',
                contentType: 'application/json' // 设置请求的内容类型为 JSON
            }); //只重载数据
        })

        var typeSelect = xmSelect.render({
            el: '#typeSelect',
            radio: false,
            // clickClose: true,
            filterable: true,
            toolbar: {show: true},
            name: 'deptIds',
            layVerify: 'required',
            prop: {name: 'deptName', value: 'ID'},
            data: [],
            // 启用严格模式，确保不会有重复选中项
            strict: true,
            style: {
                paddingLeft: '0px',
                position: 'relative',
                width: '160px',
                height: '38px'
            },
            tree: {
                show: true,
                strict: false, //是否父子结构，父子结构父节点不会被选中
                indent: 30,//间距
                expandedKeys: [-1],
                clickCheck: true,
                clickExpand: true,//点击展开
            },

        });

        $.ajax({
            url: ctx + '/deptController/deptDetail',
            type: 'PUT',
            success: function (res) {

                // 1. 将ID统一转为字符串
                // var data = res.data.map(item => {
                //     return {
                //         deptName: item.deptName,
                //         ID: item.ID.toString() // 确保ID是字符串
                //     };
                // });

                // 3. 更新下拉框数据
                typeSelect.update({
                    data: res.data,
                });

                // 4. 设置初始值（确保deptId转为字符串）
                var initValue = deptId ? deptId.toString() : "";
                if (initValue) {
                    typeSelect.setValue([initValue]);
                }
            }
        });
    });
</script>

</body>
</html>