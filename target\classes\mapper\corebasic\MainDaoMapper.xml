<?xml version="1.0" encoding="UTF-8" ?>   
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IMainDao">
	<resultMap type="com.fwy.corebasic.entity.Core_User" id="userMap">
		<id property="id" column="id" javaType="Integer" jdbcType="INTEGER" />
		<result property="userName" column="userName" javaType="String"
			jdbcType="VARCHAR" />
		<result property="passWord" column="passWord" javaType="String"
			jdbcType="VARCHAR" />
	</resultMap>
	<select id="login" parameterType="com.fwy.corebasic.entity.Core_User"
		resultType="com.fwy.corebasic.entity.Core_User">
		select * from core_user where USERNAME=#{userName} and
		PASSWORD=#{password}
	</select>

</mapper>