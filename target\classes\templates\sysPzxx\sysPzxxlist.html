<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="UTF-8">
<title>Insert title here</title>
<div th:replace="Importfile::html"></div>
<script th:src="@{/scripts/security/main/openWay.js}"></script>
</head>
<style>
.zfClr3 {
	color: green;
}

.zfClr4 {
	color: red;
}
</style>

<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">

            <table class="layui-hide" id="pzxxTable" lay-filter="pzxxTable"></table>
            <script type="text/html" id="topToolbar"> 
              <!-- <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i class="layui-icon">&#xe608;</i>增加</button>
				<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i class="layui-icon">&#xe642;</i>编辑</button>
				<button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i class="layui-icon">&#xe640;</i>删除</button>
              </div> -->

			  <div class="layui-btn-container">
				<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i class="layui-icon">&#xe642;</i>编辑</button>
              </div>
            </script>
          </div>
        </div>
      </div>
    </div>
  </div>

<script th:inline="javascript"> 

layui.use(['layer', 'element', 'table', 'form','laydate'], function () {
  var admin = layui.admin,
  table = layui.table;
  var layer = layui.layer,
  form = layui.form,
  element = layui.element;
  var $ = layui.$;

  form.render();
  table.render({
    toolbar: '#topToolbar',
    defaultToolbar: ['filter'],
    elem: '#pzxxTable',
    even: false,
    title: '策略配置列表',
    url: ctx + 'sysPzxxController/sysPzxxJson',
    page: true,
    cols: [
      [{type: 'checkbox'},
      {field: 'ip_fail_count', title: 'IP锁定阈值'},
      {field: 'mm_fail_count', title: '密码错误阈值'},
      {field: 'cqwdrsj', title: '长期未登录'},
      {field: 'isStart',title:'状态',
      	templet:function(data){
          	var html = '';
          	if(data.status == 0){
          		html += '<input type="checkbox" value='+data.bh+' name="isStart" lay-skin="switch" lay-text="启用|停用">'
          	}else{
          		html += '<input type="checkbox" value='+data.bh+' checked=true  name="isStart" lay-skin="switch" lay-text="启用|停用">'
          	}
      	    return	html;
      	}
      },{title:'操作',fixed: 'right', align: 'center',
      	templet:function(data){
     	   var html = '';
     	   html += '<a class="layui-btn layui-btn-xs  layui-btn-normal" onclick=updataPxzz('+data.bh+') lay-event="edit">编辑</a>';
 	         /*  html += '<a class="layui-btn layui-btn-xs layui-btn-primary" onclick=checkUser('+data.bh+') lay-event="check">查看</a>' +
		   	   '<a class="layui-btn layui-btn-xs  layui-btn-normal" onclick=updataPxzz('+data.bh+') lay-event="edit">编辑</a>' +
	           '<a class="layui-btn layui-btn-danger layui-btn-xs" onclick=deleteUser('+data.bh+') lay-event="del">删除</a>' */       
            return html;
      	}
     }
      ]
    ],request: {
      pageName: 'pageNum', //页码的参数名称，默认：page
      limitName: 'pageSize' //每页数据量的参数名，默认：limit
    },parseData: function(res){ //res 即为原始返回的数据
	  return {
	 	"code": res.code, //解析接口状态
	 	"msg": res.msg, //解析提示文本
	 	"count": res.data.total, //解析数据长度
	 	"data": res.data.list //解析数据列表
	  }	
	}
	
  });
  
  form.on('switch()', function(data){
      var id = data.value;
 	 var isStart =this.checked ? 1 : 0;
 	$.ajax({
 			url:ctx + 'sysPzxxController/isShow?bh='+id+'&isStart='+isStart,
 		    success:function(res){
 		    	 if(res.code == 0){
 		    			layer.msg('修改成功',{icon: 1});
 					}else{
 						layer.msg('修改失败',{icon: 2});
 					}	
 		    },
 		    error:function(data){
 		    	layer.msg('操作失败',{icon: 2});
 		    }
 		});
 	});
  var active = {
	  		reload:function(){
	  			var checkName = $("#checkName"),checkNumber = $("#checkNumber"),
	  			deptId = $("#deptId"),phone = $("#phone"),isStart = $("#isStart"),
	  			createTime = $("#createTime"),lastLoginTime = $("#lastLoginTime"),
	  			userName = $("#userName");
	  			table.reload('usertable',{
	  				page:{
	  					curr:1
	  				},
	  				where:{
	  					fullName : checkName.val(),idCardNumber : checkNumber.val(),
	  					deptidForSelect : deptId.val(),phone : phone.val(),isStart:isStart.val(),
	  					createTime:createTime.val(),lastLoginTime:lastLoginTime.val(),
	  					userName : userName.val()
	  				}
	  			})
	  	    }
	  }
//头工具栏事件
  table.on('toolbar(pzxxTable)', function(obj){
   var checkStatus = table.checkStatus(obj.config.id);
   switch(obj.event){
     case 'add_btn':
    	 var urlstr = ctx + 'userController/addUser';
    	 var tit = "添加用户";
    	 top.layui.index.openTabsPage(urlstr,tit);
	 break;
     case 'edit_btn':
         var data = checkStatus.data;
         if(data.length < 1){
       	  layer.msg('请选中一行数据', {
       		  icon: 2});
       	  return;
         }
         if(data.length > 1){
       	  layer.msg('只能选中一行数据', {
       		  icon: 2});
       	  return;
         }
         updataPxzz(checkStatus.data[0].bh);
       break;
     case 'delete_btn':
         var data = checkStatus.data;
         if(data.length < 1){
       	  layer.msg('请至少选中一行数据', {
       		  icon: 2});
       	  return;
         }
       	//获取选中的行id
         var ids = '';
         for(var i = 0;i < data.length - 1; i++){
         	ids += data[i].id + ",";
         }
         ids += data[data.length - 1].id
         deleteUser(ids);
       break;
   };
 }); 
  $("#search_btn").click(function(){
  	var type = 'reload';
      active[type] ? active[type].call(this) : '';
  })
 $("#reset_btn").click(function(){
	 window.location.reload();
  })
});

	function deleteUser(id){
		var url = ctx + 'userController/deleteInfo?ids=' + id;
    	layer.confirm('是否删除',{btn:['确定','取消'],title:'提示'},function(){
			var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
			$.ajax({
				url:url,
				success:function(res){
					layer.close(loading);

					 if(res.code == 0){
						layer.msg('删除成功',{icon: 1});
						location.reload();
					}else{
						layer.alert(res.msg,{icon: 2});
					}
					
  				},         
                error:function (res) {
					layer.close(loading);
					layer.alert('操作失败',{icon: 2});
                }
			})
		})
	}
	function updataPxzz(id){
   	    var titleName = "编辑策略配置";
 	    var url = ctx + 'sysPzxxController/editPzxx?id=' + id;
        title = titleName,width='1000px',height='650px',submitButId = '#submitBut';
        //top.layui.index.openTabsPage(url,title);
         //openWindow(url,title,width,height,submitButId);
        var index = layer.open({
	  		type:2,
	  		title: title,
	  		area: ['1000px', '650px'],
	  		fixed: false, //不固定
	          maxmin: true,
	          content: url,
	          btn: ['确定', '取消'], 
	          yes: function(index,layero){
	          	var submit = layero.find('iframe').contents().find('#submitBut');
	          	submit.click();
	          	return false;
	          },
	          btn2: function(){
	            layer.closeAll();
	          },
	          zIndex: layer.zIndex, //重点1
	          success: function(layero){
	             layer.setTop(layero); //重点2
	          }
	  	});
        layer.full(index);
       
	}
	function checkUser(id){
    	var urlstr = ctx + 'userController/showUser?userId=' + id;
    	var tit = "查看用户";
   	 	top.layui.index.openTabsPage(urlstr,tit);
	}

$(document).on("click",".layui-table-body table.layui-table tbody tr",function(){
    var obj = event ? event.target : event.srcElement;
    var tag = obj.tagName;
    var checkbox = $(this).find("td div.laytable-cell-checkbox div.layui-form-checkbox I");
    if(checkbox.length!=0){
        if(tag == 'DIV') {
            checkbox.click();
        }
    }
    
});

$(document).on("click","td div.laytable-cell-checkbox div.layui-form-checkbox",function(e){
    e.stopPropagation();
});
</script>
</body>