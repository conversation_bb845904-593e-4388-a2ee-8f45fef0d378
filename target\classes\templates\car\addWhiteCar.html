<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>添加白名单车辆</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .input-wrapper .layui-input,
        .layui-form-select {
            width: 250px;
        }

        .input-wrapper img {
            height: 150px;
        }

        .layui-form-item .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-block {
            margin-left: 140px;
        }

        .layui-form-item .layui-form-text {
            line-height: 38px;
            display: inline-block;
            vertical-align: middle;
        }

    </style>
</head>
<body>
<form class="layui-form" lay-filter="form-demo">
    <br>
    <br>
    <br>
    <br>
    <br>
    <div class="layui-form-item">
        <label class="layui-form-label ">车牌号</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" name="carNum" placeholder="请输入车牌号" autocomplete="off" class="layui-input"
                   th:value="${whiteCarRecord.carNum}">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="form-submit" id="subBtn">提交</button>
        </div>
    </div>
</form>

<script>
    layui.use(['form'], function() {
        var form = layui.form;

        //监听表单提交事件
        form.on('submit(form-submit)', function(data) {
            if (!data.field.carNum) {
                layer.msg('未输入车牌号！');
                return false;
            }

            //在这里通过ajax将数据提交给后端Java
            $.ajax({
                type: "POST", //方法类型
                dataType: "json", //数据类型
                contentType: "application/json;charset=utf-8", //定义数据格式为json
                url: ctx+"/whiteCarController/save1", //后台请求url
                data: JSON.stringify(data.field), //将表单的值序列化为JSON字符串
                processData: false,
                success: function(res) {
                    layer.msg('保存成功');
                    // 关闭弹窗
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    // 刷新父页面数据
                    parent.location.reload();
                    // window.close()
                },
                error: function(result) {
                    console.log('Ajax 请求发生错误:', error);
                }
            });
            return false; //阻止表单跳转提交
        });
    });
</script>

</body>
</html>