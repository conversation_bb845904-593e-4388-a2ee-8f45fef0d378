<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="utf-8">
<title>用户信息</title>
<div th:replace="Importfile::html"></div>
<link rel="stylesheet"
	th:href="@{/plugins/formSelects/formSelects-v4.css}" />
	<script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
<style>
.layui-form-item .layui-input-inline{
	width : 260px;
}
.layui-form-label{
    width:120px;
}
</style>
</head>
<body>
	<div class="layui-fluid">
		<div class="layui-card">
			<div class="layui-card-body">
				<fieldset class="layui-elem-field layui-field-title"
					style="margin-top: 20px;">
					<legend>用户信息</legend>
				</fieldset>

				<form class="layui-form" action="">
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">用户名：</label>
							<div class="layui-input-inline">
      							<input type="text" name="userName" lay-verify="required"
      							 autocomplete="off" th:value="${user.userName}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">身份证号：</label>
							<div class="layui-input-inline">
      							<input type="text" name="idCardNumber" lay-verify="required"
      							 autocomplete="off" th:value="${user.idCardNumber}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
					</div>

					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">用户全称：</label>
							<div class="layui-input-inline">
      							<input type="text" name="fullName" lay-verify="required"
      							  autocomplete="off" th:value="${user.fullName}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">联系电话：</label>
							<div class="layui-input-inline">
        						<input type="tel" name="phone" lay-verify="required|phone"
        						 autocomplete="off" th:value="${user.phone}" class="layui-input" disabled="disabled">
      						</div>
						</div>
					</div>
					
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">最后登录时间：</label>
							<div class="layui-input-inline">
      							<input type="text" name="lastLoginTime" lay-verify="date"
      							  autocomplete="off" th:value="${lasttime}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">最后登录IP：</label>
							<div class="layui-input-inline">
      							<input type="text" name="lastLoginIP" lay-verify="required"
      							 autocomplete="off" th:value="${user.lastLoginIP}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
					</div>
									
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">创建时间：</label>
							<div class="layui-input-inline">
      							<input type="text" name="createTime" lay-verify="date"
      							 autocomplete="off" th:value="${creattime}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">创建人：</label>
							<div class="layui-input-inline">
      							<input type="text" name="createBy" lay-verify="required"
      							autocomplete="off" th:value="${user.createBy}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
					</div>
					
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">所属部门：</label>
							<div class="layui-input-inline">
      							<input type="text" name="deptName" lay-verify="required"
      							autocomplete="off" th:value="${user.dept.deptName}" class="layui-input" disabled="disabled">
   							 </div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">角色权限：</label>
							<div class="layui-input-inline input-width">
					         <select name="roleNames" xm-select="role_select" >
							  </select>
							  <input type="hidden" name="roleIds" th:value="${roleIds}" id="roleIds">
							</div>
						</div>
					</div>
					<div class="layui-form-item" th:if="${user!=null&&user.isSys}">
						<label class="layui-form-label">系统级：</label>
						<div class="layui-input-inline" th:if="${user!=null&&user.isSys}">
							<input type="checkbox" name="isSys" lay-skin="switch" checked=""
								lay-text="是|否" disabled>
						</div>
						<div class="layui-input-inline" th:if="${user==null||(user!=null&&!user.isSys)}">
							<input type="checkbox" name="isSys" lay-skin="switch"
								lay-text="是|否" disabled>
						</div>
					</div>	
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label">是否显示：</label>
							<div class="layui-input-inline" th:if="${user!=null&&user.isStart}">
								<input type="checkbox" name="isStart" lay-skin="switch" checked=""
									lay-text="是|否" disabled>
							</div>
							<div class="layui-input-inline" th:if="${user==null||(user!=null&&!user.isStart)}">
								<input type="checkbox" name="isStart" lay-skin="switch"
									lay-text="是|否" disabled>
							</div>
						</div>
					</div>
					
				</form>
			</div>
		</div>
	</div>
<script>
	var formSelects = layui.formSelects;
		layui.use([ 'form', 'layedit', 'laydate', 'jquery'  ],
			function() {
				var form = layui.form, layer = layui.layer, layedit = layui.layedit, laydate = layui.laydate, $ = jQuery = layui.$;
		});
		formSelects.data('role_select','server',{
	    	data: {"roleIds": $("#roleIds").val()},
	     	keyVal:'roleName',
	    	keyName:'roleName',
	    	direction: 'auto',
	        url : ctx + 'roleController/roleDialogJsonX',
	        beforeSuccess: function(id, url, searchVal, result){
	        	result = result.data;
				return result;
			}
	    }).on('role_select', function(id, vals, val, isAdd, isDisabled){
	    	
	    	var ids='';
	    	for(var i = 0;i < vals.length;i++){
	    		if(i != vals.length - 1){
	        		ids += vals[i].id + ','
	        	 }else{
	        		ids += vals[i].id;
	        	 }
	         }
	           $("#roleIds").val(ids);
	    }, true);
		formSelects.disabled();
		
</script>
	
</body>
</html>