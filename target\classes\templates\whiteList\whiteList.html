<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>白名单管理</title>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" />
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
</head>
<style>
    /*固定input宽度*/
    .layui-input, .layui-textarea {
        display: block;
        width: 180px;
        padding-left: 10px;
    }
</style>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <blockquote class="layui-elem-quote quoteBox" id="search" style="margin-bottom: 5px">
                        <div class="layui-inline">
                            <label class="layui-form-label">登录账户：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input"  id="userName" autocomplete="off" placeholder="登录账户">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">用户名：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input"  id="fullName" autocomplete="off" placeholder="用户名">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">身份证号：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input"  id="idCardNumber" autocomplete="off" placeholder="身份证号">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">手机号：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input"  id="phone" autocomplete="off" placeholder="手机号">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">所属部门：</label>
                            <div class="layui-input-inline" style="width: 180px;">
                                <div class="xm-select" id="deptId" placeholder="请选择">
                                </div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                            <button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</button>
                            <button class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
                        </div>
                    </blockquote>

                    <table class="layui-hide" id="whiteList_table" lay-filter="whiteList_table"></table>
                    <script type="text/html" id="topToolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i class="layui-icon">&#xe608;</i>增加</button>
                            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i class="layui-icon">&#xe640;</i>删除</button>
                        </div>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    //回车搜索
    $("blockquote").on('keyup','input,select',function (e) {
        if (e.keyCode == 13){//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#search_btn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.use(['table','laydate'], function(){
        var admin = layui.admin,laydate = layui.laydate
            ,table = layui.table, form = layui.form;
        form.render();
        table.render({
                toolbar : '#topToolbar',
                defaultToolbar: ['filter']
            ,height:'full-50'
            ,limit:20,
                elem : '#whiteList_table',
                even : false,
                title : '白名单列表',
                defaultToolbar:[],
                // height : 'full-100',
                url : ctx + '/whiteListController/whiteListJson',
                page : true,
                cols : [ [
                    {
                        type : 'checkbox'
                    },
                    {
                        field:'userName',
                        title:'userName',
                        hide: true
                    },
                    {
                        field: 'fullName',
                        title: '用户名',
                        width:160,
                        align: 'center'
                    },
                    {
                        field: 'userName',
                        title: '登录账户',
                        width:120,
                        align: 'center'
                    },
                    {
                        field: 'deptName',
                        title: '所属部门',
                        width:120,
                        align: 'center'
                        ,templet:function(data){
                            var html = "";
                            if(data.deptName==null){
                                return html ;
                            }else {
                                return data.deptName
                            }
                        }
                    },
                    {
                        field: 'idCardNumber',
                        title: '身份证号',
                        width:200,
                        align: 'center'
                        ,templet:function(data){
                            var html = "";
                            if(data.idCardNumber==null){
                                return html ;
                            }else {
                                return data.idCardNumber
                            }
                        }
                    },
                    {
                        field: 'phone',
                        title: '手机号',
                        width:180,
                        align: 'center'
                    }
                ] ],
                request : {
                    pageName : 'pageNum', //页码的参数名称，默认：page
                    limitName : 'pageSize' //每页数据量的参数名，默认：limit
                },
                parseData : function(res) { //res 即为原始返回的数据
                    return {
                        "code" : res.code, //解析接口状态
                        "msg" : res.msg, //解析提示文本
                        "count" : res.data.total, //解析数据长度
                        "data" : res.data.list
                        //解析数据列表
                    }
                },
                done : function(res, curr, count) {
                }

            });
        var active = {
            reload:function(){
                var deptId = "";
                if(!(dialog_deptId.getValue()=="")){
                    deptId = dialog_deptId.getValue()[0].ID;
                }
                var fullName = $("#fullName").val(),
                    idCardNumber = $("#idCardNumber").val(),
                    userName = $("#userName").val(),
                    phone = $("#phone").val();
                table.reload('whiteList_table',{
                    page:{
                        curr:1
                    },
                    where:{fullName:fullName,
                        idCardNumber: idCardNumber,
                        phone: phone,
                        userName: userName,
                        deptId: deptId}
                })
            }
        };
        $("#search_btn").click(function(){
            var type = 'reload';
            active[type] ? active[type].call(this) : '';
        });
        $("#unset_Btn").click(function(){
            $("#search :input").val("");
            dialog_deptId.reset();
            $.ajax({
                url:ctx + '/deptController/tree',
                method:'get',
                dataType : 'json' ,
                success:function(response){
                    var arr = response.data;//这里的data可能是ajax返回的数据
                    dialog_deptId.update({
                        data: arr
                    });
                },
                error:function (res) {
                }
            });
            layui.form.render();
            table.reload('whiteList_table', {
                where: null
                ,page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
        $(document).keydown(function(event){
            if(event.keyCode == 13){
                var type = 'reload';
                active[type] ? active[type].call(this) : '';
            }
        });
        //头工具栏事件
        table.on('toolbar(whiteList_table)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            var data = checkStatus.data;
            var width=900,height=560,
                submitButId = '#submitBut',
                flashTable='whiteList_table';
            switch(obj.event){
                case 'add_btn':
                    var url = ctx + '/whiteListController/addWhiteList',
                        tit = '新增白名单用户';
                    xadmin.openWindowTop(url,tit,width,height,submitButId,flashTable);
                break;
                case 'delete_btn':
                    if(data.length == 1){
                        var url = ctx + '/whiteListController/deleteWhiteList?account='+data[0].userName,
                            tableName = 'whiteList_table';
                        xadmin.deleteDemo(url,tableName);
                    }else{
                        layer.msg("请选择一条数据");
                    }
                break;
            }
        });
    });

    var dialog_deptId = xmSelect.render({
        el: '#deptId',
        filterable: true,
        model: { label: { type: 'block' }  },
        radio: true,
        clickClose: true,
        tree: {
            show: true,
            strict: false,
            indent: 30,
            expandedKeys: [ -1 ]
        },
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width:'auto'
        },
        prop : {
            name : "deptName",
            value : "ID"
        },
        height: '200px',
        empty : '暂无数据',
        data : []
    });
    $(function(){
        $.ajax({
            url:ctx + '/deptController/tree',
            method:'get',
            dataType : 'json' ,
            success:function(response){
                var arr = response.data;//这里的data可能是ajax返回的数据
                dialog_deptId.update({
                    data: arr
                });
            },
            error:function (res) {
            }
        });
    });

    layui.form.render();
</script>

</body></html>