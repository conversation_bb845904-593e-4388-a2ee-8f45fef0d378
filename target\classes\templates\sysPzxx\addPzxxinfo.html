<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="utf-8">
<title>策略信息</title>
<div th:replace="Importfile::html"></div>
<script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
<link rel="stylesheet"
	th:href="@{/plugins/formSelects/formSelects-v4.css}" />
<style>
.layui-form-item .layui-input-inline{
	width : 260px;
}
</style>
</head>
<script type="text/javascript">
   

    $(function () {
        $("#password").val('');
        $("#roleNames").rwpUICombobox({
            onBeforeOpen: rwpTemp.f_selectuserrole
        });
        $("#deptName").rwpUICombobox({
            onBeforeOpen: rwpTemp.f_selectuserdept
        });
        if ("1" == 1)
            $('#ulIsSys').show(); //系统级管理员 可设置用户的系统级权限
        $("#ajaxform").rwpUIForm({
            isAjaxSubmit: true
        });
    });
</script>
<style type="text/css">
.parmBut {
	font-size: 10px;
	margin-left: 10px;
	margin-bottom: 5px;
	line-height: 20px;
	height: 30px;
}
.rwpSpinner {
	width: auto;
}
.dsbl_slsj{
	width:20px !important;
}

#sjhm                                  ~ .rwpCombobox {
	display: none;
}
#sbj_hb                                  ~ .rwpCombobox {
	display: none;
}
.cur {
	box-shadow: 0px 4px 10px black;
}
</style>
<form action="${ctx}/syspzxxController/savePzxx" id="ajaxform" method="post">
    <fieldset>
        <legend><i class="i"></i>系统参数添加</legend>
        <div class="formpanel">
            <div class="clear">
                <ul class="DialabelWidth">
                    <li class="editor-label"><label for="dz">IP锁定阈值:</label></li>
                    <li class="editor-field">
                    	<input data-val="true" data-val-required="IP锁定阈值不能为空" id="ip_fail_count" name="ip_fail_count" type="text"/>
                    </li>
                    <li class="editor-validation">
                    	<span class="field-validation-valid" data-valmsg-for="ip_fail_count" data-valmsg-replace="true"></span>
                    </li>
                </ul>
                <ul class="DialabelWidth">
                    <li class="editor-label"><label for="mm_fail_count">密码错误阈值:</label></li>
                    <li class="editor-field">
                    	<input data-val="true" data-val-required="密码错误阈值不能为空" id="mm_fail_count" name="mm_fail_count" type="text"/>
                    </li>
                    <li class="editor-validation"><span
                            class="field-validation-valid" data-valmsg-for="mm_fail_count"
                            data-valmsg-replace="true"></span>
                    </li>
                </ul>
            </div>
            <div class="clear">
                <ul class="DialabelWidth">
                    <li class="editor-label"><label for="cqwdrsj">长期未登录时间（天）:</label></li>
                    <li class="editor-field">
                    	<input data-val="true" data-val-length="长期未登录时间不能超过4字符" data-val-length-max="4"
                              data-val-required="长期未登录时间不能为空" id="cqwdrsj" name="cqwdrsj" type="text" value=""/>
                    </li>
                    <li class="editor-validation">
                    	<span class="field-validation-valid" data-valmsg-for="cqwdrsj" data-valmsg-replace="true"></span>
                    </li>
                </ul>
               
							<script type="text/javascript">
							$(function() {
								
								$('.dsbl_slsj').rwpUISpinner({
									type : 'int',
									minValue : 0,
									maxValue : 60,
									defaultValueText : ''
								});
								
							});
						</script>
            </div>
            <div class="clear">
            	 <ul class="DialabelWidth" style="width: 850px;">
									<li class="editor-label">系统登入时间(24小时制)</li>
									<li style="color: red;"></li>
									<li class="editor-field">
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">上午:</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblmhb" type="text"
												value="${not empty timeHelper.dsblmhb?timeHelper.dsblmhb:6}"
												style="width: 30px; " /></li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">时</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblmmb" type="text"
												value="${not empty timeHelper.dsblmmb?timeHelper.dsblmmb:0}"
												style="width: 30px; " />
											</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">分---</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblmhe" type="text"
												value="${not empty timeHelper.dsblmhe?timeHelper.dsblmhe:12}"
												style="width: 30px; " /></li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">时</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblmme" type="text"
												value="${not empty timeHelper.dsblmme?timeHelper.dsblmme:0}"
												style="width: 30px; " />
											</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">分,</li>
										</ul></li>
									<li class="editor-field">
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">下午:</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblahb" type="text"
												value="${not empty timeHelper.dsblahb?timeHelper.dsblahb:12}"
												style="width: 30px; " /></li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">时</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblamb" type="text"
												value="${not empty timeHelper.dsblamb?timeHelper.dsblamb:1}"
												style="width: 30px; " />
											</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">分---</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblahe" type="text"
												value="${not empty timeHelper.dsblahe?timeHelper.dsblahe:23}"
												style="width: 30px; " /></li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">时</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field"><input class="dsbl_slsj"
												name="dsblame" type="text"
												value="${not empty timeHelper.dsblame?timeHelper.dsblame:0}"
												style="width: 30px; " />
											</li>
										</ul>
										<ul class="DialabelWidth" style="width: auto">
											<li class="editor-field">分</li>
										</ul></li>
								</ul>
            </div>
        </div>
    </fieldset>
</form>