var menu = (function($) {
	
	var menuTemplate = "<li data-id='*{id}' class='layui-nav-item'>"+
							"<a href='javascript:;' lay-href='*{url}' lay-tips='*{menuName}' lay-direction='2'> <i class='*{icon}'></i> <cite>*{menuName}</cite></a>"+
						"</li>",
		menuHasChildrenTemplate = "<li data-id='*{id}' class='layui-nav-item'>"+
							"<a href='javascript:;' lay-tips='*{menuName}' lay-direction='2'> <i class='*{icon}'></i> <cite>*{menuName}</cite></a>"+
						"<dl class='layui-nav-child'></dl></li>",
		menuSubTemplate = "<dd data-id='*{id}'><a href='javascript:;'>*{menuName}</a><dl class='layui-nav-child'></dl></dd>",
		menuChildrenTemplate = "<dd data-id='*{id}'><a lay-href='*{url}'>*{menuName}</a></dd>";
    
       	var joinMenu = function(menuData, ctx) {
            var i = 0, menuArrayLength = menuData.length;
            for(i = 0; i < menuArrayLength; i++) {
                var menu = menuData[i], liHtml = '',
                           $menu, parentID = menu.parentID, $List, template, url = "";
                if(menu.children){
                	if(menu.parentID === 0){
                		template = menuHasChildrenTemplate;
                	}else{
                		template = menuSubTemplate;
                	}
                }else{
                	if(menu.parentID === 0){
                		template = menuTemplate;
                	}else{
                		template = menuChildrenTemplate;
                	}
                }
                if(menu.menuUrl === null) {
                    url = "javascript:void(0)";
                } else {
                    url = ctx + '/' + menu.menuUrl;
                }
                
                liHtml = template.replace(/\*{id}/g, menu.ID)
                				 .replace(/\*{icon}/g, menu.iconCssClass)
                				 .replace(/\*{url}/g, url)
                                 .replace(/\*{menuName}/g, menu.menuName);
                
                $menu = $(liHtml);
                
                if(menu.children){
                	if(menu.parentID === 0){
                		$List = $("#nav");
                	}else{
                		$List = $("#nav [data-id='" + parentID + "']>dl");
                	}
                }else{
                	if(menu.parentID === 0){
                		$List = $("#nav");
                	}else{
                		$List = $("#nav [data-id='" + parentID + "']>dl");
                	}
                }
                
                $List.append($menu);
                if(menu.children) {
                	joinMenu(menu.children, ctx);
                } else {
                    continue;
                }
            }
            
        };                        	
     	
        //递归获取第一条菜单
    	var getFirstUrl = function(menuData){
    		if(menuData.length >= 1){
    			if(menuData[0].children){
    				//默认第一条菜单选中
    				$("#nav [data-id='" + menuData[0].ID + "']").attr("class", "layui-nav-item layui-nav-itemed");
    				return getFirstUrl(menuData[0].children);
            	}else{
            		return menuData[0];
            	}
            }
    	}
     init = function(menuData, ctx) {
    	joinMenu(menuData, ctx);
    	//默认选中第一个菜单
    	var firstMenu = getFirstUrl(menuData);
    	if(firstMenu.ID == menuData[0].ID){
    		$("#nav [data-id='" + firstMenu.ID + "']").attr("class", "layui-nav-item layui-this");
    	}else{
    		$("#nav [data-id='" + firstMenu.ID + "']").attr("class", "layui-nav-itemed layui-this");
    	}
    	$("#firstFrame").attr("src", firstMenu.menuUrl);
     };
  /*------------------------------- END PUBLIC ----------------------------------*/
      return {init: init};
  })(jQuery);
         
