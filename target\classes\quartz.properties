#
#============================================================================
# Configure Main Scheduler Properties \u8C03\u5EA6\u5668\u5C5E\u6027
#============================================================================
org.quartz.scheduler.instanceName: DefaultQuartzScheduler
org.quartz.scheduler.instanceId = AUTO
org.quartz.scheduler.rmi.export: false
org.quartz.scheduler.rmi.proxy: false
org.quartz.scheduler.wrapJobExecutionInUserTransaction: false
org.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount= 10
org.quartz.threadPool.threadPriority: 5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread: true
org.quartz.jobStore.misfireThreshold: 60000
org.quartz.jobStore.acquireTriggersWithinLock=true
#============================================================================
# Configure JobStore
#============================================================================
#存储方式使用JobStoreTX，也就是数据库
org.quartz.jobStore.class: org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass:org.quartz.impl.jdbcjobstore.StdJDBCDelegate
#使用自己的配置文件
org.quartz.jobStore.useProperties:true
#数据库中quartz表的表名前缀
org.quartz.jobStore.tablePrefix:qrtz_
#org.quartz.jobStore.dataSource:qzDS
#是否使用集群（如果项目只部署到一台服务器，就不用了）
org.quartz.jobStore.isClustered = false
#============================================================================
# Configure Datasources
#============================================================================
#配置数据源（org.quartz.dataSource.qzDS.maxConnections: c3p0配置的是有s的，druid数据源没有s） 如果定时任务数据库跟项目数据库一样  这里不用修改
#org.quartz.dataSource.qzDS.connectionProvider.class:com.fwy.common.task.DruidConnectionProvider
#org.quartz.dataSource.qzDS.driver: com.mysql.cj.jdbc.Driver
#org.quartz.dataSource.qzDS.URL: **********************************************************************************************************************************************************
#org.quartz.dataSource.qzDS.user: root
#org.quartz.dataSource.qzDS.password: 123456
#org.quartz.dataSource.qzDS.maxConnection: 10

