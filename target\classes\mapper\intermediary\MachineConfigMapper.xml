<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.intermediary.dao.IMachineConfigDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="configMap" type="com.fwy.intermediary.entity.MachineConfig">
        <id column="code" property="code" />
        <result column="dept_id" property="deptId" />
        <result column="dept_name" property="deptName" />
        <result column="type" property="type" />
        <result column="channel" property="channel" />
        <result column="machine_name" property="machineName" />
        <result column="ip" property="ip" />
        <result column="password" property="password" />
        <result column="state_id" property="stateId" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime"  javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime"  javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findByCondition" parameterType="MachineConfigCondition" resultMap="configMap">
        select * from machine_config where state_id != 3
        <if test="stateId != null">
            and state_id = #{stateId}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="machineName != null &amp;&amp; machineName !=''">
            and machine_name like concat('%', #{machineName}, '%')
        </if>
        <if test="channel != null &amp;&amp; channel !=''">
            and channel like concat('%', #{channel}, '%')
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="ip != null &amp;&amp; ip !=''">
            and ip = #{ip}
        </if>
        <if test="updateUser != null &amp;&amp; updateUser != ''">
            and update_user like concat('%', #{updateUser}, '%')
        </if>
        <if test="startDate != null">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            <!--&lt;小于-->
            and create_time &lt;= #{endDate}
        </if>
        <if test="updateTime != null">
            and update_time >= #{startDate}
        </if>
    </select>
</mapper>
