<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fwy.intermediary.dao.CarCollectionRecordMapper" >
  <resultMap id="BaseResultMap" type="com.fwy.intermediary.entity.CarCollectionRecord" >
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="car_id" property="carId" jdbcType="VARCHAR" />
    <result column="dept_id" property="deptId" jdbcType="VARCHAR" />
    <result column="dept_name" property="deptName" jdbcType="VARCHAR" />
    <result column="camera_location" property="cameraLocation" jdbcType="VARCHAR" />
    <result column="car_num" property="carNum" jdbcType="VARCHAR" />
    <result column="oss_url" property="ossUrl" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
    <update id="updateStateId">
      update car_collection_record
      set state_id = #{stateId}
      where car_id = #{carId}
    </update>

    <!--通过状态删除记录表的数据-->
  <delete id="deleteDataByState">
        delete from car_collection_record
        where car_id in (select code from car_collection_image where state_id=3)
  </delete>

  <select id="findByCondition" parameterType="com.fwy.intermediary.entity.show.CarRecordList" resultMap="BaseResultMap">
    select * from car_collection_record
    <where>
      <if test="carId != null">
        car_id = #{carId}
      </if>
      <if test="deptIds != null and deptIds.size() != 0">
        and dept_id in
        <foreach collection="deptIds" item="deptId" index="index"
                 open="(" close=")" separator=",">
          #{deptId}
        </foreach>
      </if>
      <if test="startDate != null">
        and create_time >= #{startDate}
      </if>
      <if test="endDate != null">
        <!--&lt;小于-->
        and create_time &lt;= #{endDate}
      </if>
    </where>
    order by create_time desc
  </select>

  <!--判断是否存在此车牌-->
  <select id="ifExists" resultType="java.lang.Integer" parameterType="java.lang.String">
    select exists
    (select car_num from car_collection_record
    where car_num = #{carNum,jdbcType=VARCHAR})
  </select>

  <!--根据车id查询所有数据-->
  <select id="getRecordByCarId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select *
    from car_collection_record
    where code = #{code,jdbcType=VARCHAR}
  </select>

  <!--通过车牌！删除！车辆信息-->
  <delete id="deleteByCarNum" parameterType="java.lang.String" >
    delete from car_collection_record
    where car_num = #{carNum,jdbcType=VARCHAR}
  </delete>

  <select id="selectCarNumAndDateAndLocation" resultMap="BaseResultMap">
    select camera_location,car_num,create_time
    from car_collection_record
    where car_num=#{carNum,jdbcType=VARCHAR}
    order by create_time desc limit 1
  </select>
<!--  <select id="getCarWarningCount" resultType="com.fwy.intermediary.entity.response.CarWarningCountResponse">-->
<!--    SELECT-->
<!--    COUNT(*) AS carWarningCount,-->
<!--    create_time AS hour-->
<!--    FROM car_collection_record-->
<!--    WHERE car_id = #{carId}-->
<!--    <if test="deptIds != null and deptIds.size() != 0">-->
<!--      and dept_id in-->
<!--      <foreach collection="deptIds" item="deptId" index="index"-->
<!--               open="(" close=")" separator=",">-->
<!--        #{deptId}-->
<!--      </foreach>-->
<!--    </if>-->
<!--    <if test="startDate != null">-->
<!--      and create_time >= #{startDate}-->
<!--    </if>-->
<!--    <if test="endDate != null">-->
<!--      &lt;!&ndash;&lt;小于&ndash;&gt;-->
<!--      and create_time &lt;= #{endDate}-->
<!--    </if>-->
<!--    GROUP BY create_time-->
<!--    order by create_time-->
<!--  </select>-->
  <select id="getCarWarningCount" resultType="com.fwy.intermediary.entity.response.CarWarningCountResponse">
    SELECT
    COUNT(*) AS carWarningCount,
    HOUR(create_time) AS hour
    FROM car_collection_record
    WHERE car_id = #{carId} AND HOUR(create_time) BETWEEN 8 AND 18
    <if test="deptIds != null and deptIds.size() != 0">
      and dept_id in
      <foreach collection="deptIds" item="deptId" index="index"
               open="(" close=")" separator=",">
        #{deptId}
      </foreach>
    </if>
    <if test="startDate != null">
      and create_time >= #{startDate}
    </if>
    <if test="endDate != null">
      <!--&lt;小于-->
      and create_time &lt;= #{endDate}
    </if>
    GROUP BY HOUR(create_time)
    ORDER BY HOUR(create_time)
  </select>
  <select id="getCount" resultType="java.lang.Integer">
    select count(*) from car_collection_record
    where car_id = #{carId}
  </select>

  <select id="getCarList" resultMap="BaseResultMap">
    select * from car_collection_record
    where car_id = #{carId}
  </select>

    <insert id="save" parameterType="com.fwy.intermediary.entity.CarCollectionRecord" >
    insert into car_collection_record (code, car_id, dept_id, 
      dept_name, camera_location, car_num, 
      oss_url, create_time)
    values (#{code,jdbcType=VARCHAR}, #{carId,jdbcType=VARCHAR}, #{deptId,jdbcType=VARCHAR}, 
      #{deptName,jdbcType=VARCHAR}, #{cameraLocation,jdbcType=VARCHAR}, #{carNum,jdbcType=VARCHAR}, 
      #{ossUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fwy.intermediary.entity.CarCollectionRecord" >
    insert into car_collection_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        code,
      </if>
      <if test="carId != null" >
        car_id,
      </if>
      <if test="deptId != null" >
        dept_id,
      </if>
      <if test="deptName != null" >
        dept_name,
      </if>
      <if test="cameraLocation != null" >
        camera_location,
      </if>
      <if test="carNum != null" >
        car_num,
      </if>
      <if test="ossUrl != null" >
        oss_url,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="carId != null" >
        #{carId,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null" >
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null" >
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="cameraLocation != null" >
        #{cameraLocation,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null" >
        #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="ossUrl != null" >
        #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>