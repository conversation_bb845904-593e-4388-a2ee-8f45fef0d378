<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>部门配置参数</title>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <style>
        .layui-form-item .layui-input-inline {
            width: 260px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                <legend>部门配置参数</legend>
            </fieldset>
            <form id="modelAuthForm" lay-filter="modelAuthForm" class="layui-form">
                <div class="layui-form-item">
                    <input name="id" id="id" type="hidden" th:value="${deptParams?.id}">
                </div>

                <div class="layui-form-item">
                    <div class="form-group" style="margin-top: 20px;margin-left: 60px;">
                        <div class="layui-form-item">
                            <input type="hidden" id="qrCodeUrl" name="qrCodeUrl" th:value="${deptParams?.qrCodeUrl}">
                            <ul>
                                <li>
                                    <img id="img_info" alt="" width="100px;" height="100px;" name="img_info">
                                </li>
                                <li style="margin-top: 7px;">
                                    <button type="button" style="width: 100px;" class="layui-btn"
                                            id="upbtn" lay-event="upbtn">选择图标
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="form-group" style="margin-top: 20px">
                        <div class="layui-form-item">
                            <label class="layui-form-label"><span style="color:red">*</span>所属部门：</label>
                            <div class="layui-input-inline" style="width: 75%">
                                <div class="xm-select" id="selectDeptId">
                                </div>
                                <input type="hidden" name="deptId" id="deptId" th:value="${deptParams?.deptId}"/>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">经度:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" th:value="${deptParams?.longitude}" name="longitude"
                                   id="longitude" lay-verify="required" placeholder="经度" maxlength="8">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">纬度:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" th:value="${deptParams?.latitude}" name="latitude"
                                   id="latitude" lay-verify="required" placeholder="纬度" maxlength="8">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>时间模板:</label>
                        <div class="layui-input-inline">
                            <div class="xm-select" id="selectTimeModel">
                            </div>
                            <input type="hidden" th:value="${deptParams?.timeModelId}" id="timeModelId" name="timeModelId">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>超时分钟数:</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input" th:value="${deptParams?.timeoutMinutes}" name="timeoutMinutes"
                                   lay-verify="required" id="timeoutMinutes" placeholder="超时分钟数" maxlength="5">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>每天放号总量:</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input" th:value="${deptParams?.everyDayTotalNumber}" name="everyDayTotalNumber"
                                   id="everyDayTotalNumber" placeholder="每天放号总量" maxlength="5">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>单人单日取号数:</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input" lay-verify="required" th:value="${deptParams?.everyOneTakeNumber}" name="everyOneTakeNumber"
                                    id="everyOneTakeNumber" placeholder="单人单日可取号数" maxlength="2">
                        </div>
                    </div>
                </div>

                <div  class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">是否提前进闸：</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="istqjr" lay-filter="istqjr" lay-event="istqjr" value="1" lay-skin="switch"  th:attr="checked=${deptParams?.istqjr==1}"
                                   lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">提前进闸分钟数：</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input" th:value="${deptParams?.tqjrsj}" name="tqjrsj"
                                    lay-filter="tqjrsj" maxlength="5"  lay-event="tqjrsj" id="tqjrsj" placeholder="进闸分钟数">
                        </div>
                    </div>
                </div>

                <div  class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">是否提前出闸：</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="iscqsj"  lay-filter="iscqsj" lay-event="iscqsj" value="1" lay-skin="switch"  th:attr="checked=${deptParams?.iscqsj==1}"
                                   lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">限制出闸分钟：</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input" th:value="${deptParams?.cqsj}" name="cqsj"
                                     lay-event="cqsj" id="cqsj" placeholder="出闸分钟数" maxlength="5">
                        </div>
                    </div>
                </div>

                <div  class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">开启取号间隔限制：</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="isopenGetNum"  lay-filter="isopenGetNum" lay-event="isopenGetNum" value="1" lay-skin="switch"  th:attr="checked=${deptParams?.isopenGetNum==1}"
                                   lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">取号间隔时间（分钟）：</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input" th:value="${deptParams?.getMinutes}" name="getMinutes"
                                   lay-event="getMinutes" id="getMinutes" placeholder="出闸分钟数" maxlength="5">
                        </div>
                    </div>
                </div>


                <div  class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>单次取号进闸次数：</label>
                        <div class="layui-input-inline">
                            <input type="number" class="layui-input" th:value="${deptParams?.zjxzNum}" name="zjxzNum"
                                   id="zjxzNum" lay-verify="required" placeholder="重复出闸次数" maxlength="5">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label"><span style="color:red">*</span>部门地址:</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入部门地址" name="address" class="layui-textarea"
                                  lay-verify="required" maxlength="500" th:text="${deptParams?.address}"></textarea>
                    </div>
                </div>


                <button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>
            </form>
        </div>
    </div>
</div>
<script>
    $(function () {
        var id = $("#id").val();
        if (id != null && id != "") {
            $("#img_info").attr("src", ctx + "deptParamController/readPartImage?id=" + id);
        }else {
            $("#img_info").attr("src", ctx + "/photos/imgHdPic.jpg");
        }
    });
    layui.use(['form','upload'], function () {
        var form = layui.form,
         upload = layui.upload;
        form.render();
        form.verify({
            payCode: function (value) {
                if (value.length > 32) {
                    return '支付代码填写长度大于32';
                }
            }
        });
        //监听提交
        form.on('submit(submitBut)', function (data) {
            console.log(data)
            xadmin.submitForm(ctx + '/deptParamController/saveDeptParams', data.field, 'deptParamsTable');
            return false;
        });

        upload.render({
            elem: '#upbtn',
            url: ctx + '/uploadImageForAbsolutePath',
            auto: true,//选择文件后不自动上传
            bindAction: '#submitBut',
            //上传前的回调
            before: function () {
            },
            //选择文件后的回调
            choose: function (obj) {
                obj.preview(function (index, file, result) {
                    $('#img_info').attr('src', result);
                })
            }, //操作成功的回调
            done: function (res) {
                if(res.code == 0){
                    $("#qrCodeUrl").val(res.data);
                }else{
                    layer.msg(res.msg,{icon: 2});
                }
            },
            //上传错误回调
            error: function (index, upload) {
                layer.alert('上传失败！' + index);
            }
        });

    });

    var dialog_deptId = xmSelect.render({
        el: '#selectDeptId',
        filterable: true,
        layVerify: 'required|unique',
        layVerType: 'msg',
        model: { label: { type: 'block' }  },
        template({ item, sels, name, value }){
            return '<p title="'+name+'">'+name+'</p>';
        },
        radio: true,
        clickClose: true,
        tree: {
            show: true,
            strict: false,
            indent: 30,
            expandedKeys: [ -1 ]
        },
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width:'620px'
        },
        prop : {
            name : "deptName",
            value : "ID"
        },
        height: '200px',
        empty : '暂无数据',
        data : [],
        on({arr, change, isAdd}) {
            $("#deptId").val(arr[0].ID);
        }

    });

    var selectTimeModel = xmSelect.render({
        el: '#selectTimeModel',
        toolbar: {show: true},
        data: [],
        layVerify: 'required',
        prop : {
            name : "modelName",
            value : "id"
        },
        on({arr, change, isAdd}) {
            $("#timeModelId").val(arr[0].id);
        }
    });

    var selectEvaluateModel = xmSelect.render({
        el: '#selectEvaluateModel',
        toolbar: {show: true},
        layVerify: 'required',
        data: [],
        prop : {
            name : "modelname",
            value : "id"
        },
        on({arr, change, isAdd}) {
            $("#evaluateModelId").val(arr[0].id);
        }
    });

    var selectNotesTemplate = xmSelect.render({
        el: '#selectNotesTemplate',
        toolbar: {show: true},
        data: [],
        layVerify: 'required',
        prop : {
            name : "modelName",
            value : "id"
        },
        on({arr, change, isAdd}) {
            $("#notesTemplateId").val(arr[0].id);
        }
    });

    $(function () {
        $.ajax({
            url: ctx + '/deptController/tree',
            data: { deptId: $('#deptId').val()},
            method: "POST",
            dataType: 'json',
            success: function (response) {
                dialog_deptId.update({
                    data: response.data
                })
            },
            error: function (res) {
            }
        });
        $.ajax({
            url: ctx + '/modelController/selectModellJson',
            data: {modelType: "8",isvalid:"1","id": $("#timeModelId").val()},
            success:function(response){
                selectTimeModel.update({
                    data: response.data
                })
            },
            error: function(error){
                console.log(error);
            }
        });
        $.ajax({
            url: ctx + '/appraiseController/findAppraiseTemplatePopUp',
            data: {"appraisalTemplateId": $("#evaluateModelId").val()},
            success:function(response){
                selectEvaluateModel.update({
                    data: response.data
                })
            },
            error:function(error){
                console.log(error);
            }
        });
        $.ajax({
            url: ctx + '/notesTemplateController/findNotesTemplatePopUp',
            data: {"notesTemplateId": $("#notesTemplateId").val()},
            success:function(response){
                selectNotesTemplate.update({
                    data: response.data
                })
            },
            error: function(error){
                console.log(error);
            }
        });
    });
    layui.form.render();
</script>
</body>
</html>