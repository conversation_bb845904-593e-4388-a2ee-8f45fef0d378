<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IBlackWhiteDao">
	<resultMap type="com.fwy.corebasic.entity.Black_white" id="blackWhiteMap">
		<id property="id" column="id" />
		<result property="ip" column="ip" />
		<result property="reason" column="reason" />
		<result property="cjsj" column="cjsj" javaType="java.util.Date"
			jdbcType="TIMESTAMP" />
		<result property="jssj" column="jssj" javaType="java.util.Date"
			jdbcType="TIMESTAMP" />
        <result property="account" column="account" />
        <result property="type" column="type" />
	</resultMap>
	<resultMap type="com.fwy.corebasic.entity.Core_User" id="userMap">
		<id property="id" column="id" javaType="Long" jdbcType="INTEGER"/>
		<result property="userName" column="username" javaType="String"
				jdbcType="VARCHAR"/>
		<result property="deptId" column="deptid" javaType="Long"
				jdbcType="INTEGER"/>
		<result property="deptName" column="deptName" javaType="String"
				jdbcType="VARCHAR"/>
		<result property="password" column="password" javaType="String"
				jdbcType="VARCHAR"/>
		<result property="fullName" column="fullname" javaType="String"
				jdbcType="VARCHAR"/>
		<result property="phone" column="phone" javaType="String"
				jdbcType="VARCHAR"/>
		<result property="isStart" column="isstart"/>
		<result property="isSys" column="issys"/>
		<result property="createTime" column="createtime" javaType="java.util.Date"
				jdbcType="TIMESTAMP"/>
		<result property="isDel" column="isdel"/>
		<result property="lastLoginTime" column="lastlogintime"
				javaType="java.util.Date" jdbcType="TIMESTAMP"/>
		<result property="lastLoginIp" column="lastloginip" javaType="String"
				jdbcType="VARCHAR"/>
		<result property="appLastLoginTime" column="lastlogintime"
				javaType="java.util.Date" jdbcType="TIMESTAMP"/>
		<association property="userInfo" javaType="com.fwy.corebasic.entity.Core_User_Check">
			<id property="id" column="id" javaType="Long" jdbcType="INTEGER"/>
			<result property="id" column="ID"/>
			<result property="createBy" column="CREATEBY"/>
			<result property="idCardNumber" column="IDCARDNUMBER"/>
			<result property="pcCode" column="PC_CODE"/>
			<result property="payCode" column="PAYCODE"/>
			<result property="payType" column="PAYTYPE"/>
			<result property="goodsRemark" column="GOODSREMARK"/>
			<result property="isAdmin" column="ISADMIN"/>
			<result property="policeManPhoto" column="POLICEMANPHOTO"/>
			<result property="jybh" column="JYBH"/>
			<result property="gmsfhm" column="GMSFHM"/>
			<result property="yhyxq" column="YHYXQ"/>
			<result property="sfxgmm" column="SFXGMM"/>
			<result property="mmyxq" column="MMYXQ"/>
			<result property="allowIp" column="ALLOWIP"/>
			<result property="sfjy" column="SFJY"/>
			<result property="yhyxqs" column="YHYXQS"/>
			<result property="mmyxqs" column="MMYXQS"/>
			<result property="jyw" column="JYW	"/>
			<result property="jyws" column="JYWS"/>
			<result property="position" column="POSITION"/>
			<result property="degree" column="DEGREE"/>
			<result property="politicalStatus" column="POLITICALSTATUS"/>
		</association>
		<association property="dept" javaType="com.fwy.corebasic.entity.Core_Dept">
			<id property="id" column="deptid" javaType="Long" jdbcType="INTEGER"/>
			<result property="parentId" column="parentid" javaType="Long"
					jdbcType="INTEGER"/>
			<result property="orderCode" column="ordercode" javaType="String"
					jdbcType="VARCHAR"/>
			<result property="isSys" column="issys"/>
			<result property="deptName" column="deptname" javaType="String"
					jdbcType="VARCHAR"/>
			<result property="isShow" column="isshow"/>
			<result property="remark" column="remark" javaType="String"
					jdbcType="VARCHAR"/>
			<result property="deptCode" column="deptcode" javaType="String"
					jdbcType="VARCHAR"/>
		</association>
	</resultMap>
	<insert id="addBlackWhiteIp" parameterType="com.fwy.corebasic.entity.Black_white" useGeneratedKeys="true">
		insert into black_white (ID,IP,CJSJ,JSSJ,REASON,ACCOUNT,TYPE)values
		(
		#{id},
		#{ip,jdbcType=VARCHAR},
		#{cjsj,jdbcType=TIMESTAMP},
		#{jssj,jdbcType=TIMESTAMP},
		#{reason,jdbcType=VARCHAR},
		#{account,jdbcType=VARCHAR},
		#{type,jdbcType=INTEGER}
		)
	</insert>

	<update id="updateBlackIp" parameterType="com.fwy.corebasic.entity.Black_white">
		update black_white set
		IP=#{ip},CJSJ=#{cjsj},JSSJ=#{jssj}
		where ID=#{id}
	</update>
	
	<!-- 删除 -->
	<delete id="deleteBlackIp" parameterType="java.util.List">
		delete from black_white
		where ID in
		<foreach collection="idList" index="index" item="item"
            open="(" separator="," close=")">
            #{item}
        </foreach>
	</delete>
	
	<!-- 查询 -->
	<select id="getBlackIp" resultMap="blackWhiteMap">
		select * from black_white
		where
			IP=#{ip}
			and (CJSJ &lt;= now() and JSSJ &gt;= now() or CJSJ = jssj)
			and type = 0
		order by CJSJ desc
	</select>
	
	<!-- 分页查询黑名单 -->
	<select id="list" resultMap="blackWhiteMap">
		select * from black_white t
		<where>
			<if test="ip !=null and ip != ''">
				and IP like concat('%',#{ip},'%')
			</if>
			<if test="startTime !=null and startTime != ''">
				and date_format(t.CJSJ, '%Y-%m-%d') &gt;= #{startTime}
			</if>
			<if test="endTime !=null and endTime != ''">
				and date_format(t.CJSJ, '%Y-%m-%d') &lt;= #{endTime}
			</if>
			and t.TYPE = #{type}
		</where>
		order by CJSJ desc
	</select>
	
	<!-- 查询单个 -->
	<select id="findByid" resultMap="blackWhiteMap">
		select * from black_white
		where ID=#{id}
	</select>

    <select id="findWhiteUserList" resultType="com.fwy.corebasic.entity.Core_User">
		select cu.ID,
		cu.FULLNAME,
		cu.USERNAME,
		cu.PHONE,
		cd.DEPTNAME deptName,
		ck.IDCARDNUMBER
		from core_user cu
		left join core_user_check ck
		on cu.ID = ck.ID
		left join core_dept cd
		on cu.DEPTID = cd.ID
		left join black_white bw
		on bw.ACCOUNT = cu.USERNAME
		where 1 = 1
		<if test="userName !=null and userName != ''">
			and cu.USERNAME like concat('%', #{userName} ,'%')
		</if>
		<if test="fullName !=null and fullName != ''">
			and cu.FULLNAME like concat('%',#{fullName},'%')
		</if>
		<if test="phone !=null and phone != ''">
			and cu.PHONE like concat('%',#{phone},'%')
		</if>
		<if test="depts != null and depts.size() != 0">
			and cu.DEPTID in
			<foreach collection="depts" item="deptId" separator="," open="(" close=")">
				#{deptId}
			</foreach>
		</if>
		<if test="idCardNumber !=null">
			and ck.IDCARDNUMBER like concat('%', #{idCardNumber},'%')
		</if>
        and bw.TYPE = 1
        order by cu.CREATETIME desc
    </select>

<!--	<insert id="batchSaveWhiteListUser">-->
<!--		insert into black_white(id,ACCOUNT,TYPE)-->
<!--		select SEQ_black_ip_id.nextval, cd.* from(-->
<!--		<foreach open="(" collection="list" index="index" item="userName" close=")"  separator="union">-->
<!--			select-->
<!--			#{userName},-->
<!--			1-->
<!--			from dual-->
<!--		</foreach>-->
<!--		) cd-->
<!--	</insert>-->
<insert id="batchSaveWhiteListUser" useGeneratedKeys="true">
		insert into black_white(ID,ACCOUNT,TYPE)
		values
		<foreach collection="list" index="index" item="blackWhite" separator=",">
			(#{blackWhite.id},#{blackWhite.account},1)
		</foreach>
	</insert>

	<!-- 删除 -->
	<delete id="delWhiteListUser" parameterType="java.util.List">
		delete from black_white
		where ACCOUNT = #{account}
	</delete>

	<select id="getByUserName" resultType="com.fwy.corebasic.entity.Black_white">
		select * from black_white where ACCOUNT = #{userName} and TYPE = 1
	</select>
</mapper>