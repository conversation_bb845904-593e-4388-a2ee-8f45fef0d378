<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>客户端日志</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>
  <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
  <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
  <style>
  	.label-width{
  		width:100px;
  	}
  	.input-width{
  		width:45% !important;
  	}
  </style>
</head>
<body>
  
	<div  class="layui-layer-content" style="overflow: visible;">
			<fieldset class="layui-elem-field layui-field-title"
					style="margin-top: 20px;">
					<legend>日志详情</legend>
			</fieldset>
			<div class="layui-form-item">
				<label class="layui-form-label label-width">机器码</label>
				<div class="layui-input-inline input-width">
					<input name="machineCode" type="text" class="layui-input" maxlength="50"
						   lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.machineCode}" disabled="disabled">
				</div>
			</div>
	   		<div class="layui-form-item">
	            <label class="layui-form-label label-width">日志描述</label>
	            <div class="layui-input-inline input-width">
	                  <input name="logDesc" type="text" class="layui-input" maxlength="50"
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${logInfo?.logDesc}" disabled="disabled">
	            </div>
	        </div>
			<div class="layui-form-item">
				<label class="layui-form-label label-width">日志详情</label>
				<div class="layui-input-inline input-width">
					<textarea name="logResult" id="logResult" th:text="${logInfo?.logResult}" class="layui-textarea" style="width:510px; height:20px" disabled="disabled"></textarea>
				</div>
			</div>
	        <!--<div class="layui-form-item">
	            <label class="layui-form-label label-width">返回参数json</label>
	            <div class="layui-input-inline input-width">
	                  <textarea name="resJson" id="" th:text="${logInfo?.resJson}" class="layui-textarea" style="width:510px; height:20px" disabled="disabled"></textarea>
	            </div>
	        </div> -->
	         <div class="layui-form-item">
	            <label class="layui-form-label label-width">操作时间</label>
	            <div class="layui-input-inline input-width">
	                  <input name="logTime" type="text" class="layui-input" maxlength="50"
	                  lay-vertype="tips" lay-verify="required" required="" th:value="${#dates.format(logInfo?.logTime, 'yyyy-MM-dd HH:mm:ss')}" disabled="disabled">
	            </div>
	        </div>
	</div>

</body>
</html>