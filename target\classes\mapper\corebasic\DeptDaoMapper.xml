<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IDeptDao">
    <resultMap type="com.fwy.corebasic.entity.Core_Dept" id="BaseMap">
        <result property="id" column="ID"/>
        <result property="parentId" column="PARENTID"/>
        <result property="parentName" column="PARENTNAME"/>
        <result property="orderCode" column="ORDERCODE"/>
        <result property="isSys" column="ISSYS"/>
        <result property="deptName" column="DEPTNAME"/>
        <result property="adminRegionId" column="ADMINREGIONID"/>
        <result property="isShow" column="ISSHOW"/>
        <result property="remark" column="REMARK"/>
        <result property="deptCode" column="DEPTCODE"/>
        <result property="createTime" column="DEPTPHONE"/>
        <result property="version" column="VERSION"/>
        <result property="LEVEL" column="LEVEL"/>

    </resultMap>

    <resultMap type="com.fwy.corebasic.entity.Core_Dept" id="deptChildrenMap">
        <result property="id" column="ID"/>
        <result property="parentId" column="PARENTID"/>
        <result property="parentName" column="PARENTNAME"/>
        <result property="orderCode" column="ORDERCODE"/>
        <result property="isSys" column="ISSYS"/>
        <result property="deptName" column="DEPTNAME"/>
        <result property="adminRegionId" column="ADMINREGIONID"/>
        <result property="isShow" column="ISSHOW"/>
        <result property="LEVEL" column="LEVEL"/>
        <result property="remark" column="REMARK"/>
        <result property="deptCode" column="DEPTCODE"/>
        <result property="createTime" column="DEPTPHONE"/>
        <result property="municipalDeptId" column="MUNICIPAL_DEPT_ID"/>
        <collection column="id" property="children"
                    ofType="com.fwy.corebasic.entity.Core_Dept"
                    select="getChildrenList"/>
    </resultMap>
    <select id="getChildrenList" resultMap="deptChildrenMap">
        select *
        from core_dept
        where PARENTID = #{parentId}
          and ISSHOW = 1
        order by ORDERCODE
    </select>

    <select id="getTree" resultMap="deptChildrenMap">
        select *
        from core_dept
                where ISSHOW = 1
        <if test="id != null">
            and ID = #{id}
        </if>
        <if test="id == null">
            and PARENTID = 0
        </if>
        order by ORDERCODE
    </select>
    <!-- 删除部门 -->
    <delete id="delete" parameterType="long">
        delete
        from core_dept
        where ID = #{id}
    </delete>

    <!-- 增加部门 -->
    <insert id="save" parameterType="com.fwy.corebasic.entity.Core_Dept">
        insert into core_dept(PARENTID, ORDERCODE, ISSYS, DEPTNAME, DEPTTYPE, ISSHOW, REMARK, DEPTCODE,LEVEL)
        values (#{parentId},
                #{orderCode,jdbcType=VARCHAR}, #{isSys,jdbcType=INTEGER},
                #{deptName,jdbcType=VARCHAR}, #{deptType,jdbcType=VARCHAR}, #{isShow,jdbcType=INTEGER},
                #{remark,jdbcType=VARCHAR}, #{deptCode,jdbcType=VARCHAR},
                #{LEVEL,jdbcType=INTEGER}
               )
    </insert>
    <select id="list" resultMap="BaseMap">
        select t.*, t2.DISCRIPTION PARENTNAME
                from core_dept t
                             left join (select * from dic_datadetail where DICCODE = 'deptType') t2
                on t.DEPTTYPE = t2.DICVALUE
            <where>
                <if test="deptId != null">
                    t.ID in (SELECT ID FROM (
                    SELECT t1.ID, IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID), 0) AS ischild
                    FROM
                    (
                        SELECT ID, PARENTID FROM core_dept t
                        ORDER BY ID, PARENTID
                    ) t1,
                    (SELECT @pids := #{deptId}) t2
                    ) t3
                    WHERE ISCHILD != 0
                    OR ID = #{deptId})
                </if>
                <if test="depts != null and depts.size() != 0">
                    and t.ID in
                    <foreach collection="depts"  open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                </if>
            </where>
        order by
                t.ORDERCODE
    </select>
    <!-- 修改部门 -->
    <update id="update" parameterType="com.fwy.corebasic.entity.Core_Dept">
        update core_dept
        set PARENTID=#{parentId,jdbcType=INTEGER},
            ORDERCODE=#{orderCode,jdbcType=VARCHAR},
            ISSYS=#{isSys,jdbcType=INTEGER},
            DEPTTYPE=#{deptType,jdbcType=VARCHAR},
            DEPTNAME=#{deptName,jdbcType=VARCHAR},
            ISSHOW=#{isShow,jdbcType=INTEGER},
            LEVEL=#{LEVEL,jdbcType=INTEGER},
            REMARK=#{remark,jdbcType=VARCHAR},
            DEPTCODE=#{deptCode,jdbcType=VARCHAR},
            VERSION=version + 1
        where ID = #{id}
    </update>
    <!-- 是否有子集 -->
    <select id="isHaveChlidren" resultType="int">
        select count(*)
        from core_dept
        where PARENTID = #{id}
    </select>
    <!-- 获得插入数据的orderCode -->
    <select id="getInsertCode" resultType="string" parameterType="long">
        select Max(ordercode)
        from core_dept
        where PARENTID = #{parentId}
    </select>

    <!-- 根据名字得到id -->
    <select id="getDeptIdByDeptName" parameterType="string"
            resultType="long">
        select ID
        from core_dept
        where DEPTNAME = #{parentDeptName}
    </select>

    <select id="getCountByDeptcord" parameterType="String" resultType="int">
        select count(1)
        from core_dept cd
        where cd.DEPTCODE = #{deptCode}
    </select>


    <select id="findChildren" resultType="java.util.HashMap">
        select ID,getChildFromDept(ID) as childrenIdStr  from core_dept
    </select>


    <!-- 是否有子集 -->
    <select id="isHaveChildren" resultType="Integer">
        select count(*)
        from core_dept
        where PARENTID = #{id}
    </select>

    <!-- 获取同一父类下的最大ordercode -->
    <select id="getMaxiOrderCode" parameterType="Long" resultType="com.fwy.corebasic.entity.Core_Dept">
        select *
        from core_dept
        where ORDERCODE =
              (select max(ORDERCODE) from core_dept where PARENTID = (select PARENTID from core_dept where ID = #{id}))
    </select>

    <!-- 获取同一父类下的最小ordercode -->
    <select id="getMiniOrderCode" parameterType="Long" resultType="com.fwy.corebasic.entity.Core_Dept">
        select *
        from core_dept
        where ORDERCODE =
              (select min(ORDERCODE) from core_dept where PARENTID = (select PARENTID from core_dept where ID = #{id}))
    </select>

    <!-- 获取需要移动的自身ordercode -->
    <select id="getOwnOrderCode" parameterType="Long" resultType="com.fwy.corebasic.entity.Core_Dept">
        select *
        from core_dept
        where ID = #{id}
    </select>
    <!-- 获取需要移动的下一个对象的ordercode -->
    <select id="getNextOrderCode" parameterType="Long" resultType="com.fwy.corebasic.entity.Core_Dept">
        select *
        from core_dept
        where ORDERCODE = (select min(ORDERCODE)
                           from core_dept
                           where PARENTID = (select PARENTID from core_dept where ID = #{id})
                             and ORDERCODE &gt; (select ORDERCODE from core_dept where ID = #{id}))
    </select>

    <!-- 获取需要移动的上一个对象的ordercode -->
    <select id="getOnAOrderCode" parameterType="Long" resultType="com.fwy.corebasic.entity.Core_Dept">
        select *
        from core_dept
        where ORDERCODE = (select max(ORDERCODE)
                           from core_dept
                           where PARENTID = (select PARENTID from core_dept where ID = #{id})
                             and ORDERCODE &lt; (select ORDERCODE from core_dept where ID = #{id}))
    </select>
    <!-- regexp_replace('12345612','12','xx',1,1)函数不支持mysql -->
    <update id="moving">
        update core_dept cd
        set cd.ORDERCODE = (SELECT concat(
                                           SUBSTR(cd.ORDERCODE from 1 FOR (LOCATE(#{orderCode_1}, cd.ORDERCODE) - 1)),
                                           #{orderCode_2},
                                           SUBSTR(cd.ORDERCODE from
                                                  (LOCATE(#{orderCode_1}, cd.ORDERCODE) + LENGTH(#{orderCode_1})))
                                           )),
            VERSION=VERSION + 1
        where cd.ID in (SELECT ID
                        FROM (
                                     SELECT t1.ID,
                                            IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID),
                                               0) AS ischild
                                     FROM (SELECT ID, PARENTID
                                           FROM core_dept t
                                           ORDER BY ID, PARENTID
                                                  ) t1,
                                          (SELECT @pids := #{deptId}) t2
                                     ) t3
                        WHERE ISCHILD != 0
                           OR ID = #{deptId})
    </update>


    <select id="CountNum" parameterType="Long" resultType="Long">
        select count(de.ID)
        from core_dept cd
                     left join core_dept de on cd.ID = de.PARENTID
        where cd.ID = #{id}
    </select>



    <!--	Dtree部门树-->
    <select id="getDeptTree" resultMap="deptChildrenMap">
        select z.*, t2.DISCRIPTION parentName
        from core_dept z
                     left join (select * from dic_datadetail where DICCODE = 'deptType') t2
                on z.DEPTTYPE = t2.DICVALUE
                where 1 = 1
        <if test="orderCode != null and orderCode != ''">
            and z.ORDERCODE like concat('%',#{orderCode},'%')
        </if>
        <if test="parentId != null">
            and z.PARENTID = #{parentId}
        </if>
        <if test="id != null and id != ''">
            and z.ID = #{id}
        </if>
        <if test="adminRegionId != null and adminRegionId != ''">
            and z.ADMINREGIONID = #{adminRegionId}
        </if>
        and z.ISSHOW = 1
                order by z.DEPTCODE asc
    </select>

    <!--	Dtree判断是否为叶子节点-->
    <select id="isChildNode" resultType="java.lang.Integer">
        select count(*)
        from core_dept z where 1 = 1
        <if test="parentId != null and parentId != ''">
            and z.PARENTID = #{parentId}
        </if>
        and z.ISSHOW = 1
                order by z.DEPTCODE asc
    </select>

    <!-- 获取部门列表id数组 -->
    <select id="getAllDeptId" resultType="String">
        select id
        from core_dept
        where ORDERCODE like concat('%',#{code},'%')
          and ISSHOW = 1
    </select>

    <select id="syslist" resultMap="BaseMap">
        select *
        from core_dept cd
        where cd.ISSYS = 1
        order by cd.DEPTCODE asc
    </select>

    <select id="deptTreeDataPopUp" resultType="com.fwy.corebasic.entity.Core_Dept">
        select c.ID, c.DEPTNAME, c.PARENTID, a.SELECTED from core_dept c
                left join (select ID, 1 selected
                           from core_dept where cast(ID as char) in
                (
        <foreach collection="selectDeptId" item="selectId" index="index" separator=",">
            #{selectId,jdbcType=VARCHAR}
        </foreach>
        ) )a
                on c.ID = a.ID
                where c.ISSHOW = 1
        <if test="deptId != null">
            and  c.ID in (SELECT ID
                          FROM (
                                       SELECT t1.ID,
                                              IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID),
                                                 0) AS ischild
                                       FROM (SELECT ID, PARENTID
                                             FROM core_dept t
                                             ORDER BY ID, PARENTID
                                                    ) t1,
                                            (SELECT @pids := #{deptId}) t2
                                       ) t3
                          WHERE ISCHILD != 0
                             OR ID = #{deptId})
        </if>
    </select>

    <select id="getObjectById" parameterType="long"
            resultType="com.fwy.corebasic.entity.Core_Dept">
        select cd.*, de.DEPTNAME parentName
        from core_dept cd
                     left join core_dept de on de.ID = cd.PARENTID
        where cd.ID = #{id}
    </select>

    <select id="getObjectByCode" resultMap="BaseMap">
        select *
        from CORE_DEPT
        where DEPTCODE = #{code}
    </select>
    <update id="showAll">
        update core_dept
        set ISSHOW=#{isShow},
            VERSION=VERSION + 1
        where ID in (SELECT ID
                     FROM (
                                  SELECT t1.ID,
                                         IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID),
                                            0) AS ischild
                                  FROM (SELECT ID, PARENTID
                                        FROM core_dept t
                                        ORDER BY ID, PARENTID
                                               ) t1,
                                       (SELECT @pids := #{id}) t2
                                  ) t3
                     WHERE ischild != 0
                        OR ID = #{id})
    </update>

    <select id="getNextDeptId" resultType="java.lang.Long">
        SELECT AUTO_INCREMENT
        FROM information_schema.`TABLES`
        WHERE TABLE_NAME = 'core_dept'
        order by AUTO_INCREMENT desc
        limit 1
    </select>

    <insert id="insertMunicipalData" useGeneratedKeys="true">
        insert into core_dept (ID, PARENTID, ORDERCODE, ISSYS, DEPTNAME, ISSHOW,
                               REMARK, DEPTCODE, CREATETIME, DEPTTYPE,MUNICIPAL_DEPT_ID)
        values (#{id}, 0, #{orderCode}, 1, #{deptName}, 1, #{remark}, #{deptCode},
                sysdate(), 6,#{municipalDeptId,jdbcType=VARCHAR})
    </insert>

    <select id="getDeptNum" resultType="int">
      select ifnull(count(*),0) from core_dept where DEPTCODE = #{deptCode}
      and MUNICIPAL_DEPT_ID = #{municipalDeptId}
</select>
    <select id="getchildsid" resultType="Integer">
        select m.ID from core_dept m where
            LOCATE( concat(',',m.id,','),concat(',',getChildFromDept(#{deptid})))>0
    </select>
    <select id="getLongChilds" resultType="java.lang.Long">
        select getChildFromDept(#{deptId}) from core_dept
    </select>
    <select id="getBranchList" resultType="com.fwy.corebasic.entity.Core_Dept">
        select * from core_dept where PARENTID = #{deptId} AND LEVEL = 2
    </select>
    <select id="getCurrentBranchIds" resultType="com.fwy.corebasic.entity.Core_Dept">
        select * from core_dept where PARENTID = #{deptId} AND LEVEL = 0
    </select>
    <select id="getDeptByDeptName" resultType="com.fwy.corebasic.entity.Core_Dept">
        select *
        from core_dept
        where DEPTNAME = #{parentDeptName}
    </select>
    <select id="getBranchIds" resultType="com.fwy.corebasic.entity.Core_Dept">
        select * from core_dept where PARENTID = #{deptId}
    </select>

</mapper>