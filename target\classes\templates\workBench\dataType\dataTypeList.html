<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>字典类型管理</title>
    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}">
    <script type="text/javascript" th:src="@{/admin/layui/layui.js}"></script>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-inline {
            width: 200px;
        }
    </style>
    <!--<style>
        #search {
            margin-bottom: 5px;
        }

        /*固定input宽度*/
        .layui-input, .layui-textarea {
            display: block;
            width: 180px;
            padding-left: 10px;
        }

    </style>-->
    <!--<script th:replace="loading::loading"></script>-->
</head>
<body>
<!--<div th:replace="loading::demo"></div>-->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <blockquote class="layui-elem-quote quoteBox" id="search">
                        <div class="layui-form-item layui-row">
                            <div class="layui-col-md4 layui-col-sm4 layui-col-xs4 layui-col-lg4 layui-form">
                                <label class="layui-form-label">字典类型：</label>
                                <div class="layui-input-inline ">
                                    <input class="layui-input" id="searchValue" autocomplete="off"
                                           placeholder="请输入字典类型">
                                </div>
                            </div>
                            <div class="layui-col-md4 layui-col-sm4 layui-col-xs4 layui-col-lg4">
                                <!--<label class="layui-form-label "></label>-->
                                <button class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询
                                </button>
                                <button class="layui-btn" id="unset_Btn"><i class="layui-icon">&#xe669;</i>重置</button>
                            </div>
                        </div>
                    </blockquote>
                    <table class="layui-hide" id="dateType_table" lay-filter="dateType_table"></table>

                    <script type="text/html" id="topToolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i
                                    class="layui-icon">&#xe608;</i>增加
                            </button>
                            <button class="layui-btn layui-btn-sm" lay-event="add_detial"><i
                                    class="layui-icon">&#xe608;</i>增加字典详情
                            </button>
                            <!--<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i
                                    class="layui-icon">&#xe642;</i>编辑
                            </button>-->
                            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i
                                    class="layui-icon">&#xe640;</i>删除
                            </button>
                        </div>
                    </script>

                    <script type="text/html" id="activeToolbar">
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                        <a class="layui-btn layui-btn-xs" lay-event="detail">字典详情</a>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script th:inline="javascript">

    $("blockquote").on('keyup', 'input,select', function (e) {
        if (e.keyCode == 13) {//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#search_btn").trigger("click");//触发搜索按钮的点击事件
        }
    });
    layui.config({
        base: ctx + '/plugins/'
    }).extend({
        //treetable: '/treetable-lay/treetable'
    }).use(['layer', 'form', 'table'], function () {
        var $ = layui.jquery;
        var table = layui.table;
        var treetable = layui.treetable;
        var form = layui.form;
        table.render({
            elem: '#dateType_table'
            , url: ctx + '/dataTypeController/dateTypeJson'
            , toolbar: '#topToolbar'
            , defaultToolbar: []
            // ,height:'full-50'
            , page: true
            , cols: [
                [
                    {type: 'checkbox', width: '5.1%'}
                    , {field: 'id', title: 'ID', hide: true}
                    , {field: 'dicType', title: '字典类型', width: '20%', align: 'center'}
                    , {field: 'dicCode', title: '字典代码', width: '15%', align: 'center'}
                    , {field: 'orderCode', title: '排序码', width: '10%', align: 'center'}
                    , {field: 'createTime', title: '创建时间', width: '10%', align: 'center'}
                    , {field: 'updateTime', title: '修改时间', width: '10%', align: 'center'}
                    , {
                    field: 'isStart',
                    title: '是否启用',
                    width: '10%',
                    fixed: 'right',
                    align: 'center',
                    templet: function (data) {
                        var html = '';
                        if (data.isStart) {
                            html += '<input type="checkbox" value=' + data.id + '  checked=true name="isStart" id="isStart" lay-skin="switch" lay-filter="isStart" lay-text="启用|停用">'
                        } else {
                            html += '<input type="checkbox" value=' + data.id + ' name="isStart" id="isStart" lay-skin="switch" lay-filter="isStart" lay-text="启用|停用">'
                        }
                        return html;
                    }
                }, {
                    field: 'updateTime',
                    title: '操作',
                    align: 'center',
                    width: '20%',
                    fixed: 'right',
                    toolbar: '#activeToolbar'
                }
                ]
            ]
            , request: {
                pageName: 'pageNum' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
        });
        var active = {
            reload: function () {
                var dicType = $("#searchValue").val();
                table.reload('dateType_table', {
                    page: {
                        curr: 1
                    },
                    where: {dicType: dicType}
                })
            }
        };
        $("#search_btn").click(function () {
            var type = 'reload';
            active[type] ? active[type].call(this) : '';
        });
        $("#unset_Btn").click(function () {
            $("#search :input").val("");
            table.reload('dateType_table', {
                page: {
                    curr: 1
                },
                where: null
            })
        });
        $(document).keydown(function (event) {
            if (event.keyCode == 13) {
                var type = 'reload';
                active[type] ? active[type].call(this) : '';
            }
        });
        //头工具栏事件
        table.on('toolbar(dateType_table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            var data = checkStatus.data;
            var submitButId = '#submitBut';
            switch (obj.event) {
                case 'add_btn':
                    var width = 600,
                        height = 450
                    var url = ctx + '/dataTypeController/addDataType',
                        title = '新增字典类型';
                    xadmin.openWindow(url, title, width, height, submitButId);
                    break;
                case 'edit_btn':
                    if (data.length == 1) {
                        var width = 600,
                            height = 450
                        var url = ctx + 'dataTypeController/editDataType?id=' + data[0].id,
                            title = '编辑字典类型';
                        xadmin.openWindow(url, title, width, height, submitButId);
                    } else {
                        layer.msg("请选择一条数据")
                    }
                    break;
                case 'delete_btn':
                    if (data.length == 1) {
                        deleteUser(data[0].id, data[0].dicCode);
                    } else {
                        layer.msg("请选择一条数据");
                    }
                    break;
                case 'add_detial':
                    if (data.length == 1) {
                        var width = 600,
                            height = 450
                        var url = ctx + 'dataDetailController/addDataDetail?dicCode=' + data[0].dicCode,
                            title = '新增字典数据';
                        xadmin.openWindow(url, title, width, height, submitButId);
                    } else {
                        layer.msg("请选择一条数据");
                    }
                    break;
            }
        });
        //监听行工具事件
        table.on('tool(dateType_table)', function (obj) {
            var data = obj.data;
            var submitButId = '#submitBut';
            if (obj.event === 'del') {
                // 删除函数
                deleteUser(data.id, data.dicCode);
            } else if (obj.event === 'edit') {
                var width = 600,
                    height = 450
                var url = ctx + 'dataTypeController/editDataType?id=' + data.id,
                    title = '编辑字典类型';
                xadmin.openWindow(url, title, width, height, submitButId);
            } else if (obj.event === 'detail') {
                var urlstr = ctx + 'dataTypeController/dataDetail?dicCode=' + data.dicCode;
                var tit = '字典详情';
                top.layui.index.openTabsPage(urlstr, tit);
            }
        });

        function deleteUser(id, dicCode) {
            var url = ctx + 'dataTypeController/deleteDataType?id=' + id + '&dicCode=' + dicCode;
            layer.confirm('您确定要删除？', {icon: 3, btn: ['确定', '取消'], title: '提示'}, function () {
                var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                $.ajax({
                    url: url,
                    success: function (res) {
                        layer.close(loading);
                        if (res.code == 0) {
                            layer.closeAll('loading');
                            layer.msg(res.msg, {icon: 1});
                            location.reload();
                        } else {
                            layer.closeAll('loading');
                            layer.msg(res.msg, {icon: 2});
                        }

                    },
                    error: function (res) {
                        layer.close(loading);
                        layer.msg(res.msg, {icon: 2});
                    }
                })
            })
        }

        form.on('switch(isStart)', function (data) {
            var id = data.value;
            var isStart = this.checked ? '1' : '0';
            var url = ctx + 'dataTypeController/changeStart?id=' + id + '&isStart=' + isStart;
            $.ajax({
                url: url,
                type: "POST",
                async: true,
                cache: false,
                data: {"id": id, "isStart": isStart},
                success: function (result) {
                    if (res.code == 0) {
                        layer.closeAll('loading');
                        layer.msg("更新成功!", {icon: 6, skin: 'layer-ext-moon'});
                    } else {
                        layer.closeAll('loading');
                        layer.msg("更新失败!", {icon: 5, skin: 'layer-ext-moon'});
                    }
                },
                error: function (data) {
                    layer.closeAll('loading');
                    layer.msg("更新失败!", {icon: 5, skin: 'layer-ext-moon'});
                }
            });
        });
    });
</script>

</body>
</html>