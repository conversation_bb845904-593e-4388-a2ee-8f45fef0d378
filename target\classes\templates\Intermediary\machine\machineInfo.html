<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>修改设备信息</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
    <script th:src="@{/scripts/security/crypto-js.js}"></script>
    <script th:src="@{/scripts/security/front_aes.js}"></script>
    <script th:src="@{/scripts/security/signature.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .layui-form {
            padding-top: 30px;
        }

        .input-wrapper .layui-input,
        .layui-form-select {
            width: 250px;
        }

        .input-wrapper img {
            height: 150px;
        }

        .layui-form-item .layui-form-label {
            width: 100px;
        }

        .layui-form-item .layui-input-block {
            margin-left: 140px;
        }

        .layui-form-item .layui-form-text {
            line-height: 38px;
            display: inline-block;
            vertical-align: middle;
        }
        xm-select {
            min-height: 36px;
            line-height: 36px;
            padding-right: 10px;
        }

    </style>

</head>
<body>
<form class="layui-form" lay-filter="edit-form">
    <input type="hidden" name="code" autocomplete="off"
           th:value="${machineConfig.code}">
    <div class="layui-form-item">
        <label class="layui-form-label">设备类型</label>
        <div class="layui-input-block input-wrapper">
            <select name="type" lay-verify="required" id="typeSelect">
                <option value="">请选择设备类型</option>
                <option value="1" th:selected="${machineConfig.type == 1}">超脑</option>
                <option value="0" th:selected="${machineConfig.type == 0}">摄像头</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" ><span
                style="color:red">*</span>所属部门：</label>
        <div class="layui-input-block input-wrapper">
            <div class="xm-select" id="deptIdSelect"  style="width: 250px">
            </div>
            <input type="hidden" id="deptId" th:value="${machineConfig?.deptId}">
            <input type="hidden" id="deptName" name="deptName" th:value="${machineConfig?.deptName}">

        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">设备所处通道号</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" name="channel" id="channel" autocomplete="off" placeholder="请输入通道号"
                   lay-verify="required" class="layui-input" th:value="${machineConfig.channel}">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">设备账号</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" name="machineName" autocomplete="off" placeholder="请输入设备账号"
                   lay-verify="required" class="layui-input" th:value="${machineConfig.machineName}">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">ip</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" name="ip" lay-verify="required" autocomplete="off" placeholder="请输入设备访问ip"
                   class="layui-input"
                   th:value="${machineConfig.ip}">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">密码</label>
        <div class="layui-input-block input-wrapper">
            <input type="password" name="password" lay-verify="password" autocomplete="off" placeholder="请输入密码"
                   class="layui-input" id="password" th:value="${machineConfig.password}">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">确认密码</label>
        <div class="layui-input-block input-wrapper">
            <input type="password" name="confirmPassword" lay-verify="password" autocomplete="off" placeholder="请确认密码"
                   class="layui-input" id="confirmPassword" th:value="${machineConfig.password}">
            <i class="layui-icon layui-icon-eye lay-eye" id="togglePassword"></i>
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block input-wrapper">
            <input type="text" value="有效" readonly class="layui-input">
        </div>
        <input type="hidden" name="stateId" value="1">
    </div>


    <div class="layui-form-item" th:if="${machineConfig.code != null}">
        <label class="layui-form-label">最后操作用户</label>
        <div class="layui-input-block">
            <span id="updateUser" class="layui-form-text">[[${machineConfig.updateUser}]]</span>
        </div>
    </div>

    <div class="layui-form-item" th:if="${machineConfig.code != null}">
        <label class="layui-form-label">最后操作时间</label>
        <div class="layui-input-block">
            <span id="updateTime" class="layui-form-text">[[${machineConfig.updateTime}]]</span>
        </div>
    </div>

    <div class="layui-form-item" th:if="${machineConfig.code != null}">
        <label class="layui-form-label">添加时间</label>
        <div class="layui-input-block">
            <span class="layui-form-text">[[${machineConfig.createTime}]]</span>
        </div>
    </div>

    <!-- 添加其他表单项 -->

    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="submit" id="subBtn">保存</button>
            <button type="reset" class="layui-btn layui-btn-primary" onclick="resetForm()">重置</button>
        </div>
    </div>
</form>

<script>


    function reloadXmselect(){
        $.ajax({
            url: ctx + "deptController/tree",
            data:{deptId:$("#deptId").val()},
            method: "get",
            dataType: 'json',
            success: function (response) {
                dialog_deptId.update({
                    data: response.data
                })
                dialog_deptId.changeExpandedKeys(true);
            },
            error: function (res) {
            }
        });

    }

    $(function () {
        reloadXmselect();
        $('#typeSelect').change(function() {
            var selectedValue = $(this).val();
            var verifyInput = $('#channel'); // 假设有一个需要验证的输入框

            if (selectedValue == 0) {
                verifyInput.attr('lay-verify', 'required');
            } else {
                verifyInput.removeAttr('lay-verify');
            }
        });
    });
    var dialog_deptId = xmSelect.render({
        el: '#deptIdSelect',
        filterable: true,
        name: 'deptId',
        tips: '请选择',

        model: {label: {type: 'block'}},
        template:function(item) {
            return '<p title="' + item.name + '">' + item.name + '</p>';
        },
        verify: {
            required: true,
            message: '请选择所属部门'
        },
        on: function (data) {
            //arr:  当前多选已选中的数据
            var arr = data.arr;
            //change, 此次选择变化的数据,数组
            var change = data.change;
            //isAdd, 此次操作是新增还是删除
            var isAdd = data.isAdd;

        },

        radio: true,//单选多选
        tree: {
            show: true,
            strict: false, //是否父子结构，父子结构父节点不会被选中
            indent: 30,//间距
            expandedKeys: true,
            clickCheck: true,
            clickExpand: true,//点击展开
        },
        clickClose: true,//点击关闭
        autoRow: true,
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width: '228px'
        },
        prop: {
            name: "deptName",
            value: "ID"
        },
        height: '200px',
        empty: '暂无数据',
        data: [],
        direction: 'auto',
    });

    layui.use(['form', 'layer'], function () {
        var form = layui.form;
        var layer = layui.layer;

        $('#togglePassword').click(function () {
            var confirmPasswordInput = $('#confirmPassword');
            var passwordType = confirmPasswordInput.attr('type');
            var newType = passwordType == 'password' ? 'text' : 'password';
            confirmPasswordInput.attr('type', newType);

            // 切换图标样式
            $(this).toggleClass('layui-icon-eye layui-icon-eye-close');
        });
        // 自定义密码校验规则
        form.verify({
            password: function (value, item) {
                if (value.length < 6) {
                    return '密码长度不能小于6位';
                }
                if (value !== $('#password').val()) {
                    return '两次密码输入不一致';
                }
            }
        });

        // 表单提交监听
        form.on('submit(submit)', function (data) {

            var formData = new FormData();

            // 将其他字段数据也添加到formData中
            for (var key in data.field) {
                formData.append(key, data.field[key]);
            }

            $.ajax({
                url: ctx + 'machineConfigController/updateOrSave',
                type: 'POST',
                data: JSON.stringify(data.field),
                contentType: 'application/json',
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg('保存成功');
                        // 关闭弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        // 刷新父页面数据
                        parent.location.reload();
                    } else {
                        layer.msg(res.msg);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Ajax 请求发生错误:', error);
                }
            });

            return false; // 阻止表单提交
        });

        form.render();
    });

    // 重置表单及图像
    function resetForm() {
        var form = layui.form;
        var $ = layui.jquery;

        // 重置表单项
        form.val("edit-form", {});

    }
</script>
<script>
    layui.use('layer', function () {
        var layer = layui.layer;
        $('#image-preview').on('click', function () {
            var imageUrl = $(this).attr('src');
            layer.photos({
                photos: {
                    title: '', // 图片标题（可选）
                    data: [
                        {
                            src: imageUrl // 大图的 URL
                        }
                    ],
                    minHeight: 600
                },
                anim: 5 // 切换动画类型，可根据需要调整
            });
        });

    });

</script>
</body>
</html>