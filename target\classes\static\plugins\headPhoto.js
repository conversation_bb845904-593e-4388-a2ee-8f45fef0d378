$(document).ready(function(){
    //为外面的盒子绑定一个点击事件
    $("#uploadImgBtn").click(function(){
        /*
        1、先获取input标签
        2、给input标签绑定change事件
        3、把图片回显
         */
//      1、先回去input标签
        var $input = $("#file");
//      2、给input标签绑定change事件
        $input.on("change" , function(){
            //补充说明：因为我们给input标签设置multiple属性，因此一次可以上传多个文件
            //获取选择图片的个数
            var idObject = document.getElementsByTagName('img');
                console.log(idObject);
            if (idObject.length>0){
                idObject[0].parentNode.removeChild(idObject[0]);
            }

            var files = this.files;
            var length = files.length;
            var div = document.getElementById("picd"),
                img = document.createElement("img");
            div.appendChild(img);
            console.log("选择了"+length+"张图片");
            //3、回显
                var fr = new FileReader();
                fr.onload = function(e){
                    console.log("回显了图片")
                    img.src = this.result;
                }
                fr.readAsDataURL(files[length-1]);//读取文件
            var formData = new FormData();
            formData.append("file", $("#file")[0].files[0]);
            $.ajax({
                url:ctx + "/upload",
                type:"post",
                data:formData,
                dataType:"json",
                processData: false,//用于对data参数进行序列化处理 这里必须false
                contentType: false, //必须*/
                beforeSend: function () {
                    console.log("正在进行，请稍候");
                },
                success:function(data){
                    if(data.code == 0){
                        $("#idPhoto").val(data.icoImgStr);
                    }else{
                        layer.msg("图片上传失败");
                    }
                }
            })
        })
    })
})