<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>[[${application.sysBaseInfo.sysName}]]</title>
    <!-- Tell the browser to be responsive to screen width -->
    <head>
        <meta charset="utf-8">
        <title>登入</title>
        <meta name="renderer" content="webkit">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="viewport"
              content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
        <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}" media="all">
        <link rel="stylesheet" th:href="@{/admin/layui/css/admin.css}" media="all">
        <link rel="stylesheet" th:href="@{/admin/layui/css/login.css}" media="all">
        <link rel="stylesheet" th:href="@{/plugins/particles/style.css}" media="all">
        <style class="cp-pen-styles">
            body {
                background-color: white;
            }

            #particles-js {
                position: absolute;
            }

            .layadmin-user-login {
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                position: absolute;
            }

            .layadmin-user-login-codeimg {
                /* 与input高度一致 */
                height: 38px;
                border-style: solid;
                border-width: 1px;
                background-color: #fff;
                border-radius: 2px;
                border-color: #e6e6e6;
            }

            .layadmin-user-login {
                padding: unset;
                min-height: unset;
            }
        </style>
        <script type="text/javascript">
            if (top.location != location) {
                top.location.href = location.href;
            }
        </script>
    </head>
<body>
<!--  <div id="particles-js"></div>-->
<div class="layadmin-user-login layadmin-user-display-show" id="LAY-user-login" style="display: none;">
    <div class="layadmin-user-login-box layadmin-user-login-header">
        <h2>[[${application.sysBaseInfo.sysName}]]</h2>
        <h3>[[${application.sysBaseInfo.englishName}]]</h3>
        <!--  <h3>wechat service platform</h3> -->
    </div>
    <div class="layadmin-user-login-main">

        <form class="layui-form layadmin-user-login-box layadmin-user-login-body" method="post" id="ajaxform">
            <input id="signature" type="hidden" name="signature"/>
            <input id="password" name="password" class="signature" type="password" style="display:none;"/>
            <div class="layui-form-item">
                <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="userName"></label>
                <input type="text" name="userName" id="userName" lay-verify="userName" placeholder="用户名"
                       class="layui-input signature">
            </div>
            <div class="layui-form-item">
                <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="showpw"></label>
                <input type="password" name="showpw" id="showpw" lay-verify="password" placeholder="密码"
                       class="layui-input">
            </div>
            <div class="layui-form-item"
                 th:if="${application.loginValidateInfo == null || application.loginValidateInfo.checkCode == true }">
                <div class="layui-row">
                    <div class="layui-col-xs7">
                        <label class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="checkCode"></label>
                        <input type="text" name="checkCode" id="checkCode" lay-verify="checkCode" placeholder="验证码"
                               class="layui-input signature">
                    </div>
                    <div class="layui-col-xs5">
                        <div style="margin-left: 10px;">
                            <img src="./validateCode" class="layadmin-user-login-codeimg" id="imgs">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-bottom: 20px;">
                <input type="checkbox" name="remeberMe" lay-skin="primary" title="记住密码">
                <div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>记住密码</span><i
                        class="layui-icon layui-icon-ok"></i></div>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="login" id="btnLogin" type="button">登
                    录
                </button>
            </div>
        </form>
    </div>


    <!--<div class="ladmin-user-login-theme">
      <script type="text/html" template>
        <ul>
          <li data-theme=""><img src="{{ layui.setter.base }}style/res/bg-none.jpg"></li>
          <li data-theme="#03152A" style="background-color: #03152A;"></li>
          <li data-theme="#2E241B" style="background-color: #2E241B;"></li>
          <li data-theme="#50314F" style="background-color: #50314F;"></li>
          <li data-theme="#344058" style="background-color: #344058;"></li>
          <li data-theme="#20222A" style="background-color: #20222A;"></li>
        </ul>
      </script>
    </div>-->

</div>
<div class="layui-trans layadmin-user-login-footer">
    <input type="hidden" id="title" th:value="${application.sysBaseInfo?.copyrightInfo}">
    <div id="copyrightInfo"></div>
</div>
<script th:src="@{/admin/layui/layui.js}"></script>
<script th:src="@{/plugins/jquery/jquery-3.4.1.min.js}"></script>
<script th:src="@{/scripts/security/crypto-js.js}"></script>
<script th:src="@{/scripts/security/front_aes.js}"></script>
<script th:src="@{/scripts/security/signature.js}"></script>
<script th:src="@{/scripts/main/userlogin.js}"></script>

<script th:src="@{/plugins/particles/particles.min.js}"></script>
<script th:src="@{/plugins/particles/app.js}"></script>

<style id="LAY_layadmin_theme">
    .layui-side-menu, .layadmin-pagetabs .layui-tab-title li:after,
    .layadmin-pagetabs .layui-tab-title li.layui-this:after,
    .layui-layer-admin .layui-layer-title, .layadmin-side-shrink .layui-side-menu .layui-nav > .layui-nav-item > .layui-nav-child {
        background-color: #20222A !important;
    }

    .layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a,
    .layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a {
        background-color: #009688 !important;
    }

    .layui-layout-admin .layui-logo {
        background-color: #20222A !important;
    }
</style>


<div class="layui-layer-move"></div>
<script>
    $(function () {
        var value = $('#title').val();
        $('#copyrightInfo').append(value);
    });
</script>
</body>
</html>
