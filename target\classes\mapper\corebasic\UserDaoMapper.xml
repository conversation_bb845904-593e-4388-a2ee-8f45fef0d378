<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IUserDao">
    <resultMap type="com.fwy.corebasic.entity.Core_User" id="userMap">
        <id property="id" column="id" javaType="Long" jdbcType="INTEGER"/>
        <result property="userName" column="USERNAME" javaType="String"
                jdbcType="VARCHAR"/>
        <result property="deptId" column="DEPTID" javaType="Long"
                jdbcType="INTEGER"/>
        <result property="password" column="PASSWORD" javaType="String"
                jdbcType="VARCHAR"/>
        <result property="fullName" column="FULLNAME" javaType="String"
                jdbcType="VARCHAR"/>
        <result property="phone" column="PHONE" javaType="String"
                jdbcType="VARCHAR"/>
        <result property="isStart" column="ISSTART"/>
        <result property="isSys" column="ISSYS"/>
        <result property="createTime" column="CREATETIME" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result property="isDel" column="ISDEL"/>
        <result property="lastLoginTime" column="LASTLOGINTIME"
                javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="lastLoginIp" column="LASTLOGINIP" javaType="String"
                jdbcType="VARCHAR"/>
        <result property="appLastLoginTime" column="LASTLOGINTIME"
                javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <association property="userInfo" javaType="com.fwy.corebasic.entity.Core_User_Check">
            <id property="id" column="ID" javaType="Long" jdbcType="INTEGER"/>
            <result property="id" column="ID"/>
            <result property="createBy" column="CREATEBY"/>
            <result property="idCardNumber" column="IDCARDNUMBER"/>
            <result property="pcCode" column="PC_CODE"/>
            <result property="payCode" column="PAYCODE"/>
            <result property="payType" column="PAYTYPE"/>
            <result property="goodsRemark" column="GOODSREMARK"/>
            <result property="isAdmin" column="ISADMIN"/>
            <result property="policeManPhoto" column="POLICEMANPHOTO"/>
            <result property="jybh" column="JYBH"/>
            <result property="gmsfhm" column="GMSFHM"/>
            <result property="yhyxq" column="YHYXQ"/>
            <result property="sfxgmm" column="SFXGMM"/>
            <result property="mmyxq" column="MMYXQ"/>
            <result property="allowIp" column="ALLOWIP"/>
            <result property="sfjy" column="SFJY"/>
            <result property="yhyxqs" column="YHYXQS"/>
            <result property="mmyxqs" column="MMYXQS"/>
            <result property="jyw" column="JYW"/>
            <result property="jyws" column="JYWS"/>
            <result property="position" column="POSITION"/>
            <result property="degree" column="DEGREE"/>
            <result property="politicalStatus" column="POLITICALSTATUS"/>
        </association>
        <association property="dept" javaType="com.fwy.corebasic.entity.Core_Dept">
            <id property="id" column="DEPTID" javaType="Long" jdbcType="INTEGER"/>
            <result property="parentId" column="PARENTID" javaType="Long"
                    jdbcType="INTEGER"/>
            <result property="orderCode" column="ORDERCODE" javaType="String"
                    jdbcType="VARCHAR"/>
            <result property="isSys" column="ISSYS"/>
            <result property="deptName" column="DEPTNAME" javaType="String"
                    jdbcType="VARCHAR"/>
            <result property="isShow" column="ISSHOW"/>
            <result property="remark" column="REMARK" javaType="String"
                    jdbcType="VARCHAR"/>
            <result property="deptCode" column="DEPTCODE" javaType="String"
                    jdbcType="VARCHAR"/>
        </association>
        <collection property="roles" ofType="com.fwy.corebasic.entity.Core_Role"
                    javaType="ArrayList">
            <id property="id" column="rid" javaType="Long" jdbcType="INTEGER"/>
            <result property="roleName" column="ROLENAME" javaType="String"
                    jdbcType="VARCHAR"/>
            <result property="iconCssClass" column="ICONCSSCLASS"
                    javaType="String" jdbcType="VARCHAR"/>

            <collection property="auths" ofType="com.fwy.corebasic.entity.Core_Auth"
                        javaType="ArrayList">
                <id property="id" column="aid" javaType="Long" jdbcType="INTEGER"/>
                <result property="authName" column="AUTHNAME" javaType="java.lang.String"
                        jdbcType="VARCHAR"/>
                <collection property="actions"
                            ofType="com.fwy.corebasic.entity.Core_ActionForAuth" javaType="ArrayList">
                    <id property="id" column="AAID" javaType="Long" jdbcType="INTEGER"/>
                    <result property="controlName" column="CONTRONAME" javaType="String"
                            jdbcType="VARCHAR"/>
                    <result property="actionName" column="ACTIONNAME" javaType="String"
                            jdbcType="VARCHAR"/>
                    <result property="display" column="DISPLAY" javaType="String"
                            jdbcType="VARCHAR"/>
                </collection>
            </collection>
        </collection>
    </resultMap>
    <!-- 列表 -->
    <select id="list" resultMap="userMap">
        select cd.DEPTNAME, cu.*
        from core_user cu
                     join core_dept cd on cu.DEPTID = cd.ID ${whereSql}
    </select>
    <!-- 列表 -->
    <select id="list1" resultMap="userMap">
        select * from(
                select cd.DEPTNAME, cu.*, ck.IDCARDNUMBER
                from core_user cu
                             left join core_dept cd on cu.DEPTID = cd.ID
                             left join core_user_check ck on cu.ID = ck.ID
               <where>
                   <if test="idCard !=null and idCard !=''">
                    and ck.IDCARDNUMBER like concat('%',trim(#{idCard}),'%')
                   </if>
                   <if test="fullName != null and fullName != ''">
                       and cu.FULLNAME like concat('%', trim(#{fullName}), '%')
                   </if>
                   <if test="phone != null and phone != ''">
                       and cu.PHONE like concat('%', trim(#{phone}), '%')
                   </if>
                   <if test="deptId != null">
                       and cu.DEPTID = #{deptId}
                   </if>
               </where>

        ) B
                where (
                              select count(1)
                              from (
                                           select bw.ACCOUNT
                                           from black_white bw
                                           where bw.TYPE = 1
                                           ) A
                              where A.ACCOUNT = B.USERNAME) = 0
    </select>
    <!-- 部门ID查找用户 -->
    <select id="getDeptId" resultMap="userMap">
        select cd.DEPTNAME, cu.*
        from core_user cu
                     join core_dept cd on
                cu.DEPTID = cd.ID
        where cu.DEPTID in
              (SELECT ID
               FROM (
                            SELECT t1.ID,
                                   IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID), 0) AS ischild
                            FROM (SELECT ID, PARENTID
                                  FROM core_dept t
                                  ORDER BY ID, PARENTID
                                         ) t1,
                                 (SELECT @pids := #{deptId}) t2
                            ) t3
               WHERE ischild != 0
                  OR ID = #{deptId})
    </select>
    <!--获取单个对象 -->
    <select id="getObjectById" parameterType="Long" resultMap="userMap">
        select cd.DEPTNAME,
       cd.ID       did,
       cu.*,
       t.*,
       cr.ID       as rid,
       cr.ROLENAME
      from core_user cu
      left join core_dept cd
        on cu.DEPTID = cd.ID
      left join core_userforrole cufr
        on cu.ID = cufr.USERID
      left join core_user_check t
        on cu.ID = t.ID
      left join core_role cr
        on cr.ID in (select core_userforrole.ROLEID
                   from core_userforrole
                  where core_userforrole.USERID = #{id})
      where cu.ID =#{id}
    </select>
    <!--根据用户名查询单个对象 -->
    <select id="getByUserName" parameterType="String" resultMap="userMap">
        SELECT U.*,
               R.ID  RID,
               R.ROLENAME,
               A.ID  AID,
               A.AUTHNAME,
               U.LASTLOGINTIME,
               U.CREATETIME,
               AA.ID AAID,
               AA.CONTRONAME,
               AA.ACTIONNAME,
               AA.DISPLAY,
               CD.ID DID,
               CD.DEPTNAME,
               CD.DEPTCODE,
               CD.ORDERCODE,
               CD.PARENTID,
               CUI.*
        from core_user U
                     LEFT JOIN core_userforrole UR ON U.ID = UR.USERID
                     LEFT JOIN core_role R ON UR.ROLEID = R.ID
                     LEFT JOIN core_authforrole AR ON R.ID = AR.ROLEID
                     LEFT JOIN core_auth A ON AR.AUTHID = A.ID
                     LEFT JOIN core_actionforauth AA ON A.ID = AA.AUTHID
                     LEFT JOIN core_dept CD ON U.DEPTID = CD.ID
                     LEFT JOIN core_user_check CUI ON U.ID = CUI.ID
        where U.USERNAME = #{name}
        and U.ISSTART = 1
    </select>
    <!--根据用户名查询单个对象 -->
    <select id="getByUserByName" parameterType="string" resultMap="userMap">
        SELECT U.*, CK.*
        FROM core_user U
                     LEFT JOIN core_user_check CK
                ON U.ID = CK.ID
        WHERE U.USERNAME = #{userName}
    </select>
    <!--保存 -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" parameterType="com.fwy.corebasic.entity.Core_User">
        INSERT INTO core_user(ID,
                              DEPTID,
                              USERNAME,
                              PASSWORD,
                              FULLNAME,
                              PHONE,
                              ISSTART,
                              ISSYS,
                              CREATETIME)
        VALUES (#{id,jdbcType=INTEGER},
                #{deptId,jdbcType=INTEGER},
                #{userName,jdbcType=VARCHAR},
                #{password,jdbcType=VARCHAR},
                #{fullName,jdbcType=VARCHAR},
                #{phone,jdbcType=VARCHAR},
                #{isStart,jdbcType=INTEGER},
                #{isSys,jdbcType=INTEGER},
                now())
    </insert>
    <insert id="save_check" parameterType="com.fwy.corebasic.entity.Core_User">
        <selectKey resultType="Long" order="BEFORE" keyProperty="id">
            SELECT ID AS ID
            FROM core_user
            WHERE USERNAME = #{userName}
        </selectKey>
        INSERT INTO core_user_check(ID,
                                    CREATEBY,
                                    IDCARDNUMBER,
                                    ISADMIN,
                                    JYBH,
                                    YHYXQ,
                                    MMYXQ,
                                    ALLOWIP,
                                    SFJY,
                                    YHYXQS,
                                    MMYXQS,
                                    JYW,
                                    POSITION,
                                    DEGREE,
                                    POLITICALSTATUS)
        values (#{id,jdbcType=VARCHAR},
                #{createBy,jdbcType=VARCHAR},
                #{idCardNumber,jdbcType=VARCHAR},
                #{isAdmin,jdbcType=INTEGER},
                #{jybh,jdbcType=VARCHAR},
                #{yhyxq,jdbcType=VARCHAR},
                #{mmyxq,jdbcType=DATE},
                #{allowIp,jdbcType=VARCHAR},
                #{sfjy,jdbcType=INTEGER},
                #{yhyxqs,jdbcType=DATE},
                #{mmyxqs,jdbcType=DATE},
                #{jyw,jdbcType=VARCHAR},
                #{position,jdbcType=INTEGER},
                #{degree,jdbcType=INTEGER},
                #{politicalStatus,jdbcType=INTEGER})
    </insert>
    <!--修改 -->
    <update id="update" parameterType="com.fwy.corebasic.entity.Core_User">
        update core_user set
                DEPTID=#{deptId,jdbcType=INTEGER},
                USERNAME=#{userName,jdbcType=VARCHAR},
        <if test="password != null and password != ''">
            PASSWORD=#{password,jdbcType=VARCHAR},
        </if>
        FULLNAME=#{fullName,jdbcType=VARCHAR},
        PHONE=#{phone,jdbcType=VARCHAR},
        ISSTART=#{isStart,jdbcType=INTEGER},
        ISSYS=#{isSys,jdbcType=INTEGER}
                where ID =
                      #{id}
    </update>
    <!-- 删除 -->
    <delete id="delete" parameterType="String">
        delete
        from core_user
        where LOCATE( concat(',', ID, ','),concat(',', #{ids}, ','))> 0
    </delete>
    <!--修改 UserInfo-->
    <update id="update_check" parameterType="com.fwy.corebasic.entity.Core_User">
        update core_user_check
        set IDCARDNUMBER    = #{idCardNumber,jdbcType=INTEGER},
            ISADMIN         = #{isAdmin,jdbcType=INTEGER},
            JYBH            = #{jybh,jdbcType=VARCHAR},
            YHYXQ           = #{yhyxq,jdbcType=VARCHAR},
            MMYXQ           = #{mmyxq,jdbcType=DATE},
            ALLOWIP         = #{allowIp,jdbcType=VARCHAR},
            SFJY            = #{sfjy,jdbcType=INTEGER},
            YHYXQS          = #{yhyxqs,jdbcType=DATE},
            MMYXQS          = #{mmyxqs,jdbcType=DATE},
            JYW             = #{jyw,jdbcType=VARCHAR},
            POSITION        = #{position,jdbcType=INTEGER},
            DEGREE          = #{degree,jdbcType=INTEGER},
            POLITICALSTATUS = #{politicalStatus,jdbcType=INTEGER}
        where ID =
              #{id}
    </update>
    <!--参数更新 -->
    <update id="updateForLogin" parameterType="com.fwy.corebasic.entity.Core_User">
        update core_user
        set LASTLOGINTIME = now(),
            LASTLOGINIP   = #{lastLoginIp}
        where ID = #{id}
    </update>
    <!-- 删除 -->
    <delete id="deleteCoreUser" parameterType="String">
        delete
        from core_user
        where LOCATE(  concat(',', ID, ','),concat(',', #{ids}, ','))> 0
    </delete>

    <delete id="deleteCoreUserRole" parameterType="String">
        delete
        from core_userforrole
        where LOCATE( concat(',', USERID, ','),concat(',', #{ids}, ','))> 0
    </delete>

    <delete id="deleteCoreUserCheck" parameterType="String">
        delete
        from core_user_check
        where LOCATE( concat(',', ID, ','),concat(',', #{ids}, ','))> 0
    </delete>

     <!-- 移除用户角色 -->
    <delete id="removeRolesById" parameterType="Long">
        delete
        from core_userforrole
        where USERID = #{userid}
    </delete>
    <!-- 储存用户角色 -->
    <insert id="addRoles" parameterType="com.fwy.corebasic.entity.Core_Role">
        insert into core_userforrole
        values (#{userid}, #{roleid})
    </insert>
    <!-- 根据角色名字查询角色id -->
    <select id="getRoleIdByRoleName" parameterType="string"
            resultType="long">
        select ID
        from core_role
        where ROLENAME = #{rolename}
    </select>
    <!--    <select id="count">-->
    <!--		select count(*) from core_dept c where-->
    <!--		c.parentid=#{id}-->
    <!--	</select>-->
    <!-- 设置用户的启动状态 -->
    <update id="setStart">
        update core_user
        set ISSTART=#{isstart}
        where ID = #{id}
    </update>
    <!--根据用户名获取部门ID -->
    <select id="getdeptid" resultType="long">
        select DEPTID
        from CORE_USER t
        where USERNAME = #{username}
    </select>
    <!-- 胡山林用户名查询 -->
    <select id="getObjectByName" resultMap="userMap">
        SELECT T.ID,
               T.USERNAME,
               T.PASSWORD,
               T.FULLNAME,
               T.DEPTID,
               T.PHONE,
               T.ISSTART,
               T.ISSYS,
               T.CREATETIME,
               T.ISDEL,
               T.LASTLOGINTIME,
               T.LASTLOGINIP
        FROM core_user T
        where USERNAME = #{username}
    </select>
    <!-- 胡山林警员编号查询 -->
    <select id="getObjectByJYBH" resultMap="userMap">
        SELECT T.ID,
               T.USERNAME,
               T.PASSWORD,
               T.FULLNAME,
               T.DEPTID,
               T.PHONE,
               T.ISSTART,
               T.ISSYS,
               T.CREATETIME,
               T.ISDEL,
               T.LASTLOGINTIME,
               T.LASTLOGINIP,
               C.CREATEBY
        FROM core_user T
                     LEFT JOIN core_user_check C
                ON T.ID = C.ID
        where C.JYBH = #{jybh}
    </select>

    <!--数目统计 -->
    <select id="userCount" resultType="Int">
        SELECT count(1)
        FROM core_user WHERE 1 = 1
        <if test="deptid != null and deptid != ''">
            and DEPTID in (SELECT ID
                           FROM (
                                        SELECT t1.ID,
                                               IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID),
                                                  0) AS ischild
                                        FROM (SELECT ID, PARENTID
                                              FROM core_dept t
                                              ORDER BY ID, PARENTID
                                                     ) t1,
                                             (SELECT @pids := #{deptid}) t2
                                        ) t3
                           WHERE ischild != 0
                              OR ID = #{deptid})
        </if>
    </select>

    <select id="list2" resultMap="userMap">
        select cd.DEPTNAME, cu.*, ck.IDCARDNUMBER
                from core_user cu
                left join core_dept cd
                on cu.DEPTID = cd.ID
                left join core_user_check ck
                on cu.ID = ck.ID
        <where>
        <if test="depts != null and depts.size() != 0">
            cu.DEPTID in
            <foreach collection="depts" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="userName != null and userName != ''">
            and cu.USERNAME like concat('%', #{userName}, '%')
        </if>
        <if test="fullName != null and fullName != ''">
            and cu.FULLNAME like concat('%', #{fullName}, '%')
        </if>
        <if test="phone != null and phone != ''">
            and cu.PHONE like concat('%', #{phone}, '%')
        </if>
        <if test="idCardNumber != null and idCardNumber != ''">
            and ck.IDCARDNUMBER like concat('%', #{idCardNumber}, '%')
        </if>
        <if test="deptidForSelect != null">
            and cu.DEPTID = #{deptidForSelect}
        </if>
        <if test="isStart != null">
            and cu.ISSTART = #{isStart}
        </if>
        </where>
    </select>
    <update id="updatePasswordValid">
        update core_user_check u
        set u.YHYXQ = u.YHYXQ + ifnull(ceil(now() - (select LASTLOGINTIME from core_user where ID = u.id)), 0),
            u.MMYXQ = u.MMYXQ + ifnull(ceil(now() - (select LASTLOGINTIME from core_user where ID = u.id)), 0)
        where u.ID = #{id}
    </update>
    <update id="editpassword" parameterType="com.fwy.corebasic.entity.Core_User">
        update core_user u
        set u.PASSWORD = #{password}
        where u.ID = #{id}
    </update>

    <select id="getObjectByIdCardNumber" resultMap="userMap">
        select ID, IDCARDNUMBER
        from core_user_check
        where IDCARDNUMBER = #{idCardNumber,jdbcType=VARCHAR}
    </select>

    <!--修改 -->
    <update id="updateSelf" parameterType="com.fwy.corebasic.entity.Core_User">
        update core_user cu
                left join core_user_check cuc on cu.ID = cuc.ID
        set cu.USERNAME=#{userName,jdbcType=VARCHAR},
            cu.FULLNAME=#{fullName,jdbcType=VARCHAR},
            cu.PHONE=#{phone,jdbcType=VARCHAR},
            cuc.IDCARDNUMBER = #{idCardNumber}
        where cu.ID = #{id};

    </update>

    <select id="getUserListByRoleIDAndDeptID" resultMap="userMap">
        select cu.*
        from core_user cu
                     join core_userforrole cur on cur.USERID = cu.ID
        <where>
            <if test="deptId != null">
                cu.DEPTID = #{deptId}
            </if>
            and cast(cur.ROLEID as char) in
            <foreach collection="roles" item="role" index="index" open="(" separator="," close=")">
                #{role}
            </foreach>
        </where>
    </select>

    <select id="userDatPopUp" resultType="com.fwy.corebasic.entity.Core_User">
        select u.ID, u.FULLNAME, a.SELECTED from core_user u
                left join (select ID, 1 SELECTED
                           from core_user where cast(ID as char) in
                (
        <foreach collection="userIdString" item="userId" index="index" separator=",">
            #{userId,jdbcType=VARCHAR}
        </foreach>
        ) )a
                on u.ID = a.ID
    </select>

    <update id="updatePwdAndJyw">
        update core_user cu
        left join core_user_check cuc on cu.ID = cuc.ID
        set cu.PASSWORD=#{password},
         cuc.JYW=#{jyw}
         where cu.ID = #{id,jdbcType=VARCHAR};
    </update>

    <update id="updateJywsById" parameterType="java.lang.String">
        update s_core_user
        set ISSTART='0',
            JYWS='1'
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getAuditPersonList" resultMap="userMap">
        select cd.DEPTNAME, cu.*
        from core_user cu
                     left join core_userforrole cuf
                on cuf.USERID = cu.ID
                     left join CORE_DEPT cd
                on cd.ID = cu.DEPTID
        <where>
            <if test="userIds != null and userIds != ''">
                and OCATE(concat(',', cu.ID, ','),concat(',', #{userIds}, ',')) > 0
            </if>
            and  cast(cuf.ROLEID as char) in
            <foreach collection="roleIdList" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </where>
    </select>
</mapper>