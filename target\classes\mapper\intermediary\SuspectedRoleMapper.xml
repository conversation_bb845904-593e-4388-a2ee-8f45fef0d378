<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.intermediary.dao.SuspectedRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleMap" type="com.fwy.intermediary.entity.SuspectedRole">
        <id column="code" property="code" />
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="expiration" property="expiration" />
        <result column="counts" property="counts" />
        <result column="type" property="type" />
        <result column="date_range" property="dateRange" />
        <result column="state_id" property="stateId" />
    </resultMap>

    <select id="findByCondition" parameterType="com.fwy.intermediary.entity.SuspectedRole" resultMap="roleMap">
        select *
        from suspected_role
        <where>
            <if test="deptId != null">
                AND dept_id = #{deptId}
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
        </where>

    </select>
</mapper>
