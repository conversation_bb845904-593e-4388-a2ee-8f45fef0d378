function SHA256(message) {
    return CryptoJS.SHA256(message).toString(CryptoJS.enc.Hex);
}
// 生成数据散列码，在需要参与散列的input元素的class属性中添加signature
function signature(signatureForm) {
    //
    var signatureArray = [];
    signatureArray.push("userName" + "=" + $("#userName").val());
    signatureArray.push("password" + "=" + $("#password").val());
    signatureArray.push("checkCode" + "=" + $("#checkCode").val());
    var join = signatureArray.join('&');
    return SHA256(join);
}