/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.2 (2020-08-17)
 */
!function(){"use strict";var n,t=tinymce.util.Tools.resolve("tinymce.PluginManager"),y=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),f=tinymce.util.Tools.resolve("tinymce.EditorManager"),m=tinymce.util.Tools.resolve("tinymce.Env"),h=tinymce.util.Tools.resolve("tinymce.util.Tools"),d=function(t){return t.getParam("importcss_selector_converter")},i=(n="array",function(t){return r=typeof(e=t),(null===e?"null":"object"==r&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==r&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":r)===n;var e,r}),o=Array.prototype.push,l=function(t,e){return function(t){for(var e=[],r=0,n=t.length;r<n;++r){if(!i(t[r]))throw new Error("Arr.flatten item "+r+" was not an array, input: "+t);o.apply(e,t[r])}return e}(function(t,e){for(var r=t.length,n=new Array(r),i=0;i<r;i++){var o=t[i];n[i]=e(o,i)}return n}(t,e))},p=function(e){return"string"==typeof e?function(t){return-1!==t.indexOf(e)}:e instanceof RegExp?function(t){return e.test(t)}:e},_=function(s,t,a){var u=[],r={};function l(t,e){var r,n,i,o=t.href;if(n=o,i=m.cacheSuffix,"string"==typeof n&&(n=n.replace("?"+i,"").replace("&"+i,"")),(o=n)&&a(o,e)&&!function(t,e){var r,n=!1!==(r=t.getParam("skin"))&&(r||"oxide");if(n){var i=t.getParam("skin_url"),o=i?t.documentBaseURI.toAbsolute(i):f.baseURL+"/skins/ui/"+n,c=f.baseURL+"/skins/content/";return e===o+"/content"+(t.inline?".inline":"")+".min.css"||-1!==e.indexOf(c)}return!1}(s,o)){h.each(t.imports,function(t){l(t,!0)});try{r=t.cssRules||t.rules}catch(c){}h.each(r,function(t){t.styleSheet?l(t.styleSheet,!0):t.selectorText&&h.each(t.selectorText.split(","),function(t){u.push(h.trim(t))})})}}h.each(s.contentCSS,function(t){r[t]=!0}),a=a||function(t,e){return e||r[t]};try{h.each(t.styleSheets,function(t){l(t)})}catch(e){}return u},x=function(t,e){var r,n=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(e);if(n){var i=n[1],o=n[2].substr(1).split(".").join(" "),c=h.makeMap("a,img");return n[1]?(r={title:e},t.schema.getTextBlockElements()[i]?r.block=i:t.schema.getBlockElements()[i]||c[i.toLowerCase()]?r.selector=i:r.inline=i):n[2]&&(r={inline:"span",title:e.substr(1),classes:o}),!1!==t.getParam("importcss_merge_classes")?r.classes=o:r.attributes={"class":o},r}},P=function(t,e){return null===e||!1!==t.getParam("importcss_exclusive")},r=function(g){g.on("init",function(t){var e,r,n,i,o=(e=[],r=[],n={},{addItemToGroup:function(t,e){n[t]?n[t].push(e):(r.push(t),n[t]=[e])},addItem:function(t){e.push(t)},toFormats:function(){return l(r,function(t){var e=n[t];return 0===e.length?[]:[{title:t,items:e}]}).concat(e)}}),v={},c=p(g.getParam("importcss_selector_filter")),s=(i=g.getParam("importcss_groups"),h.map(i,function(t){return h.extend({},t,{original:t,selectors:{},filter:p(t.filter),item:{text:t.title,menu:[]}})})),a=function(t,e){if(f=t,p=v,!(P(g,m=e)?f in p:f in m.selectors)){a=t,l=v,P(g,u=e)?l[a]=!0:u.selectors[a]=!0;var r=(o=(i=g).plugins.importcss,c=t,((s=e)&&s.selector_converter?s.selector_converter:d(i)?d(i):function(){return x(i,c)}).call(o,c,s));if(r){var n=r.name||y.DOM.uniqueId();return g.formatter.register(n,r),h.extend({},{title:r.title,format:n})}}var i,o,c,s,a,u,l,f,m,p;return null};h.each(_(g,g.getDoc(),p(g.getParam("importcss_file_filter"))),function(r){if(-1===r.indexOf(".mce-")&&(!c||c(r))){var t=(n=s,i=r,h.grep(n,function(t){return!t.filter||t.filter(i)}));if(0<t.length)h.each(t,function(t){var e=a(r,t);e&&o.addItemToGroup(t.title,e)});else{var e=a(r,null);e&&o.addItem(e)}}var n,i});var u=o.toFormats();g.fire("addStyleModifications",{items:u,replace:!g.getParam("importcss_append")})})};!function e(){t.add("importcss",function(t){return r(t),e=t,{convertSelectorToFormat:function(t){return x(e,t)}};var e})}()}();