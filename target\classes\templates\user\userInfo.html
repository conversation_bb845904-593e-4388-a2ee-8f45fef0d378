<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>用户信息</title>
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
    <script th:src="@{/scripts/security/crypto-js.js}"></script>
    <script th:src="@{/scripts/security/front_aes.js}"></script>
    <script th:src="@{/scripts/security/signature.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <style>

        .layui-form-label {
            width: 150px;
        }

        .layui-input-inline {
            width: 250px;
        }

        .layui-input {
            width: 240px;
        }
    </style>
</head>
<script th:inline="javascript">
    $(function () {
        $("#password").val('');

        var yhyxqsStr = [[${yhyxqs}]];
        var yhyxqStr = [[${yhyxq}]];
        var mmyxqsStr = [[${mmyxqs}]];
        var mmyxqStr = [[${mmyxq}]];
        /*var yhyxqsStr = $('#yhyxqs').val();
        var yhyxqStr = $('#yhyxq').val();
        var mmyxqsStr = $('#mmyxqs').val();
        var mmyxqStr = $('#mmyxq').val();*/

        if (yhyxqsStr == null || yhyxqsStr == '') {
            // 用户有效期始默认当天
            var date = new Date();
            $("#yhyxqs").val(formatDate(date));
        }

        if (yhyxqStr == null || yhyxqStr == '') {
            // 用户有效期止默认30天
            var date = new Date();
            date.setDate(date.getDate() + 30);
            $("#yhyxq").val(formatDate(date));
        }

        if (mmyxqsStr == null || mmyxqsStr == '') {
            // 密码有效期始默认当天
            var date = new Date();
            $("#mmyxqs").val(formatDate(date));
        }

        if (mmyxqStr == null || mmyxqStr == '') {
            // 密码有效期止默认15天
            var date = new Date();
            date.setDate(date.getDate() + 15);
            $("#mmyxq").val(formatDate(date));
        }
    })

    function formatDate(date) {
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        return y + '-' + m + '-' + d;
    };
</script>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title"
                      style="margin-top: 20px;">
                <legend>用户信息</legend>
            </fieldset>

            <form class="layui-form" action="">
                <input type="hidden" name="id" id="id" th:value="${user?.id}">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>用户名：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="userName" lay-verify="required|unique" th:if="${user==null}"
                                   placeholder="请输入用户名" oninput="del(this,'blank|char')" autocomplete="off" th:value="${user?.userName}"
                                   class="layui-input">
                            <input type="text" name="userName" lay-verify="required|unique" th:if="${user!=null}"
                                   placeholder="请输入用户名" autocomplete="off" th:value="${user?.userName}"
                                   class="layui-input" readonly>
                        </div>
                    </div>
                    <div class="layui-inline" th:if="${user==null}">
                        <label class="layui-form-label"><span style="color:red">*</span>密码：</label>
                        <div class="layui-input-inline">
                            <input type="password"  autocomplete=new-password placeholder="请输入密码" oninput="del(this,'blank|char')" name="password" id="password" lay-verify="required|passwordLength"
                                   class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>姓名：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="fullName" lay-verify="required|fullnameLength"
                                   placeholder="请输入姓名" oninput="del(this,'blank|char')" autocomplete="off" th:value="${user?.fullName}"
                                   class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>身份证号：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="idCardNumber" id="idCardNumber" lay-verify="required|uniqueCard"
                                   placeholder="请输入身份证号" oninput="del(this,'blank|char')" autocomplete="off" th:value="${userInfo?.idCardNumber}"
                                   class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">


                    <div class="layui-inline">
                        <label class="layui-form-label">警员编号(员工编号)：</label>
                        <div class="layui-input-inline">
                            <input type="text" id="jybh" name="jybh" lay-verify="uniqueJybh"
                                   placeholder="请输入警员编号(员工编号)" oninput="del(this,'blank|char')" autocomplete="off" th:value="${userInfo?.jybh}"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>角色权限：</label>
                        <div class="layui-input-inline" style="width: 240px">

                            <div class="layui-input-inline" style="width: 240px">
                                <input type="hidden"  th:value="${roleIds}" id="roleIds">
                                <div class="xm-select" id="roleIdSelect">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>用户有效期始：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="yhyxqs" id="yhyxqs" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(userInfo?.yhyxqs, 'yyyy-MM-dd')}"
                                       class="layui-input" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>用户有效期止：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="yhyxq" id="yhyxq" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(userInfo?.yhyxq, 'yyyy-MM-dd')}"
                                       class="layui-input" readonly>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>密码有效期始：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="mmyxqs" id="mmyxqs" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(userInfo?.mmyxqs, 'yyyy-MM-dd')}"
                                       class="layui-input" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>密码有效期止：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="mmyxq" id="mmyxq" lay-verify="date"
                                       autocomplete="off" th:value="${#dates.format(userInfo?.mmyxq, 'yyyy-MM-dd')}"
                                       class="layui-input" readonly>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>联系电话：</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="phone" lay-verify="required|phone" placeholder="请输入电话号码"
                                   th:value="${user?.phone}" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="margin-left: 0px"><span
                                style="color:red">*</span>所属部门：</label>
                        <div class="layui-input-inline" style="width: 240px">
                            <div class="xm-select" id="deptIdSelect">
                            </div>
                            <input type="hidden" id="deptId" th:value="${user?.deptId}">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">职位：</label>
                        <div class="layui-input-inline" style="width: 240px">
                            <input type="hidden"  th:value="${userInfo?.position}" id="position">
                            <div class="xm-select" id="positionSelect">
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 100px">学历：</label>
                        <div class="layui-input-inline" style="width: 240px">
                            <input type="hidden"  th:value="${userInfo?.degree}" id="education">
                            <div class="xm-select" id="educationSelect">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">政治面貌：</label>
                        <div class="layui-input-inline" style="width: 240px">

                            <input type="hidden"  th:value="${userInfo?.politicalStatus}" id="politicalStatus">
                            <div class="xm-select" id="politicalStatusSelect">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>允许登录IP：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="allowIp" oninput="del(this,'blank|char')" lay-verify="required|allowIp"
                                   placeholder="请输入允许登录IP" autocomplete="off" th:value="${userInfo?.allowIp}"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>创建用户：</label>
                        <div class="layui-input-inline">
                            <input type="text" placeholder="" autocomplete="off" th:value="${userName}"
                                   class="layui-input" disabled="disabled">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">是否警员:</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" value="1" th:attr=" checked=${userInfo?.sfjy == 1 ? 1 : 0}"
                                   name="sfjy" id="sfjy" lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">启用状态:</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" value="1"
                                   th:attr="checked=${user?.isStart == 1 ? 1 : 0}" name="isStart" id="isStart"
                                   lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline" th:if="${session.user != null &&session.user.isSys==1}">
                        <label class="layui-form-label">是否系统级:</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" value="1" th:attr="checked=${user?.isSys == 1 ? 1 : 0}"
                                   name="isSys" id="isSys" lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">是否超级管理员:</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" value="1" th:attr="checked=${userInfo?.isAdmin == 1 ? 1 : 0}"
                                   name="isAdmin" id="isAdmin" lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                </div>

                <div class="layui-form-item" style="text-align: center">
                    <button class="layui-btn" lay-submit="" lay-filter="demo1">保存</button>
                    <button class="layui-btn layui-btn-normal" id="cancelBtn">返回</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function timeRender(attrName, value, minOrMax, type) {
        //每当日期被选择完的时候重新渲染另外一个日期组件  限制最大或者是最小值  就不会出现日期重叠的情况。
        // alert(minOrMax);
        var date = new Date();
        layui.use(['laydate'], function () {
            var laydate = layui.laydate;
            $('input[name$=' + attrName + ']').each(function () {
                $(this).removeAttr('lay-key');
                var clone = $(this).clone().appendTo($(this).parent().empty())[0];//解决最大和最少值无法重新渲染问题
                if (type == 1) {
                    //表示开始日期
                    var secDate = laydate.render({
                        elem: clone,
                        value: value,
                        btns: ['clear', 'confirm'],
                        max: minOrMax == '' || minOrMax == null ? date.getFullYear() + 1 + '-12-31' : minOrMax,  //如果为空或者为null会限制当天选择  给一个初始值
                        // type: 'datetime',
                        trigger: 'click',
                        done: function (value, date, endDate) {
                            this.value = value;
                            this.elem.val(value)
                            // if (value!=null&&value!=''){
                            //     value=new Date(value)
                            //     value.setDate(value.getDate()+1)
                            // }
                            timeRender("yhyxq", $("#yhyxq").val(), value, 0)
                            $("#yhyxq").click();
                        },
                        change: function (value, date) { //监听日期被切换
                            var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            start = new Date(Date.parse(start));
                            var end = $("#yhyxq").val().replace("-", "/");
                            end = new Date(Date.parse(end));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                secDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                } else {
                    var secDate = laydate.render({
                        elem: clone,
                        value: value,
                        min: minOrMax == '' || minOrMax == null ? '2010-12-31' : minOrMax, //初始最小限制时间
                        // type: 'datetime',
                        btns: ['clear', 'confirm'],
                        trigger: 'click',
                        done: function (value, date, endDate) {
                            this.value = value
                            this.elem.val(value)
                            // if (value!=null&&value!=''){
                            //      value=new Date(value)
                            //     value.setDate(value.getDate()-1)
                            // }
                            timeRender('yhyxqs', $("#yhyxqs").val(), value, 1)
                        },
                        change: function (value, date) { //监听日期被切换
                            var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            end = new Date(Date.parse(end));
                            var start = $("#yhyxqs").val().replace("-", "/");
                            start = new Date(Date.parse(start));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                secDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                }

            })
        });
    }

    function mmTimeRender(attrName, value, minOrMax, type) {
        //每当日期被选择完的时候重新渲染另外一个日期组件  限制最大或者是最小值  就不会出现日期重叠的情况。
        // alert(minOrMax);
        var date = new Date();
        layui.use(['laydate'], function () {
            var laydate = layui.laydate;
            $('input[name$=' + attrName + ']').each(function () {
                $(this).removeAttr('lay-key');
                var clone = $(this).clone().appendTo($(this).parent().empty())[0];//解决最大和最少值无法重新渲染问题
                if (type == 1) {
                    //表示开始日期
                    var mmSecDate = laydate.render({
                        elem: clone,
                        value: value,
                        btns: ['clear', 'confirm'],
                        max: minOrMax == '' || minOrMax == null ? date.getFullYear() + 1 + '-12-31' : minOrMax,  //如果为空或者为null会限制当天选择  给一个初始值
                        // type: 'datetime',
                        trigger: 'click',
                        done: function (value, date, endDate) {
                            this.value = value
                            this.elem.val(value)
                            // if (value!=null&&value!=''){
                            //     value=new Date(value)
                            //     value.setDate(value.getDate()+1)
                            // }
                            mmTimeRender("mmyxq", $("#mmyxq").val(), value, 0)
                            $("#mmyxq").click();
                        },
                        change: function (value, date) { //监听日期被切换
                            var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            start = new Date(Date.parse(start));
                            var end = $("#mmyxq").val().replace("-", "/");
                            end = new Date(Date.parse(end));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                mmSecDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                } else {
                    var mmSecDate = laydate.render({
                        elem: clone,
                        value: value,
                        min: minOrMax == '' || minOrMax == null ? '2010-12-31' : minOrMax, //初始最小限制时间
                        // type: 'datetime',
                        btns: ['clear', 'confirm'],
                        trigger: 'click',
                        done: function (value, date, endDate) {
                            this.value = value
                            this.elem.val(value)
                            // if (value!=null&&value!=''){
                            //      value=new Date(value)
                            //     value.setDate(value.getDate()-1)
                            // }
                            mmTimeRender('mmyxqs', $("#mmyxqs").val(), value, 1)
                        },
                        change: function (value, date) { //监听日期被切换
                            var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                            end = new Date(Date.parse(end));
                            var start = $("#mmyxqs").val().replace("-", "/");
                            start = new Date(Date.parse(start));
                            if (start > end) { //点击2017年8月15日，弹出提示语
                                mmSecDate.hint('开始时间不得大于结束时间');
                            }
                            return false;
                        }
                    });
                }

            })
        });
    }
    var formSelects = layui.formSelects;
    layui.use(['form', 'layedit', 'laydate', 'jquery'],
        function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laydate = layui.laydate,
                $ = jQuery = layui.$;

            // 回到列表页面
            $('#cancelBtn').click(function () {
                window.parent.changTabs(ctx + 'userController/userList', '', '用户列表');
            })




            //日期
            var yhEnd = laydate.render({
                elem: '#yhyxq',
                // type: 'datetime',
                trigger: 'click',
                btns: ['clear', 'confirm'],
                min: $("#yhyxqs").val() == '' || $("#yhyxqs").val() == null ? '2010-12-31' : $("#yhyxqs").val(), //初始最小限制时间
                done: function (value, date, endDate) {
                    this.value = value;
                    this.elem.val(value);
                    timeRender('yhyxqs', $("#yhyxqs").val(), value, 1)
                },
                change: function (value, date) { //监听日期被切换
                    var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                    end = new Date(Date.parse(end));
                    var start = $("#yhyxqs").val().replace("-", "/");
                    start = new Date(Date.parse(start));
                    if (start > end) { //点击2017年8月15日，弹出提示语
                        yhEnd.hint('开始时间不得大于结束时间');
                    }
                    return false;
                }
            });
            var yhStart = laydate.render({
                elem: '#yhyxqs',
                // type: 'datetime',
                max: $("#yhyxq").val() == '' || $("#yhyxq").val() == null ? '2099-12-31' : $("#yhyxq").val(),
                trigger: 'click',
                btns: ['clear', 'confirm'],
                done: function (value, date, endDate) {
                    this.value = value
                    this.elem.val(value)
                    timeRender('yhyxq', $("#yhyxq").val(), value, 0)
                    $("#yhyxq").click();
                },
                change: function (value, date) { //监听日期被切换
                    var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                    start = new Date(Date.parse(start));
                    var end = $("#yhyxq").val().replace("-", "/");
                    end = new Date(Date.parse(end));
                    if (start > end) {
                        yhStart.hint('开始时间不得大于结束时间');
                    }
                    return false;
                }
            });

            var mmEnd = laydate.render({
                elem: '#mmyxq',
                // type: 'datetime',
                trigger: 'click',
                btns: ['clear', 'confirm'],
                min: $("#mmyxqs").val() == '' || $("#mmyxqs").val() == null ? '2010-12-31' : $("#mmyxqs").val(), //初始最小限制时间
                done: function (value, date, endDate) {
                    this.value = value
                    this.elem.val(value)
                    mmTimeRender('mmyxqs', $("#mmyxqs").val(), value, 1)
                },
                change: function (value, date) { //监听日期被切换
                    var end = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                    end = new Date(Date.parse(end));
                    var start = $("#mmyxqs").val().replace("-", "/");
                    start = new Date(Date.parse(start));
                    if (start > end) { //点击2017年8月15日，弹出提示语
                        mmEnd.hint('开始时间不得大于结束时间');
                    }
                    return false;
                }
            });
            var mmStart = laydate.render({
                elem: '#mmyxqs',
                // type: 'datetime',
                max: $("#mmyxq").val() == '' || $("#mmyxq").val() == null ? '2099-12-31' : $("#mmyxq").val(),
                trigger: 'click',
                btns: ['clear', 'confirm'],
                done: function (value, date, endDate) {
                    this.value = value
                    this.elem.val(value)
                    mmTimeRender('mmyxq', $("#mmyxq").val(), value, 0)
                    $("#mmyxq").click();
                },
                change: function (value, date) { //监听日期被切换
                    var start = date.year + '/' + date.month + '/' + date.date + ' ' + date.hours + ':' + date.minutes + ':' + date.seconds
                    start = new Date(Date.parse(start));
                    var end = $("#mmyxq").val().replace("-", "/");
                    end = new Date(Date.parse(end));
                    if (start > end) {
                        mmStart.hint('开始时间不得大于结束时间');
                    }
                    return false;
                }
            });

            form.on('switch(statusFilter)', function (data) {
                var checked = data.elem.checked;
                if (checked) {
                    $("#isStart").val(true);
                } else {
                    $("#isStart").val(false);
                }
                form.render('switch');//重新渲染下拉框
            });


            form.render();

            form.verify({
                uniqueCard: function (value) {
                    var id = $("#id").val();
                    var checkMsg = '';
                    var url = ctx + "userController/uniqueIdCard?idCardNumber=" + value + "&id=" + id;
                    $.ajax({
                        url: url,
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            if (result) {
                                checkMsg += '身份证重复';
                                return checkMsg;
                            }
                        }, error: function () {
                            layer.msg("身份证验证失败");
                        }
                    });
                    if (checkMsg != '') {
                        return checkMsg;
                    }
                },
                uniqueJybh: function (value) {
                    if (top.window.parent.getBytes(value) >20){
                        return "警员编号不能超过20个字符";
                    }
                    //var orginJybh = [[${user==null?'-1':user?.jybh}]];
                    var id = $("#id").val();
                    var checkMsg = '';
                    var url = ctx + "userController/uniqueJybh?jybh=" + value + "&id=" + id;
                    if (value.length>0) {
                        $.ajax({
                            url: url,
                            datatype: 'json',
                            async: false,
                            success: function (result) {
                                if (result) {
                                    checkMsg += '警员编号重复';
                                    return checkMsg;
                                }
                            }, error: function () {
                                layer.msg("警员编号验证失败");
                            }
                        });
                    }
                    if (checkMsg != '') {
                        return checkMsg;
                    }
                },
                validip: function (value, item) {
                    var reg = /^((0[0-9]|1[0-9]\d{1,2})|(2[0-5][0-5])|(2[0-4][0-9])|(\d{1,2}))\.((0[0-9]|1[0-9]\d{1,2})|(2[0-5][0-5])|(2[0-4][0-9])|(\d{1,2}))\.((0[0-9]|1[0-9]\d{1,2})|(2[0-4][0-9])|(2[0-5][0-5])|(\d{1,2}))\.((0[0-9]|1[0-9]\d{1,2})|(2[0-4][0-9])|(2[0-5][0-5])|(\d{1,2}))$/;

                    if (!reg.test(value)) {
                        return "客户端IP校验不通过";
                    }
                },
                unique: function (value, item) {
                    var id = $("#id").val();
                    var checkMsg = '';
                    var url = ctx + "userController/uniqueData?userName=" + value + "&id=" + id;
                    $.ajax({
                        url: url,
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            if (result) {
                                checkMsg += '用户名重复';
                                return checkMsg;
                            }
                        }, error: function () {
                            layer.msg("用户名校验失败");
                        }
                    });
                    if (checkMsg != '') {
                        return checkMsg;
                    }
                    if (top.window.parent.getBytes(value) < 3) {
                        return "用户名长度3到32个字符";
                    }
                    if (top.window.parent.getBytes(value)  > 32) {
                        return "用户名长度3到32个字符";
                    }
                },
                passwordLength: function (value) {
                    // 判断密码长度不低于8位
                    if (!/\S{8,}/.test(value)) {
                        return "密码长度不低于8位";
                    }
                    // 判断密码是否包含数字
                    if (!/.*\d+.*/.test(value)) {
                        //console.log("密码必须包含数字");
                        return "密码必须包含数字";
                    }
                    // 判断密码是否包含字母
                    if (!/.*[a-zA-Z]+.*/.test(value)) {
                        //console.log("密码必须包含字母");
                        return "密码必须包含字母";
                    }
                    // 判断密码是否包含特殊符号
                    if (!/.*[~!@#$%^&*()_+.]+.*/.test(value)) {
                        //console.log("密码必须包含特殊符号");
                        return "密码必须包含特殊符号";
                    }
                },
                fullnameLength: function (value) {
                    if (top.window.parent.getBytes(value) < 2) {
                        return "姓名长度2到20个字符";
                    }
                    if (top.window.parent.getBytes(value) > 20) {
                        return "姓名长度2到20个字符";
                    }
                }
            });

            form.on('submit(demo1)', function(data) {
                if(data.field.password != null){
                    data.field.password = RSAEncryptService(data.field.password);
                }
                var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
                $.ajax({
                    type : "POST",
                    url : "saveUser",
                    data : data.field,
                    dataType : "json",
                    success : function(data) {
                        if (data.code == '0') {
                            layer.alert(data.msg, {
                                    icon : 6, skin: 'layer-ext-moon',closeBtn: 0},
                                function(){
                                    window.parent.changTabs( ctx +'userController/userList','','用户列表');
                                });
                        } else {
                            layer.msg(data.msg, {icon : 5,skin : 'layer-ext-moon'});
                        }
                        layer.close(loading);
                    },
                    error : function(data) {
                        layer.close(loading);
                        layer.msg(data.msg, {icon : 5,skin : 'layer-ext-moon'});
                    }
                });
                return false; //防止提交两次表单
            });
        });


</script>
<script type="text/javascript">

    function reloadXmselect(){

        $.ajax({
            url: ctx + "deptController/tree",
            data:{deptId:$("#deptId").val()},
            method: "get",
            dataType: 'json',
            success: function (response) {
                dialog_deptId.update({
                    data: response.data
                })
                dialog_deptId.changeExpandedKeys(true);
            },
            error: function (res) {
            }
        });

        //角色权限下拉框
        $.ajax({
            url : ctx + 'roleController/roleDialogJsonX',
            data: {"roleIds": $("#roleIds").val()},
            method: "get",
            dataType: 'json',
            success: function (response) {
                dialog_role.update({
                    data:response.data,
                });
            },
            error: function (res) {
            }
        });
        //职位下拉框
        $.get(ctx + '/dataDetailController/formSelectByCode?dicCode=position&id='+$("#position").val(),function (res) {
            dialog_position.update({
                data:res.data,
            });
        })
        //教育下拉框
        $.get( ctx + '/dataDetailController/formSelectByCode?dicCode=education&id='+ $("#education").val(),function (res) {
            dialog_edu.update({
                data:res.data,
            });
        })
        //政治面貌
        $.get( ctx + '/dataDetailController/formSelectByCode?dicCode=politicalStatus&id='+ $("#politicalStatus").val(),function (res) {
            dialog_politicalStatus.update({
                data:res.data,
            });
        })
    }

    $(function () {
        reloadXmselect();
    });
    var dialog_deptId = xmSelect.render({
        el: '#deptIdSelect',
        filterable: true,
        name: 'deptId',
        tips: '请选择',

        model: {label: {type: 'block'}},
        template:function(item) {
            return '<p title="' + item.name + '">' + item.name + '</p>';
        },
        on: function (data) {
            //arr:  当前多选已选中的数据
            var arr = data.arr;
            //change, 此次选择变化的数据,数组
            var change = data.change;
            //isAdd, 此次操作是新增还是删除
            var isAdd = data.isAdd;

        },

        radio: true,//单选多选
        tree: {
            show: true,
            strict: false, //是否父子结构，父子结构父节点不会被选中
            indent: 30,//间距
            expandedKeys: true,
            clickCheck: true,
            clickExpand: true,//点击展开
        },
        clickClose: true,//点击关闭
        autoRow: true,
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width: '228px'
        },
        prop: {
            name: "deptName",
            value: "ID"
        },
        height: '200px',
        empty: '暂无数据',
        data: [],
        direction: 'auto',
    });

    //政治面貌
    var dialog_politicalStatus = xmSelectRender("#politicalStatusSelect",[],'politicalStatus',true,'disCription','dicValue');
    //教育
    var dialog_edu = xmSelectRender("#educationSelect",[],'degree',true,'disCription','dicValue');
    //职位
    var dialog_position = xmSelectRender("#positionSelect",[],'position',true,'disCription','dicValue');
    //角色
    var dialog_role = xmSelectRender("#roleIdSelect",[],'roleIds',false,'roleName','id');

    //id：初始化 id ，
    //data: 喧嚷数据。
    //name: 提交时传递的名字
    //radio ：单选还是多选
    //dataName:自定义数据名称
    //dataValue：自定义数据的值
    function xmSelectRender(id,data,name,radio,dataName,dataValue) {
        //核验类型
        return  xmSelect.render({
            el: id,
            // filterable: true,
            name: name,
            tips: '请选择',
            // disabled: true,
            // layVerify: 'required|uniqueDept',
            // layVerify: 'required',
            // layVerType: 'msg',
            model: {label: {type: 'block'}},
            template:function(item) {
                return '<p title="' + item.name + '">' + item.name + '</p>';
            },
            on: function (data) {


            },

            radio: radio,//单选多选
            tree: {
                show: true,
                strict: false, //是否父子结构，父子结构父节点不会被选中
                indent: 10,//间距
                expandedKeys: [-1],
                clickCheck: true,
                clickExpand: true,//点击展开
            },
            clickClose: radio,//点击关闭
            autoRow: true,
            style: {
                paddingLeft: '10px',
                position: 'relative',
                width: '228px'
            },
            prop: {
                name: dataName,
                value: dataValue
            },
            height: '200px',
            empty: '暂无数据',
            data: data,
            direction: 'auto',
        });
    }
</script>
</body>
</html>