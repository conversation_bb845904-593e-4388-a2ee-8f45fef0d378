<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>用户信息</title>
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
    <script th:src="@{/scripts/security/crypto-js.js}"></script>
    <script th:src="@{/scripts/security/front_aes.js}"></script>
    <script th:src="@{/scripts/security/signature.js}"></script>
    <link rel="stylesheet"
          th:href="@{/plugins/formSelects/formSelects-v4.css}"/>
    <link rel="stylesheet"
          th:href="@{/plugins/headPhoto.css}"/>
    <style>

        .layui-form-label {
            width: 150px;
        }

        .layui-input-inline {
            width: 250px;
        }

        .layui-input {
            width: 240px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title"
                      style="margin-top: 20px;">
                <legend>用户信息</legend>
            </fieldset>

            <form class="layui-form" action="">
                <input type="hidden" name="id" id="id" th:value=${user?.id}>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>微信号：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="wename" lay-verify="required|unique" th:if="${user==null}"
                                   autocomplete="off" th:value="${user?.wename}"
                                   class="layui-input">
                            <input type="text" name="wename" lay-verify="required|unique" th:if="${user!=null}"
                                   autocomplete="off" th:value="${user?.wename}"
                                   class="layui-input" readonly>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>状态：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="status" id="status"
                                   autocomplete="off" class="layui-input" readonly>
                            <input type="hidden" id="statust" th:value="${user?.status}">
                        </div>
                    </div>

                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>昵称：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="weNickname" id="weNickname"
                                   autocomplete="off" th:value="${user?.weNickname}"
                                   class="layui-input" readonly>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>身份证号：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="idCardnumber" id="idCardnumber" lay-verify="required|identity|uniqueCard"
                                   autocomplete="off" th:value="${user?.idCardnumber}"
                                   class="layui-input" readonly>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>籍贯：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="wePlaceOfOrigin" id="wePlaceOfOrigin"
                                       autocomplete="off" th:value="${user?.wePlaceOfOrigin}"
                                       class="layui-input" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>联系电话：</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="phoneNumber"
                                   th:value="${user?.phoneNumber}" class="layui-input" readonly>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>创建时间：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="createTime" id="createTime"
                                       autocomplete="off" th:value="${#dates.format(user?.createTime, 'yyyy-MM-dd')}"
                                       class="layui-input" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>微信语言：</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="weLanguage" id="weLanguage"
                                       autocomplete="off" th:value="${user?.weLanguage}"
                                       class="layui-input" readonly>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">微信头像：</label>
                        <div class="pic">
                            <img th:src="${user?.weHeadImgUrl}" id="wxpic"/>
                        </div>
                        <input type="hidden" name="picid" id="picid" th:value="${user?.weHeadImgUrl}">
                    </div>
                </div>
                <div class="layui-form-item" style="text-align: center">
                    <button class="layui-btn layui-btn-normal" id="cancelBtn">返回</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(function () {
        var status=$('#statust').val();
        console.log(status)
        if(status==0){
            $('#status').val("未绑定");
        }else if(status==1){
            $('#status').val("绑定中");
        }else if(status==2){
            $('#status').val("已解绑");
        }
        var picid=$("#picid").val();
        console.log(picid);
        if(picid==''){
            $("#wxpic").attr("src",ctx+"haccountController/readPartImage?id=0")
        }
    });
    layui.use(['form', 'layedit', 'laydate', 'jquery'],
        function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laydate = layui.laydate,
                $ = jQuery = layui.$;

            // 回到列表页面
            $('#cancelBtn').click(function () {
                window.parent.changTabs(ctx + 'wxuserController/wxuserList', '', '微信用户列表');
            })
        });
</script>
</body>
</html>