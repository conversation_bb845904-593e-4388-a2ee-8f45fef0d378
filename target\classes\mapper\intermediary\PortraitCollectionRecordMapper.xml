<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.intermediary.dao.IPortraitCollectionRecordDao">
    <resultMap id="portraitRecord" type="com.fwy.intermediary.entity.PortraitCollectionRecord">
        <result column="code" property="code"/>
        <result column="person_code" property="personCode"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="camera_location" property="cameraLocation"/>
        <result column="person_url" property="personUrl"/>
        <result column="oss_url" property="ossUrl"/>
        <result column="state_id" property="stateId"/>
        <result column="create_time" property="createTime" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="findByCondition" parameterType="PortraitRecordCondition" resultMap="portraitRecord">
        select * from portrait_collection_record where state_id != 3
        <if test="personCode != null">
            and person_code = #{personCode}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="startDate != null">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            <!--&lt;小于-->
            and create_time &lt;= #{endDate}
        </if>
        order by create_time desc
    </select>

    <select id="getPeopleWarningCount" resultType="com.fwy.intermediary.entity.response.PeopleWarningCountResponse">
        SELECT
        COUNT(*) AS peopleWarningCount,
        HOUR(create_time) AS hour
        FROM portrait_collection_record
        WHERE person_code = #{personCode} AND HOUR(create_time) BETWEEN 8 AND 18 AND state_id != 3
        <if test="deptIds != null and deptIds.size() != 0">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index"
                     open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="startDate != null">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            <!--&lt;小于-->
            and create_time &lt;= #{endDate}
        </if>
<!--        <if test="status == 1">-->
<!--            and create_time between DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)-->
<!--            AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY)-->
<!--        </if>-->
<!--        <if test="status == 2">-->
<!--            and DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')-->
<!--        </if>-->
<!--        <if test="status == 3">-->
<!--            and DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(CURDATE(), '%Y-%m-%d')-->
<!--        </if>-->
<!--        <if test="status == 3">-->
<!--            and DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(CURDATE(), '%Y')-->
<!--        </if>-->
        GROUP BY HOUR(create_time)
        ORDER BY HOUR(create_time)
    </select>

</mapper>