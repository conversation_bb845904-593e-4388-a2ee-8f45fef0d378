<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
  <meta charset="utf-8">
  <title>任务管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <div th:replace="Importfile::html"></div>
  <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
  <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
   <script th:src="@{/scripts/security/main/openWay.js}"></script>
  <style>
  	.label-width{
  		width:100px;
  	}
  	.input-width{
  		width:75% !important;
  	}
  </style>
</head>
<body>
  
	<div id="" class="layui-layer-content" style="overflow: visible;">
	    <form id="modelAuthForm" lay-filter="modelAuthForm" class="layui-form">
	       	<div class="layui-form-item">
	          <input name="id" id="id" type="hidden" th:value="${task?.id}">
	        </div>
	        <div class="layui-form-item">
	            <label class="layui-form-label"><span style="color:red">*</span>任务名称</label>
	            <div class="layui-input-inline input-width">
	                  <input name="name" type="text" class="layui-input" maxlength="50"
	                  lay-vertype="tips" oninput="del(this,'blank|char')" lay-verify="required|uniqueName" th:value="${task?.name}">
	            </div>
	        </div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span style="color:red">*</span>任务表达式</label>
				<div class="layui-input-inline input-width">
					<input name="cron" id="cron" type="text" class="layui-input" maxlength="50" oninput="del(this,'blank')"
						   lay-vertype="tips" lay-verify="required|cron" th:value="${task?.cron}">
					<button type=button id="startBtn">生成表达式</button>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span style="color:red">*</span>任务类名称</label>
				<div class="layui-input-inline input-width">
					<input name="job_name" type="text" class="layui-input" maxlength="50"
						   lay-vertype="tips" oninput="del(this,'blank|char')" lay-verify="required|uniqueJob" th:value="${task?.job_name}">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span style="color:red">*</span>任务组名称</label>
				<div class="layui-input-inline input-width">
					<input name="job_group" type="text" class="layui-input" maxlength="50"
						   lay-vertype="tips" oninput="del(this,'blank|char')" lay-verify="required|jobGroup" th:value="${task?.job_group}">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">任务描述</label>
				<div class="layui-input-inline input-width">
					<textarea name="describe" type="text" class="layui-input" maxlength="50" lay-verify="describe" style="height: 100px;"
							  lay-vertype="tips" oninput="del(this,'blank|char')" th:text="${task?.describe}"></textarea>
				</div>
			</div>
	        <div class="layui-form-item">
						<label class="layui-form-label" style="width: 90px;">是否启用：</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="1" th:attr="checked=${task?.status=='1'?true:false}" name="status" lay-skin="switch" lay-text="是|否">
						</div>
		    </div>
	        <button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>
	   	</form>
	</div>
	<script>
        $('#startBtn').click(function() {
            var url=ctx + 'taskController/cron';
            var title="生成cron表达式";
            // if (top.layui.index) {
            //     top.layui.index.openTabsPage(url, title)
            // } else {
            //     window.open(url)
            // }
			layer.open({
				type:2,
				title: title,
				area: ['800px', '500px'],
				fixed: false, //不固定
				maxmin: true,
				content: url,
				btn: ['确定', '取消'],
				yes: function(index,layero){
					var submit = layero.find('iframe').contents().find("#submitBut");
					submit.click();
					return false;
				},
				btn2: function(){
					layer.closeAll();
				},
				zIndex: layer.zIndex, //重点1
				success: function(layero,index){
	             layer.setTop(layero); //重点2
//	        	 layer.iframeAuto(index);//弹窗自适应
				}
			});


        });
		layui.use(["form"],function(){
		var form = layui.form,cron = layui.cron;
		form.render();
		//监听提交
		form.on('submit(submitBut)', function(data){
		    console.log(data);
			var saveUrl = ctx + 'taskController/saveTask',
			tableName='task_table';
			var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
			$.ajax({
				method:"POST",
				url:saveUrl,       //提交表单的地址
				data:data.field,      //提交表单的数据
				success:function(res){
					if(res.code == 0){
						var index = parent.layer.getFrameIndex(window.name);
						parent.layer.close(index);
						parent.layer.msg(res.msg, { icon: 1});
						parent.layui.table.reload(tableName);
					}else{
						layer.msg(res.msg,{icon: 2});
					}
				},
				error:function(data){
					layer.close(loading);
					layer.msg('操作失败',{icon: 2});
				}
			});
			return false;
	  	     /*xadmin.submitForm(saveUrl,data,tableName);
	  	     return false; */
        });
        form.verify({
            uniqueName: function (value) {
            	if (top.parent.window.getBytes(value)>200){
            		return "不能超过200个字符";
				}
                var id = $("#id").val();
                var checkMsg = '';
                var url = ctx + "taskController/uniqueName?name=" + value + "&id=" + id;
                $.ajax({
                    url: url,
                    datatype: 'json',
                    async: false,
                    success: function (result) {
                        if (result) {
                            checkMsg += '任务名称重复';
                            return checkMsg;
                        }
                    }, error: function () {
                        layer.msg("任务名称验证失败");
                    }
                });
                if (checkMsg != '') {
                    return checkMsg;
                }
            },
            uniqueJob: function (value) {
				if (top.parent.window.getBytes(value)>200){
					return "不能超过200个字符";
				}
                //var orginJybh = [[${user==null?'-1':user?.jybh}]];
                var id = $("#id").val();
                var checkMsg = '';
                var url = ctx + "taskController/uniqueJob?job_name=" + value + "&id=" + id;
                $.ajax({
                    url: url,
                    datatype: 'json',
                    async: false,
                    success: function (result) {
                        if (result) {
                            checkMsg += '任务类重复';
                            return checkMsg;
                        }
                    }, error: function () {
                        layer.msg("任务类验证失败");
                    }
                });
                if (checkMsg != '') {
                    return checkMsg;
                }
            },
			cron: function (value) {
				if (top.parent.window.getBytes(value)>20){
					return "不能超过20个字符";
				}
            },
			describe: function (value) {
				if (top.parent.window.getBytes(value)>200){
					return "不能超过200个字符";
				}
            }
        });
	})
	function getCron(val){
		if(val != ''){
			$("#cron").val(val);
		}
	}
	
	</script>
</body>
</html>