<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	xmlns:http="http://www.w3.org/1999/xhtml"
	xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
<meta charset="utf-8">
<title>用户信息</title>
<div th:replace="Importfile::html"></div>
<script th:src="@{/plugins/formSelects/formSelects-v4.js}" charset="utf-8"></script>
<link rel="stylesheet"
	th:href="@{/plugins/formSelects/formSelects-v4.css}" />
<style>
.layui-form-item .layui-input-inline{
	width : 260px;
}
</style>
</head>
<script th:inline="javascript">
	$(function () {

		var yhyxqsStr = [[${yhyxqs}]];
		var yhyxqStr = [[${yhyxq}]];
		var mmyxqsStr = [[${mmyxqs}]];
		var mmyxqStr = [[${mmyxq}]];

		if (yhyxqsStr == null || yhyxqsStr == '') {
			// 用户有效期始默认当天
			var date = new Date();
			$("#yhyxqs").val(formatDate(date));
		}

		if (yhyxqStr == null || yhyxqStr == '') {
			// 用户有效期止默认30天
			var date = new Date();
			date.setDate(date.getDate() + 30);
			$("#yhyxq").val(formatDate(date));
		}

		if (mmyxqsStr == null || mmyxqsStr == '') {
			// 密码有效期始默认当天
			var date = new Date();
			$("#mmyxqs").val(formatDate(date));
		}

		if (mmyxqStr == null || mmyxqStr == '') {
			// 密码有效期止默认15天
			var date = new Date();
			date.setDate(date.getDate() + 15);
			$("#mmyxq").val(formatDate(date));
		}


	})

	function formatDate(date) {
		var y = date.getFullYear();
		var m = date.getMonth() + 1;
		m = m < 10 ? '0' + m : m;
		var d = date.getDate();
		d = d < 10 ? ('0' + d) : d;
		return y + '-' + m + '-' + d;
	};
</script>
<body>
	<div class="layui-fluid">
		<div class="layui-card">
			<div class="layui-card-body">
				<fieldset class="layui-elem-field layui-field-title"
					style="margin-top: 20px;">
					<legend>用户信息</legend>
				</fieldset>

				<form class="layui-form" action="">
				    <input type="hidden" name="id" id="id" th:value=${user?.id}>
				    <div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>登录账号：</label>
							<div class="layui-input-inline">
      							<input type="text" name="userName" lay-verify="required|unique" th:if="${user==null}"
      							 placeholder="请输入用户名" autocomplete="off" th:value="${user?.userName}" class="layui-input">
      							 <input type="text" name="userName" lay-verify="required|unique" th:if="${user!=null}"
      							 placeholder="请输入用户名" autocomplete="off" th:value="${user?.userName}" class="layui-input" readonly >
   							 </div>
						</div>
						<div class="layui-inline" th:if="${user==null}">
									<label class="layui-form-label"><span style="color:red">*</span>密码：</label>
									<div class="layui-input-inline">
										<input type="password" name="password" lay-verify="required|passwordLength"
										 class="layui-input">
									</div>
						 </div>
					</div>
					
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>用户姓名：</label>
							<div class="layui-input-inline">
      							<input type="text" name="fullName" lay-verify="required|fullnameLength"
      							 placeholder="请输入用户全称" autocomplete="off" th:value="${user?.fullName}" class="layui-input">
   							 </div>
						</div>
						
						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>身份证号：</label>
							<div class="layui-input-inline">
      							<input type="text" name="idCardNumber" lay-verify="required|identity|uniqueCard"
      							 placeholder="请输入身份证号" autocomplete="off" th:value="${user?.idCardNumber}" class="layui-input">
   							 </div>
						</div>
					</div>					
					
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>员工编号：</label>
							<div class="layui-input-inline">
								<input type="text" name="jybh" lay-verify="required|jybh"
									   placeholder="请输入员工编号" autocomplete="off" th:value="${user?.jybh}" class="layui-input">
							</div>
						</div>

					  	<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>角色权限：</label>
							<div class="layui-input-inline input-width">
					         <select name="roleIds" xm-select="role_select" xm-select-show-count="1" xm-select-search=""
				                lay-vertype="tips" lay-verify="required">
							  </select>
							  <input type="hidden"  th:value="${roleIds}" id="roleIds">
							</div>
						</div>
					   
						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>所属部门：</label>
							<div class="layui-input-inline">
	      							<select name="deptId" xm-select="deptId" xm-select-radio=""  xm-select-search=""
					                lay-vertype="tips" lay-verify="required">
								    </select>
							        <input type="hidden" id="deptId" th:value=${user?.deptId}> 
    					    </div>
						</div>
					</div>

					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>用户有效期始：</label>
							<div class="layui-input-inline">
								<div class="layui-input-inline">
									<input type="text" name="yhyxqsStr" id="yhyxqs" lay-verify="date"
										   autocomplete="off" th:value="${#dates.format(user?.yhyxqs, 'yyyy-MM-dd')}"
										   class="layui-input">
								</div>
							</div>
						</div>

						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>用户有效期止：</label>
							<div class="layui-input-inline">
								<div class="layui-input-inline">
									<input type="text" name="yhyxqStr" id="yhyxq" lay-verify="date"
										   autocomplete="off" th:value="${#dates.format(user?.yhyxq, 'yyyy-MM-dd')}"
										   class="layui-input">
								</div>
							</div>
						</div>
					</div>

					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><span style="color:red">*</span>联系电话：</label>
							<div class="layui-input-inline">
        						<input type="tel" name="phone" lay-verify="required|phone" placeholder="请输入电话号码"
        						  th:value="${user?.phone}" class="layui-input">
      						</div>
						</div>
					</div>
			
					   <div class="layui-form-item">
						   <div class="layui-inline" th:if="${session.user != null &&session.user.isSys}">
							     <label class="layui-form-label">是否系统级</label>
							        <div class="layui-input-inline">
							                <input type="checkbox" value="true" th:attr="checked=${user?.isSys == true ? true : false}" name="isSys" id="isSys" lay-skin="switch" lay-text="是|否">
							          </div>
						    </div>
						     <div class="layui-inline">
						         <label class="layui-form-label">是否启用</label>
						          <div class="layui-input-inline">
						                <input type="checkbox" value="true" th:attr="checked=${user?.isStart== true ? true : false}" name="isStart" id="isStart" lay-skin="switch" lay-text="启用|停用">    
						          </div>
					         </div>
				        </div>
				        
					<div class="layui-form-item">
						<div class="layui-input-block">
							<button class="layui-btn" lay-submit="" lay-filter="demo1">保存</button>
							<button type="reset" class="layui-btn layui-btn-primary">重置</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	<script>
	var formSelects = layui.formSelects;
	layui.use([ 'form', 'layedit', 'laydate', 'jquery'  ],
			function() {
				var form = layui.form,
				    layer = layui.layer,
				    layedit = layui.layedit,
				    laydate = layui.laydate,
				    $ = jQuery = layui.$;
				formSelects.data('deptId','server',{
				        data: {"deptId": $("#deptId").val()}, 
				     	keyVal:'ID',
				    	keyName:'deptName',
				    	direction: 'auto',
				        url : ctx + 'deptController/tree',
				        beforeSuccess: function(id, url, searchVal, result){
				        	result = result.data;
							return result;
						}
				    }).on('deptId', function(id, vals, val, isAdd, isDisabled){
			    	    $("#deptId").val(val.ID);
				    }, true);
	
				   formSelects.data('role_select','server',{
				    	data: {"roleIds": $("#roleIds").val()},
				     	keyVal:'id',
				    	keyName:'roleName',
				    	direction: 'auto',
				        url : ctx + 'roleController/roleDialogJsonX',
				        beforeSuccess: function(id, url, searchVal, result){
				        	result = result.data;

							return result;
						}
				    });
			form.render();
		    form.verify({	 
				     uniqueCard:function(value){
				    	 var id = $("#id").val();
  	              		 var checkMsg = '';
  	              		 var url = ctx +  "userController/uniqueIdCard?idCardNumber="+value+ "&id="+id;
  	              		 $.ajax({
  	    					 url : url,
  	    					 datatype : 'json',
  	    					 async: false,
  	    					 success : function(result) {
  	    						 if (result) {
  	    							 checkMsg += '身份证重复';
  	    							 return checkMsg;
  	    						 }
  		    				    },error : function() {
  		    						layer.msg("身份证验证失败");
  		    					}
  	    				});
  	              		 if(checkMsg != ''){
  	              	        return checkMsg;
  	              		 }	
				     },
                	 unique:function(value,item){
                		 var id = $("#id").val();
  	              		 var checkMsg = '';
  	              		 var url = ctx +  "userController/uniqueData?userName="+value+ "&id="+id;
  	              		 $.ajax({
  	    					 url : url,
  	    					 datatype : 'json',
  	    					 async: false,
  	    					 success : function(result) {
  	    						 if (result) {
  	    							 checkMsg += '登录账户重复';
  	    							 return checkMsg;
  	    						 }
  		    				    },error : function() {
  		    						layer.msg("登录账户验证失败");
  		    					}
  	    				});
  	              		 if(checkMsg != ''){
  	              	        return checkMsg;
  	              		 }
              			if (value.length < 3) {
  	                      	return "登录账户长度3到32个字符";
  	                  	}
  	                  	if (value.length > 32) {
  	                      	return "登录账户长度3到32个字符";
  	                  	}
                	 },
                	 passwordLength:function(value){
               		 	if (value.length < 8) {
   	                      	return "密码长度8到30个字符";
   	                  	}
   	                  	if (value.length > 30) {
   	                      	return "密码长度8到30个字符";
   	                  	}
				     },
				     fullnameLength:function(value){
			    	 	if (value.length < 2) {
   	                      	return "用户姓名长度2到20个字符";
   	                  	}
   	                  	if (value.length > 20) {
   	                      	return "用户姓名长度2到20个字符";
   	                  	}
				     }
                 });
				form.on('submit(demo1)', function(data) {
					var loading = layer.msg('正在保存', {icon: 16, shade: 0.3, time:0});
					$.ajax({
						type : "POST",
						url : "saveUser",
						data : data.field,
						dataType : "json",
						success : function(data) {
							if (data.code == '0') {
								layer.alert(data.msg, {
									icon : 6, skin: 'layer-ext-moon',closeBtn: 0},
									function(){
										window.parent.changTabs( ctx +'userController/userList','','用户列表');
									});
							} else {
								 layer.msg(data.msg, {icon : 5,skin : 'layer-ext-moon'}); 
							}
							layer.close(loading);
						},
						error : function(data) {
						   layer.close(loading);
						   layer.msg(data.msg, {icon : 5,skin : 'layer-ext-moon'});
						}
					});
					return false; //防止提交两次表单
				});
	});
    
	
</script>
</body>
</html>