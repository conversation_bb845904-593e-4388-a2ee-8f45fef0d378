<?xml version="1.0" encoding="UTF-8" ?>   
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.DataServerDao">

    <select id="findNowProcesses" resultType="java.lang.Integer" databaseId="mysql">
      show full processlist
    </select>




    <select id="findAllProcesses" resultType="java.util.HashMap" databaseId="mysql">
       show variables like concat('%',#{max_connections},'%');
    </select>

    <select id="findCacheHitRate" resultType="double">
        select 1-(sum(decode(name,'physical reads',value,0))/(sum(decode(name,'db block gets',value,0))+(sum(decode(name,'consistent gets',value,0)))))
                "Biffer <PERSON>" from v$sysstat
    </select>

    <select id="findSharedPoolHitRate" resultType="double">
        select sum(pinhits-reloads)/sum(pins) "hit radio" from v$librarycache
    </select>

    <select id="findLogOccupancy" resultType="double">
        select a.value/b.value
        from (select value from  v$sysstat where name = 'redo log space requests') a ,
             (select value from  v$sysstat where name = 'redo entries') b
    </select>

    <select id="findLockNumber" resultType="int">
        select count(*) from v$locked_object
    </select>


    <select id="findAllTableSpaceData" resultType="com.fwy.corebasic.entity.serverData.TableSpace">
        SELECT a.tablespace_name tableSpaceName,
               a.bytes total,
               b.bytes used,
               c.bytes free
        FROM sys.sm$ts_avail a, sys.sm$ts_used b, sys.sm$ts_free c
        WHERE a.tablespace_name = b.tablespace_name
          AND a.tablespace_name = c.tablespace_name
    </select>


</mapper>