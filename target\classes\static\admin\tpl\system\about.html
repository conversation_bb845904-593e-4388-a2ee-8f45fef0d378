<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<script>
    (function($) {
        $.ajax({
            url:'configController/sysBaseInfoJson',       //提交表单的地址//提交表单的数据
            success:function(data){
                if (data.stateType == '0') {
                        $('#version').html("当前版本:"+data.stateValue.version);
                        $('#copyrightInfo').append(data.stateValue.copyrightInfo);
                        $('#aboutVersion').html("当前版本:"+data.stateValue.aboutVersion);
                } else {
                    layer.msg(data.stateMsg, {
                        icon : 5,
                        skin : 'layer-ext-moon'
                    });
                }
            },
            error:function(){
            }
        });
        return false;
    })(jQuery);
</script>
<div class="layui-card-header">版本信息</div>
<div class="layui-card-body layui-text layadmin-about">
  <script type="text/html" template>
    <p id="version"></p>
  </script>
  <!-- <div class="layui-btn-container">
    <a href="" target="_blank" class="layui-btn layui-btn-danger">公安用户APP下载</a>
    <a href="" target="_blank" class="layui-btn">食药监局用户APP下载</a>
    <a href="" target="_blank" class="layui-btn  layui-btn-warm">美沙酮维持治疗治疗用户APP下载</a>
  </div> -->
</div>

<div class="layui-card-header">关于版权</div>
<div class="layui-card-body layui-text layadmin-about">
<blockquote class="layui-elem-quote" style="border: none;" >
    <p id="aboutVersion"></p>
   </a>
  </blockquote>
  <p id="copyrightInfo"></p>
</div>
</html>
