<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>流程项管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script type="text/javascript" th:src="@{/scripts/dic/operationflow/list.js}"></script>
    <script>
        var loading = layer.load(1, {
            shade: [0.1,'#fff'] //0.1透明度的白色背景
        });
    </script>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" action="">
                <div class="layui-form-item" style="background-color:#eee;padding: 10px; margin-bottom: 0px;border-left: 5px solid #009688;">
                    <div class="layui-inline">
                        <label class="layui-form-label">流程项标题</label>
                        <div class="layui-input-inline">
                            <input id="title" class="layui-input" type="text" placeholder="请输入流程项标题">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-block">
                            <button id="searchBtn" lay-event="search_btn" class="layui-btn">
                                <i class="layui-icon">&#xe615;</i>查询</button>
                            <button type="reset" class="layui-btn layui-btn-primary" id="unsetBtn">
                                <i class="layui-icon">&#xe669;</i>重置</button>
                        </div>
                    </div>
                </div>
            </form>
            <table class="layui-hide" id="operationflow-table" lay-filter="operationflow-table"></table>
            <script type="text/html" id="table-toolbar-top">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe608;</i>增加</button>
                    <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="batchDel"><i class="layui-icon">&#xe640;</i>删除</button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="export" id="export"><i class="layui-icon">&#xe601;</i>导出</button>
                </div>
            </script>

            <script type="text/html" id="zzsbtoolbar">
        </script>
            <script type="text/html" id="table-toolbar">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="oneDel">删除</a>
            </script>
        </div>
    </div>
</div>

</body>
</html>