<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.dic.dao.AdministrativeAreaDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fwy.dic.entity.AdministrativeArea">
        <result column="ID" property="id"/>
        <result column="PARENTID" property="parentId"/>
        <result column="ORDERCODE" property="orderCode"/>
        <result column="DM" property="dm"/>
        <result column="MC" property="mc"/>
        <result column="PYZT" property="pyzt"/>
        <result column="BZ" property="bz"/>
        <result column="BDRQ" property="bdrq"/>
        <result column="JC" property="jc"/>
        <result column="ISSYS" property="isSys"/>
        <result column="SFYX" property="sfyx"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumn">
        ID, PARENTID, ORDERCODE, DM, MC, PYZT, BZ,BDRQ,JC,ISSYS,SFYX
    </sql>

    <update id="updateOrderCode">
        update core_xzqhb cd set
            cd.ordercode =  ( concat(
                    SUBSTR(cd.ORDERCODE from 1 FOR (LOCATE(#{oldCode},cd.ORDERCODE)-1) ),
                    #{newCode},
                    SUBSTR(cd.ORDERCODE from (LOCATE(#{oldCode},cd.ORDERCODE)+LENGTH(#{oldCode})))
                ))
        where LOCATE(concat(',',cd.ID,','),concat(',',getChildFromXzqh(#{id})))>0
    </update>

    <resultMap type="com.fwy.dic.entity.AdministrativeArea" id="childrenMap">
        <result column="ID" property="id"/>
        <result column="PARENTID" property="parentId"/>
        <result column="ORDERCODE" property="orderCode"/>
        <result column="DM" property="dm"/>
        <result column="MC" property="mc"/>
        <result column="PYZT" property="pyzt"/>
        <result column="BZ" property="bz"/>
        <result column="BDRQ" property="bdrq"/>
        <result column="JC" property="jc"/>
        <result column="ISSYS" property="isSys"/>
        <result column="SFYX" property="sfyx"/>
        <collection column="ID" property="children"
                    ofType="com.fwy.dic.entity.AdministrativeArea"
                    select="com.fwy.dic.dao.AdministrativeAreaDao.getChildrenList"/>
    </resultMap>
    <select id="getTree" resultMap="BaseResultMap">
        select
        ID,MC,PARENTID
        from CORE_XZQHB
        where SFYX = 1
        order by ORDERCODE
    </select>
    <select id="getCountByCode" resultType="java.lang.Integer">
        select count(ID) from CORE_XZQHB where DM = #{dm}
            <if test="id!=null">
                and ID !=#{ID}
            </if>
    </select>

    <select id="getInsertCode" resultType="java.lang.String">
        select max(ORDERCODE) from CORE_XZQHB where PARENTID = #{parentId}
    </select>

    <select id="selectOrderCodeById" resultType="java.lang.String">
        select ORDERCODE from CORE_XZQHB where ID = #{parentId}
    </select>

    <select id="getChildrenList" resultMap="childrenMap">
        select
        ID,MC
        from CORE_XZQHB
        where PARENTID = #{parentId} and SFYX = 1
        order by ORDERCODE
    </select>

    <select id="getChildrenByOrderCodeLength" resultMap="BaseResultMap">
        select ID,DICNAME,CODE,ORDERCODE from CORE_XZQHB
        where length(ORDERCODE) = #{orderCodeLength}
        <if test="parentId != null">
            and PARENTID = #{parentId}
        </if>
        and SFYX = 1
    </select>

    <select id="getAllChildrenId" resultType="String">
        select getChildFromXzqh(#{id})
    </select>


    <select id="myList" resultMap="BaseResultMap">
        SELECT ID,PARENTID,DM,MC,PYZT,BZ,BDRQ,JC,ISSYS,SFYX
        FROM CORE_XZQHB
        where PARENTID=#{parentId}
        ORDER BY DM ASC;
    </select>
</mapper>
