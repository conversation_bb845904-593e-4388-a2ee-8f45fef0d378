<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.ISyspzxxDao">
	<resultMap type="com.fwy.corebasic.entity.Sys_Pzxx" id="pzxxMap">
		<result property="bh" column="BH" />
		<result property="ip_fail_count" column="ip_fail_count" />
		<result property="mm_fail_count" column="mm_fail_count" />
		<result property="drsj" column="DRSJ" />
		<result property="cqwdrsj" column="CQWDRSJ" />
		<result property="fwsj" column="FWSJ" />
		<result property="fwcs" column="FWCS" />
		<result property="status" column="STATUS" />
		<result property="isIpLock" column="IS_IP_LOCK" />
		<result property="isMmErr" column="IS_MM_ERR" />
		<result property="isCqwdlPz" column="IS_CQWDL_PZ" />
		<result property="isGpfwPz" column="IS_GPFW_PZ" />
		<result property="isTime" column="IS_TIME" />
	</resultMap>

	<select id="getSysPzxx" resultMap="pzxxMap">
		select * from sys_pzxx
	</select>
	<update id="update" parameterType="com.fwy.corebasic.entity.Sys_Pzxx">
		update sys_pzxx set
		IP_FAIL_COUNT=#{ip_fail_count,jdbcType=VARCHAR},MM_FAIL_COUNT=#{mm_fail_count,jdbcType=VARCHAR},
		DRSJ=#{drsj,jdbcType=VARCHAR},CQWDRSJ=#{cqwdrsj,jdbcType=VARCHAR},FWCS=#{fwcs,jdbcType=VARCHAR},
		FWSJ=#{fwsj,jdbcType=VARCHAR},BLACK_IP_TIME=#{black_ip_time,jdbcType=INTEGER},STATUS=#{status},
		 IS_IP_LOCK=#{isIpLock},IS_MM_ERR=#{isMmErr},IS_CQWDL_PZ = #{isCqwdlPz}, IS_GPFW_PZ=#{isGpfwPz},IS_TIME=#{isTime} where BH=#{bh}
	</update>

	<insert id="addpzxx" parameterType="com.fwy.corebasic.entity.Sys_Pzxx">
		insert into sys_pzxx
		(
		<if test="bh !=null and bh != ''">
			BH,
		</if>
		IP_FAIL_COUNT,MM_FAIL_COUNT,DRSJ,CQWDRSJ,FWSJ,FWCS)
		values(
		<if test="bh !=null and bh != ''">
			#{bh,jdbcType=VARCHAR},
		</if>
		#{ip_fail_count,jdbcType=VARCHAR},#{mm_fail_count,jdbcType=VARCHAR},#{drsj,jdbcType=VARCHAR},
		#{cqwdrsj,jdbcType=VARCHAR},#{fwsj,jdbcType=VARCHAR},#{fwcs,jdbcType=VARCHAR}),#{status}
	</insert>

	<select id="judgeCount" resultType="Integer">
		select count(*) from sys_pzxx
	</select>

	<select id="getPzxxBybh" resultMap="pzxxMap">
		select t.*  from sys_pzxx t where
		t.BH=#{bh}
	</select>

	<update id="isStatus">
		update
			sys_pzxx t
		set
			t.STATUS = #{status}
		where
			t.BH = #{bh}
	</update>

</mapper>