<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>
        .layui-container {
            padding-top: 20px;
            padding-left: 40px
        }

        .search-bar {
            background-color: #DEEBE3;
            padding: 10px;
        }

        .card-container {
            background-color: #DEEBE3;
            padding: 10px;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .layui-card {
            width: calc(16.66% - 15px);
            margin-right: 10px;
            margin-bottom: 20px;
            height: 140px;
        }

        .card-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
        }

        .image-wrapper {
            height: 90px;
        }

        .card-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .text-wrapper {
            text-align: center;
        }

        .card-label {
            margin: 0;

            font-size: 12px;
            line-height: 1.5;
        }

        .card-text {
            margin: 0;
            font-size: 12px;
            line-height: 1.5;
        }

        .red {
            color: red;
        }

        .green {
            color: green;
        }

        .orange {
            color: orange;
        }
        .red-block {
            background-color: red;
        }
        .orange-block {
            background-color: orange;
        }

        .left-column {
            width: 90px;
            float: left
        }

        .right-column {
            width: 70px;
            float: right;
        }

        .color-block {
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-right: 5px;
        }

        .right-corner-comment {
            position: absolute;
            top: 0;
            right: 0;
            margin-top: 10px;
            margin-right: 10px;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>

<body>
<div class="layui-container">
    <div class="layui-row">
        <div class="layui-col-md12">
            <form class="layui-form search-bar" lay-filter="searchForm">
                <blockquote class="layui-elem-quote quoteBox" id="search">
                    <div class="layui-inline">
                        <input type="text" name="startDate" id="startDate" placeholder="开始日期" autocomplete="off"
                               class="layui-input">
                    </div>
                    <div class="layui-inline">
                        <input type="text" name="endDate" id="endDate" placeholder="结束日期" autocomplete="off"
                               class="layui-input">
                    </div>
                    <div class="layui-inline">
                        <input type="number" name="minCount" id="minCount" placeholder="最低频次" autocomplete="off"
                               class="layui-input" style="width:100px">
                    </div>
                    <div class="layui-inline">
                        <input type="number" name="maxCount" id="maxCount" placeholder="最高频次" autocomplete="off"
                               class="layui-input" style="width:100px">
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                        <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                        <button class="layui-btn" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>

                    </div>
                    <div class="right-corner-comment">
                        <span class="layui-form-mid layui-word-aux" style="margin-left: 5px;">
                            <span class="color-block red-block"></span>：黑名单
                        </span>
                        <span class="layui-form-mid layui-word-aux" style="margin-left: 5px;">
                            <span class="color-block orange-block"></span>：疑似人员
                        </span>
                    </div>
                </blockquote>
            </form>
        </div>
    </div>

    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="card-container"></div>
        </div>
    </div>

    <div class="layui-row">
        <div class="layui-col-md12">
            <div id="pagination" class=".pagination"></div>
        </div>
    </div>
</div>

</body>
<script>
    layui.use(['form', 'laydate', 'laypage'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var laypage = layui.laypage;
        var page = 1;
        var limit = 24;
        var startDate;
        var endDate;

        var condition = {
            startDate: startDate,
            endDate: endDate,
            minCount: "",
            maxCount: "",
            currentPage: page,
            pageSize: limit
        };

        $("#minCount").on("input", function() {
            condition.minCount = $(this).val();
        });

        $("#maxCount").on("input", function() {
            condition.maxCount = $(this).val();
        });

        // 渲染日期选择器
        laydate.render({
            elem: '#startDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            done: function (value, date) {
                condition.startDate = value;
            }
        });

        laydate.render({
            elem: '#endDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            done: function (value, date) {
                condition.endDate = value;
            }
        });

        // 监听表单提交事件
        form.on('submit(searchForm)', function (data) {
            findPortrait(condition);
            return false;
        });

        // 初始化分页组件
        var paginationElem = $('#pagination');
        laypage.render({
            elem: paginationElem[0],
            layout: ['prev', 'page', 'next', 'skip', 'count'],
            jump: function (obj, first) {
                if (!first) {
                    condition.currentPage = obj.curr;
                    findPortrait(condition);
                }
            }
        });

        // 页面加载完成后执行 findPortrait 方法，默认加载第一页数据
        $(document).ready(function () {
            findPortrait(condition);
        });

        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            $("#startDate").val("");
            $("#endDate").val("");

            findPortrait(condition)
        })

        function findPortrait(condition) {
            $.ajax({
                url: ctx + '/portraitCollectionImageController/findRecordByCondition',
                type: 'POST',
                contentType: 'application/json', // 设置contentType为json
                data: JSON.stringify(condition),
                success: function (res) {
                    if (res.code === 200) {
                        var pageInfo = res.data.pageInfo;
                        var portraitList = pageInfo.list;
                        var total = pageInfo.total;
                        var pageCount = Math.ceil(total / limit);
                        laypage.render({
                            elem: 'pagination',
                            count: total,
                            curr: page,
                            limit: limit,
                            layout: ['prev', 'page', 'next', 'skip', 'count'],
                            jump: function (obj, first) {
                                if (!first) {
                                    page = obj.curr;
                                    condition.currentPage = page;
                                    findPortrait(condition);
                                }
                            }
                        });

                        var cardContainer = $('.card-container');
                        cardContainer.empty();
                        var colorClass = "green";
                        portraitList.forEach(function (item) {
                            var id = item.code
                            var date = new Date(item.updateTime);
                            var formattedDate = date.toLocaleDateString('zh-CN').substring(0, 10);
                            switch (item.type) {
                                case 1:
                                    colorClass = "orange";
                                    break;
                                case 2:
                                    colorClass = "red";
                                    break;
                                default:
                                    colorClass = "green";
                            }


                            var cardHtml = '<div class="layui-card" id="' + id + '">' +
                                '<div class="card-content">' +
                                '<div class="image-wrapper">' +
                                '<img src="' + imageBase + item.ossUrl + '" alt="Image" class="card-image" onerror="replaceWithErrorImage(this)">' +
                                '</div>' +
                                '<div class="text-wrapper ' + colorClass + '">' +
                                '<div class="left-column">' +
                                '<p class="card-label">频次：</p>' +
                                '<p class="card-label">最近记录时间：</p>' +
                                '</div>' +
                                '<div class="right-column">' +
                                '<p class="card-text">' + item.count + '</p>' +
                                '<p class="card-text">' + formattedDate + '</p>' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';


                            // 给卡片添加点击事件
                            $('body').on('click', "#" + id, function () {
                                showRecord(id);
                            });
                            cardContainer.append(cardHtml);
                        });
                    } else {
                        console.error('Invalid response format:', res);
                    }
                },
                error: function (error) {
                    console.error(error);
                }
            });
        }
    });

    function showRecord(id) {
        var url = ctx + 'portraitCollectionRecordController/toRecordPage/' + id,
            title = '人像采集记录';
        top.layui.index.openTabsPage(url, title);
    }

</script>


</html>
