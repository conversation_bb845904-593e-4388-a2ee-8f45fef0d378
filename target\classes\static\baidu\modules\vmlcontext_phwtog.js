_jsload2&&_jsload2('vmlcontext', 'var hg=10,ig=hg/2; function jg(a){this.li=[[1,0,0],[0,1,0],[0,0,1]];this.cN=[];this.TJ=[];this.qk=[];this.lineWidth=1;this.miterLimit=1*hg;this.canvas=a;var b=L("div");b.style.width=a.clientWidth+"px";b.style.height=a.clientHeight+"px";b.style.position="absolute";a.appendChild(b);this.fL=b;this.TM=this.Iw=this.Hw=1;document.styleSheets.canvas_style||(a=document.createStyleSheet(),a.owningElement.id="canvas_style",a.cssText="canvas{display:inline-block;overflow:hidden;text-align:left;width:300px;height:150px}g_vml_\\\\:shape,g_vml_\\\\:fill,g_vml_\\\\:stroke{behavior:url(#default#VML)}")} function kg(a,b){for(var c=[[1,0,0],[0,1,0],[0,0,1]],e=0;3>e;e++)for(var f=0;3>f;f++){for(var g=0,i=0;3>i;i++)g+=a[e][i]*b[i][f];c[e][f]=g}return c}var lg=jg.prototype;lg.clearRect=function(){this.fL.innerHTML=""};lg.beginPath=function(){this.qk=[]};lg.moveTo=function(a,b){var c=this.fi(a,b);this.qk.push({type:"moveTo",x:c.x,y:c.y});this.rs=c.x;this.ss=c.y};lg.lineTo=function(a,b){var c=this.fi(a,b);this.qk.push({type:"lineTo",x:c.x,y:c.y});this.rs=c.x;this.ss=c.y}; lg.bezierCurveTo=function(a,b,c,e,f,g){mg(this,this.fi(a,b),this.fi(c,e),this.fi(f,g))};function mg(a,b,c,e){a.qk.push({type:"bezierCurveTo",oW:b.x,pW:b.y,qW:c.x,rW:c.y,x:e.x,y:e.y});a.rs=e.x;a.ss=e.y}lg.quadraticCurveTo=function(a,b,c,e){a=this.fi(a,b);c=this.fi(c,e);e={x:this.rs+2/3*(a.x-this.rs),y:this.ss+2/3*(a.y-this.ss)};mg(this,e,{x:e.x+(c.x-this.rs)/3,y:e.y+(c.y-this.ss)/3},c)}; lg.arc=function(a,b,c,e,f,g){var c=c*hg,i=a+Math.cos(e)*c-ig,k=b+Math.sin(e)*c-ig,e=a+Math.cos(f)*c-ig,f=b+Math.sin(f)*c-ig;i==e&&!g&&(i+=0.125);a=this.fi(a,b);i=this.fi(i,k);e=this.fi(e,f);this.qk.push({type:g?"at":"wa",x:a.x,y:a.y,wa:c,X0:i.x,Z0:i.y,W0:e.x,Y0:e.y})};lg.rect=function(a,b,c,e){this.moveTo(a,b);this.lineTo(a+c,b);this.lineTo(a+c,b+e);this.lineTo(a,b+e);this.closePath()}; lg.stroke=function(a){var b=[],c=a?this.fillStyle:this.strokeStyle,e=c.color,c=c.alpha;b.push("<g_vml_:shape",\' filled="\',!!a,\'"\',\' style="position:absolute;width:\',10,"px;height:",10,\'px;"\',\' coordorigin="0 0" coordsize="\',10*hg," ",10*hg,\'"\',\' stroked="\',!a,\'"\',\' path="\');for(var f=s,g=s,i=s,k=s,m=0;m<this.qk.length;m++){var n=this.qk[m];switch(n.type){case "moveTo":b.push(" m ",Math.round(n.x),",",Math.round(n.y));break;case "lineTo":b.push(" l ",Math.round(n.x),",",Math.round(n.y));break;case "close":b.push(" x "); n=s;break;case "bezierCurveTo":b.push(" c ",Math.round(n.oW),",",Math.round(n.pW),",",Math.round(n.qW),",",Math.round(n.rW),",",Math.round(n.x),",",Math.round(n.y));break;case "at":case "wa":b.push(" ",n.type," ",Math.round(n.x-this.Hw*n.wa),",",Math.round(n.y-this.Iw*n.wa)," ",Math.round(n.x+this.Hw*n.wa),",",Math.round(n.y+this.Iw*n.wa)," ",Math.round(n.X0),",",Math.round(n.Z0)," ",Math.round(n.W0),",",Math.round(n.Y0))}if(n){if(f==s||n.x<f)f=n.x;if(i==s||n.x>i)i=n.x;if(g==s||n.y<g)g=n.y;if(k== s||n.y>k)k=n.y}}b.push(\' ">\');a?b.push(\'<g_vml_:fill color="\',e,\'" opacity="\',c,\'" />\'):(a=this.TM*this.lineWidth,1>a&&(c*=a),b.push("<g_vml_:stroke",\' opacity="\',c,\'"\',\' joinstyle="\',this.lineJoin,\'"\',\' miterlimit="\',this.miterLimit,\'"\',\' endcap="\',"square",\'"\',\' weight="\',a,\'px"\',\' color="\',e,\'" />\'));b.push("</g_vml_:shape>");this.fL.insertAdjacentHTML("beforeEnd",b.join(""))};lg.fill=function(){this.stroke(q)};lg.closePath=function(){this.qk.push({type:"close"})}; lg.fi=function(a,b){var c=this.li;return{x:hg*(a*c[0][0]+b*c[1][0]+c[2][0])-ig,y:hg*(a*c[0][1]+b*c[1][1]+c[2][1])-ig}};lg.save=function(){var a={};copyState(this,a);this.TJ.push(a);this.cN.push(this.li);this.li=kg(createMatrixIdentity(),this.li)};lg.restore=function(){copyState(this.TJ.pop(),this);this.li=this.cN.pop()}; function ng(a,b,c){var e;a:{for(e=0;3>e;e++)for(var f=0;2>f;f++)if(!isFinite(b[e][f])||isNaN(b[e][f])){e=t;break a}e=q}e&&(a.li=b,c&&(a.TM=Math.sqrt(Math.abs(b[0][0]*b[1][1]-b[0][1]*b[1][0]))))}lg.translate=function(a,b){ng(this,kg([[1,0,0],[0,1,0],[a,b,1]],this.li),t)};lg.rotate=function(a){var b=Math.cos(a),a=Math.sin(a);ng(this,kg([[b,a,0],[-a,b,0],[0,0,1]],this.li),t)};lg.scale=function(a,b){this.Hw*=a;this.Iw*=b;ng(this,kg([[a,0,0],[0,b,0],[0,0,1]],this.li),q)}; lg.transform=function(a,b,c,e,f,g){ng(this,kg([[a,b,0],[c,e,0],[f,g,1]],this.li),q)};lg.setTransform=function(a,b,c,e,f,g){ng(this,[[a,b,0],[c,e,0],[f,g,1]],q)};B.aQ=jg; ');