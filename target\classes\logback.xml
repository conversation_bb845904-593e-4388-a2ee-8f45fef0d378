<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="log.error" value="message-error.log"/>
    <property name="log.warn" value="message-warn.log"/>
    <property name="log.info" value="message-info.log"/>
    <property name="log.debug" value="message-debug.log"/>
    <property name="log.base" value="${user.dir}/logs"/>
    <property name="max.size" value="20MB"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder >
            <pattern>%date{%Y-%m-%d HH:mm:ss.SSS} %-5level[%thread]%logger{56}.%method:%L -%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="file.error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.base}/${log.error}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!--    这个过滤器过滤掉低于当前级别的日志信息-->
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.base}/${log.error}.%d{%Y-%m-%d}.log</fileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>

<!--        <encoder class="ch.qos.logback.classic.PatternLayout">-->
<!--            <Pattern>-->
<!--                %d{%Y-%m-%d HH:mm:ss} -%msg%n-->
<!--            </Pattern>-->
<!--        </encoder>-->
        <encoder>
            <pattern>%date{%Y-%m-%d HH:mm:ss.SSS} %-5level[%thread]%logger{56}.%method:%L -%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--    <appender name="file.warn" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        <file>${log.base}/${log.warn}</file>-->
    <!--        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
    <!--            <level>WARN</level>-->
    <!--        </filter>-->
    <!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
    <!--            <fileNamePattern>${log.base}/${log.warn}.%d{%Y-%m-%d}.log</fileNamePattern>-->
    <!--            <MaxHistory>30</MaxHistory>-->
    <!--        </rollingPolicy>-->

    <!--        <layout class="ch.qos.logback.classic.PatternLayout">-->
    <!--            <Pattern>-->
    <!--                %d{%Y-%m-%d HH:mm:ss} -%msg%n-->
    <!--            </Pattern>-->
    <!--        </layout>-->
    <!--        <encoder>-->
    <!--            <pattern>%date{%Y-%m-%d HH:mm:ss.SSS} %-5level[%thread]%logger{56}.%method:%L -%msg%n</pattern>-->
    <!--            <charset>UTF-8</charset>-->
    <!--        </encoder>-->
    <!--    </appender>-->


    <!--    <appender name="file.info" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        <file>${log.base}/${log.info}</file>-->
    <!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
    <!--            <level>INFO</level>-->

    <!--        </filter>-->
    <!--        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">-->
    <!--            <fileNamePattern>${log.base}/${log.info}.%i.log</fileNamePattern>-->
    <!--            <minIndex>1</minIndex>-->
    <!--            <maxIndex>50</maxIndex>-->
    <!--        </rollingPolicy>-->

    <!--        <triggeringPolicy-->
    <!--                class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
    <!--            <maxFileSize>${max.size}</maxFileSize>-->
    <!--        </triggeringPolicy>-->
    <!--        <encoder>-->
    <!--            <pattern>%date{%Y-%m-%d HH:mm:ss.SSS} %-5level[%thread]%logger{56}.%method:%L -%msg%n</pattern>-->
    <!--            <charset>UTF-8</charset>-->
    <!--        </encoder>-->
    <!--    </appender>-->

    <!--    <appender name="file.debug" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        <file>${log.base}/${log.debug}</file>-->
    <!--        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
    <!--            <level>DEBUG</level>-->
    <!--        </filter>-->
    <!--        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">-->
    <!--            <fileNamePattern>${log.base}/${log.debug}.%i.log</fileNamePattern>-->
    <!--            <minIndex>1</minIndex>-->
    <!--            <maxIndex>50</maxIndex>-->
    <!--        </rollingPolicy>-->

    <!--        <triggeringPolicy-->
    <!--                class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
    <!--            <maxFileSize>${max.size}</maxFileSize>-->
    <!--        </triggeringPolicy>-->
    <!--        <encoder>-->
    <!--            <pattern>%date{%Y-%m-%d HH:mm:ss.SSS} %-5level[%thread]%logger{56}.%method:%L -%msg%n</pattern>-->
    <!--            <charset>UTF-8</charset>-->
    <!--        </encoder>-->
    <!--    </appender>-->

<!--为不同的包单独设置日志级别
1.若是additivity设为false，则子Logger只会在自己的appender里输出，不会在root的logger的appender里输出
2.若是additivity设为true，则子Logger不止会在自己的appender里输出，还会在root的logger的appender里输出
下面这一段都是false  并且没有指定appender  相当于没有设置
-->
    <logger name="org.apache" additivity="false">
        <level value="INFO"/>
    </logger>
    <logger name="org.postgresql" additivity="false">
        <level value="INFO"/>
    </logger>
    <logger name="org.springframework.core" additivity="false">
        <level value="INFO"/>
    </logger>
    <logger name="org.apache" additivity="false">
        <level value="INFO"/>
    </logger>
    <logger name="sun" additivity="false">
        <level value="INFO"/>
    </logger>
    <logger name="com.netflix" additivity="false">
        <level value="INFO"/>
    </logger>


    <logger name="java.sql.Connection" level="ERROR"/>
<!--    这里druid的错误日志不进行记录 -->
    <logger name="druid.sql.Statement" level="ERROR" additivity="false">
        <appender-ref ref="stdout"/>
    </logger>
    <logger name="java.sql.PreparedStatement" level="ERROR"/>
    <logger name="org.apache.zookeeper" level="ERROR"/>


    <!--配置一个root，需要写多个文件在里面追加appender-ref即可-->
    <root level="DEBUG" >
        <appender-ref ref="file.error"/>
        <appender-ref ref="stdout"/>
    </root>

</configuration>