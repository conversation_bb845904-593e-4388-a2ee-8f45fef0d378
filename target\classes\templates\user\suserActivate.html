<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>用户管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <link rel="stylesheet" th:href="@{/plugins/formSelects/formSelects-v4.css}" media="all">
    <script th:src="@{/plugins/formSelects/formSelects-v4.js}"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
    <style>

        .input-width{
            width:200px;
        }


        .layui-form-label{
            width: 150px;
        }

    </style>
</head>
<body>

<div id="" class="layui-layer-content" style="overflow: visible;">
    <form id="contrastForm" lay-filter="contrastFormFilter" class="layui-form">
        <div class="layui-form-item">
            <input name="suser.id"  id="suserId" type="hidden" th:value="${suser?.id}">
            <input name="suser.userName"  id="userName" type="hidden" th:value="${suser?.userName}">
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>用户延长有效期至：</label>
            <div class="layui-input-inline input-width" >
                <input type="text" id="yhyxq" name="yhyxq" lay-verify="required|unique"
                     autocomplete="off" th:attr="value=${#dates.format(suser?.yhyxq,'yyyy-MM-dd ')}"class="layui-input"  >
            </div>

        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color:red;">*</span>密码延长有效期至：</label>
            <div class="layui-input-inline input-width" >
                <input type="text" id="mmyxq" name="mmyxq" lay-verify="required|unique"
                       autocomplete="off" th:attr="value=${#dates.format(suser?.mmyxq,'yyyy-MM-dd ')}" class="layui-input"  >
            </div>
        </div>


        <button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>
    </form>
</div>
<script>

    layui.use(['laydate','form'], function(){
        var form = layui.form;
        var laydate = layui.laydate;
        form.render();
        laydate.render({
            elem:'#yhyxq',
            trigger: 'click'
        });
        laydate.render({
            elem:'#mmyxq',
            trigger: 'click'
        });

        //监听提交
        form.on('submit(submitBut)', function(data){

            var id = $("input[name='contrast.id']").val();
            var url = ctx + 'schoolContrastController/doEditContrast';

            if(id == null || id=='' || id==undefined ){
                url = ctx + 'schoolContrastController/doAddContrast';
            }

            var status = $("input[name='contrast.isOpenAutoCheck']").val();
            data.field['contrast.isOpenAutoCheck'] = status;

            var data = data.field;
            var tableName='tree-table';
            submitForm(url,data,tableName);

            return false;
        });

        var formSelects = layui.formSelects;
        formSelects.data('schoolList', 'server', {
            url: ctx + 'schoolContrastController/getDriverShoolList',
            data: {"schoolCode": $("#localSchoolCode").val()},
            keyName: 'name',         //自定义返回数据中name的key, 默认 name
            keyVal: 'value',			//自定义返回数据中value的key, 默认 value
            keySel: 'selected',         //自定义返回数据中selected的key, 默认 selected
            keyDis: 'disabled',         //自定义返回数据中disabled的key, 默认 disabled
            keyChildren: 'children',    //联动多选自定义children
            response: {
                statusCode: 0,          //成功状态码
                statusName: 'code',     //code key
                msgName: 'msg',         //msg key
                dataName: 'data'        //data key
            }
        }).on('schoolList', function(id, vals, val, isAdd, isDisabled){
            //console.log(val);
            $("#externalSchoolCode").val(val.value);
        });

        formSelects.data('trafficSchoolCodeList', 'server', {
            url: ctx + 'schoolContrastController/getTrafficDriverSchoolList',
            data: {"jxdm": $("#trafficSchoolCode").val()},
            keyName: 'name',         //自定义返回数据中name的key, 默认 name
            keyVal: 'value',			//自定义返回数据中value的key, 默认 value
            keySel: 'selected',         //自定义返回数据中selected的key, 默认 selected
            keyDis: 'disabled',         //自定义返回数据中disabled的key, 默认 disabled
            keyChildren: 'children',    //联动多选自定义children
            response: {
                statusCode: 0,          //成功状态码
                statusName: 'code',     //code key
                msgName: 'msg',         //msg key
                dataName: 'data'        //data key
            },
        });

    });

    function cheackContrastCode(value){

        var id = $("input[name='contrast.id']").val();
        if(id == null || id=='' || id==undefined ){
            id = "";
        }

        var url = ctx +  "schoolContrastController/cheackContrastCode";
        var flag = false;
        $.ajax({
            url: url,
            type: 'POST',
            dataType: 'JSON',
            async: false,
            data:{machineCode:value,id:id},
            success: function(res) {
                if(res.data == 'true' || res.data == true){
                    flag = true;
                }
            }
        });
        return flag ;
    }
</script>
</body>
</html>