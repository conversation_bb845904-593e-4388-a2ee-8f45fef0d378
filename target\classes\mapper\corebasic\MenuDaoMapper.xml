<?xml version="1.0" encoding="UTF-8" ?>   
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.corebasic.dao.IMenuDao">
	<resultMap type="com.fwy.corebasic.entity.Core_Menu" id="menuMap">
		<id property="id" column="ID" javaType="Long" jdbcType="INTEGER" />
		<id property="parentId" column="PARENTID" javaType="Long" jdbcType="INTEGER" />
		<id property="orderCode" column="ORDERCODE" javaType="String"
			jdbcType="VARCHAR" />
		<id property="isSys" column="ISSYS" javaType="Boolean" jdbcType="INTEGER" />
		<id property="menuName" column="menuName" javaType="String"
			jdbcType="VARCHAR" />
		<id property="iconCssClass" column="ICONCSSCLASS" javaType="String"
			jdbcType="VARCHAR" />
		<id property="menuUrl" column="MENUURL" javaType="String" jdbcType="VARCHAR" />
		<id property="actionList" column="actionList" javaType="String"
			jdbcType="VARCHAR" />
		<id property="isShow" column="ISSHOW" javaType="Boolean" jdbcType="INTEGER" />
	</resultMap>

	<!-- 获取菜单列表 -->
	<select id="list" resultType="com.fwy.corebasic.entity.Core_Menu">
		select * from core_menu order by
		ORDERCODE
	</select>
	<!-- 系统级用户的菜单 -->
	<select id="sysList" resultType="com.fwy.corebasic.entity.Core_Menu">
		select * from core_menu where
		core_menu.ISSHOW=1 order by
		ORDERCODE
	</select>
	

	<!-- 根据用户查找菜单 -->
	<select id="getListByUserId" parameterType="Long"
		resultType="com.fwy.corebasic.entity.Core_Menu">
		select distinct
		ID,core_menu.* from core_menu where
		core_menu.ISSHOW=1
		and LOCATE(concat(',',ID,','),concat(',',getParentFromMenu(
					(select
						 GROUP_CONCAT(menu.ID) from
						 core_menu menu
												  ,(select aa.ID
															   aaid,aa.CONTRONAME,aa.ACTIONNAME,aa.DISPLAY from
														core_user u
															left join
														core_userforrole ur on u.ID=ur.USERID
															left join
														core_role r on
															ur.ROLEID=r.ID
															left join core_authforrole ar on r.ID =
																							 ar.ROLEID
															left
																join core_auth a on ar.AUTHID = a.ID
															left join
														core_actionforauth aa on
															a.ID = aa.AUTHID
													where u.ID=#{uid}) action
						where
						LOCATE(action.ACTIONNAME,menu.ACTIONLIST)>0
						and
						LOCATE(action.ACTIONNAME,menu.MENUURL)>0
						and
						LOCATE(action.CONTRONAME,menu.ACTIONLIST) >0)
		))) >0
		order by core_menu.ORDERCODE
	</select>

	<!-- 根据id获取菜单 -->
	<select id="getObjectById" parameterType="Long"
		resultType="com.fwy.corebasic.entity.Core_Menu">
		<!-- select * from core_menu where id = #{id} -->
		select a.*, b.MENUNAME as parentMenuName
			  from core_menu a
			 left join core_menu b
			    on a.PARENTID = b.ID
			 where a.ID = #{id}
	</select>

	<!-- 添加菜单 -->
	<insert id="save" parameterType="com.fwy.corebasic.entity.Core_Menu" useGeneratedKeys="true">
		insert into
		core_menu(ID,PARENTID,ORDERCODE,ISSYS,MENUNAME,ICONCSSCLASS,MENUURL,ACTIONLIST,ISSHOW)
		values(#{id},#{parentId},#{orderCode},#{isSys},#{menuName},#{iconCssClass},#{menuUrl},#{actionList},#{isShow})
	</insert>

	<!-- 修改菜单 -->
	<update id="update" parameterType="com.fwy.corebasic.entity.Core_Menu">
		update core_menu set
		<if test="parentId != null">PARENTID=#{parentId},</if>
		<if test="orderCode != null and orderCode != ''">ORDERCODE=#{orderCode},</if>
		ISSYS=#{isSys},
	    <if test="menuName != null and menuName != ''">MENUNAME=#{menuName},</if>
	    <if test="iconCssClass != null and iconCssClass != ''">ICONCSSCLASS=#{iconCssClass},</if>
		<if test="menuUrl != null and menuUrl != ''">MENUURL=#{menuUrl},</if>
	    <if test="actionList != null and actionList != ''">ACTIONLIST=#{actionList},</if>
		ISSHOW=#{isShow}
		where ID = #{id}
	</update>

	<!-- 删除菜单 -->
	<delete id="delete" parameterType="Long">
		delete from core_menu
		where ID
		= #{id}
	</delete>

	<select id="getInsertCode" resultType="String" parameterType="Long">
		select Max(ORDERCODE) from core_menu where PARENTID= #{parentId}
	</select>
<!-- 菜单显示状态 -->
	<update id="show">
		update core_menu set ISSHOW=#{isShow} where ID =#{id}
	</update>
	
	<select id="getParentIdById" resultType="Long">
	   select PARENTID from core_menu where ID = #{id}
	</select>

	<!--获取排列前一个菜单数据-->
	<select id="getUpOne" resultType="com.fwy.corebasic.entity.Core_Menu">
        select *
        from core_menu
        where ORDERCODE = (select max(ORDERCODE)
                           from core_menu
                           where PARENTID = (select PARENTID from core_menu where ID = #{id})
                             and ORDERCODE &lt; (select ORDERCODE from core_menu where ID = #{id}))
	</select>
	<!--&gt; 大于 &lt; 小于 获取排列后一个菜单数据 -->
	<select id="getDownOne" resultType="com.fwy.corebasic.entity.Core_Menu">
        select *
        from core_menu
        where ORDERCODE = (select min(ORDERCODE)
                           from core_menu
                           where PARENTID = (select PARENTID from core_menu where ID = #{id})
                             and ORDERCODE &gt; (select ORDERCODE from core_menu where ID = #{id}))
	</select>
	
	<update id="updateOrderCode">
        update core_menu cd
        set cd.ORDERCODE = (SELECT concat(
                                           SUBSTR(cd.ORDERCODE from 1 FOR (LOCATE(#{oldOrderCode}, cd.ORDERCODE) - 1)),
                                           #{newOrderCode},
                                           SUBSTR(cd.ORDERCODE from
                                                  (LOCATE(#{oldOrderCode}, cd.ORDERCODE) + LENGTH(#{oldOrderCode})))
                                           ))
        where cd.ID in (SELECT ID
                        FROM (
                                     SELECT t1.ID,
                                            IF(FIND_IN_SET(PARENTID, @pids) > 0, @pids := CONCAT(@pids, ',', ID),
                                               0) AS ischild
                                     FROM (SELECT ID, PARENTID
                                           FROM core_menu t
                                           ORDER BY ID, PARENTID
                                                  ) t1,
                                          (SELECT @pids := #{id}) t2
                                     ) t3
                        WHERE ischild != 0
                           OR ID = #{id})
	</update>

</mapper>