$(function () {
    layui.config({
        base: ctx + 'plugins/'
    }).extend({
        treetable: 'treetable-lay/treetable'
    });
    var jquery;
    var $ = layui.jquery;
    layui.use(['treetable', 'layer', 'code', 'form', 'jquery'], function () {
        var o = layui.$,
            form = layui.form,
            layer = layui.layer,
            table = layui.table,
            treetable = layui.treetable;

        var re = treetable.render({
            treeColIndex: 2,
            treeSpid: "0",
            treeIdName: 'id',
            treePidName: 'parentId',
            treeLinkage: false,
            elem: '#tree-table',
            defaultToolbar: [],
            url: ctx + 'businessController/businessJson',
            cols: [[
                {type: 'checkbox'},
                {field: 'id', title: 'id', hide: true},
                {
                    field: 'serName',
                    title: '业务名称',
                    width: 200
                },
                {
                    field: 'orderCode',
                    title: '分类标识',
                    align: 'center',
                    width: 200
                },
                {
                    title: '是否显示',
                    width: 100,
                    align: 'center',
                    templet: function (item) {
                        if (item.isShow == 1) {
                            return '<input type="checkbox" value=' + item.id + ' checked="checked" name="isShow" lay-skin="switch" lay-filter="switchTest" lay-text="有效|无效">';
                        } else {
                            return '<input type="checkbox" value=' + item.id + ' name="isShow" lay-skin="switch" lay-text="有效|无效" lay-filter="switchTest">';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    templet: function (item) {
                        return '<a class="layui-btn layui-btn-normal layui-btn-xs" onclick="editInfo(' + item.id + ')">编辑</a>' +
                            '<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delInfo(' + item.id + ')">删除</a>' +
                            '<a class="layui-btn layui-btn-xs" onclick="csmb(' + item.id + ')">参数模板</a>' +
                            '<a class="layui-btn layui-btn-warm layui-btn-xs" onclick="openApplyType(' + item.id + ')">管理申请类型</a>' +
                            '<a class="layui-btn layui-btn-warm layui-btn-xs" onclick="openApplicationParam(' + item.id + ')">管理预约参数</a>'+
                            '<a class="layui-btn layui-btn-normal layui-btn-xs" onclick="moveInfo('+item.id+',1,\''+item.orderCode+'\')">上移</a>' +
                             '<a class="layui-btn layui-btn-warm layui-btn-xs" onclick="moveInfo('+item.id+',2,\''+item.orderCode+'\')">下移</a>';
                       }
                }
            ]],
            done: function () {
                layer.closeAll('loading');
            }
        });
        form.render();

        form.on('switch(switchTest)', function(data){
            var loading = layer.msg('正在更新', {icon: 16, shade: 0.1, time:0});
            var id = data.value;
            var isShow =this.checked ? '1' : '0';
            $.ajax({
                url : ctx + '/businessController/show',
                type : "POST",
                async : true,
                cache : false,
                data : {
                    "id" : id,
                    "isShow" : isShow
                },
                success : function(result) {
                    if(result.code==0){
                        layer.closeAll('loading');
                        layer.msg("更新成功!",{icon: 6,skin: 'layer-ext-moon' });
                        location.reload();
                    }else{
                        layer.closeAll('loading');
                        layer.msg("更新失败!",{icon: 5,skin: 'layer-ext-moon' });
                    }
                },
                error : function(result) {
                    layer.closeAll('loading');
                    layer.msg("更新失败!",{icon: 5,skin: 'layer-ext-moon' });
                }
            });
        });

        window.editInfo = function editInfo(id) {
            layer.open({
                type: 2,
                title: "编辑业务字典",
                shadeClose: false,
                btn: ['保存', '关闭'],
                area: ['800px', '380px'],
                content: ctx + 'businessController/updateInfo?id=' + id,
                yes: function (index, layero) { //当前层索引、当前层DOM对象
                    //提交表单
                    var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                    submit.click();// 触发提交监听
                    return false;
                },
                offset: [
                    //0.1*($(window).height()-200)
                    100
                ]
            });
        };
        window.delInfo = function delInfo(id){
            layer.confirm('是否确定删除所选信息？', {
                icon: 3,
                btn: ['确定','取消'] //按钮
            }, function(){
                var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time:0});
                $.ajax({
                    url : ctx + 'businessController/deleteInfo',
                    type : "POST",
                    async : true,
                    cache : false,
                    data : {
                        "id" : id
                    },
                    success : function(data) {
                        if(data.code==0){
                            layer.closeAll('loading');
                            layer.msg("删除成功!",{icon: 6,skin: 'layer-ext-moon' });
                            location.reload();
                        }else{
                            layer.closeAll('loading');
                            layer.msg(data.msg,{icon: 5,skin: 'layer-ext-moon' });
                        }
                    },
                    error : function(data) {
                        layer.closeAll('loading');
                        layer.msg("删除失败!",{icon: 5,skin: 'layer-ext-moon' });
                    }
                });
            }, function(){

            });
        };
        window.moveInfo = function moveInfo(id,moveType,orderCode){
            var orderCode = orderCode + '';
            var loading = layer.msg('正在移动', {icon: 16, shade: 0.3, time:0});
            $.ajax({
                url : ctx + 'businessController/moveBusinessInfo',
                type : "POST",
                data : {'id':id,'moveType':moveType,'orderCode':orderCode},
                async : true,
                cache : false,
                success : function(res){
                    layer.close(loading);
                    if(res.code == 0){
                        layer.msg(res.msg,{icon: 1});
                        location.reload();
                    }else{
                        layer.msg(res.msg,{icon: 5});
                    }
                },
                error : function(res){
                    layer.close(loading);
                    layer.msg('操作失败',{icon: 2});
                }
            })
        };

        window.csmb = function csmb(id){
            var url= ctx + "businessController/addConfig?id=" + id;
            var tit="参数配置";
            newTab(url,tit);
        };

        // 获取选中值，返回值是一个数组（定义的primary_key参数集合）
        o('#deleteInfo').click(function () {
            var checkStatus = table.checkStatus('tree-table');
            var data = checkStatus.data;
            console.log(data);
            if (data.length !== 1) {
                layer.msg('只能选择一条数据', {
                    icon: 2
                });
                return false;
            } else  {
                layer.confirm('是否确定删除所选信息？', {
                    icon: 3,
                    btn: ['确定', '取消'] //按钮
                }, function () {
                    var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
                    $.ajax({
                        url: ctx + 'businessController/deleteInfo',
                        type: "POST",
                        async: true,
                        cache: false,
                        data: {
                            "id": data[0].id
                        },
                        success: function (data) {
                            if (data.code == 0) {
                                layer.closeAll('loading');
                                layer.msg("删除成功!", {icon: 6, skin: 'layer-ext-moon'});
                                location.reload();
                            } else {
                                layer.closeAll('loading');
                                layer.msg("删除失败!", {icon: 5, skin: 'layer-ext-moon'});
                            }
                        },
                        error: function (data) {
                            layer.closeAll('loading');
                            layer.msg("删除失败!", {icon: 5, skin: 'layer-ext-moon'});
                        }
                    });
                }, function () {

                });
            }
        });

        o('#addInfo').click(function () {
            layer.open({
                type: 2,
                title: "添加业务字典",
                shadeClose: false,
                btn: ['保存', '关闭'],
                area: ['800px', '380px'],
                content: ctx + '/businessController/addInfo',
                yes: function (index, layero) { //当前层索引、当前层DOM对象
                    //提交表单
                    var submit = layero.find('iframe').contents().find("#subBtn");// #subBtn为页面层提交按钮ID
                    submit.click();// 触发提交监听
                    return false;
                },
                offset: [
                    //0.1*($(window).height()-200)
                    100
                ]
            });

        });

    });

});



