<!DOCTYPE html >
<html xmlns:th="http://www.thymeleaf.org"
>
<head>
    <meta charset="UTF-8">
    <title>用户管理</title>
    <link rel="stylesheet" th:href="@{/admin/layui/css/layui.css}">
    <link>
    <script type="text/javascript" th:src="@{/admin/layui/layui.js}"></script>
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>

</head>

<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <blockquote class="layui-elem-quote quoteBox" id="search">
                        <input type="hidden" id="userSys" th:value="${isSys}">
                        <form id="search_form">
                            <div class="layui-inline">
                                <label class="layui-form-label w-auto">用户名：</label>
                                <div class="layui-input-inline mr0">
                                    <input class="layui-input" id="checkName" placeholder="请输入用户名">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label w-auto">账号：</label>
                                <div class="layui-input-inline mr0">
                                    <input class="layui-input" id="userName" placeholder="请输入账号">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label w-auto">身份证号：</label>
                                <div class="layui-input-inline mr0">
                                    <input class="layui-input" id="checkNumber" placeholder="请输入身份证号">
                                </div>
                            </div>
                            <div class="layui-inline layui-form">
                                <label class="layui-form-label">状态：</label>
                                <div class="layui-input-inline" style="width:186px ">
                                    <select name="isStart" id="isStart">
                                        <option value="">请选择状态</option>
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label class="layui-form-label w-auto">电话号码：</label>
                                <div class="layui-input-inline mr0">
                                    <input class="layui-input" id="phone" autocomplete="off" placeholder="请输入电话号码">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label label-width">所属部门：</label>
                                <div class="layui-input-inline " >
                                    <div class="xm-select" id="deptId">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <a class="layui-btn icon-btn" id="search_btn"><i class="layui-icon">&#xe615;</i>查询</a>
                                <a class="layui-btn" id="reset_btn"><i class="layui-icon">&#xe669;</i>重置</a>
                            </div>
                        </form>
                    </blockquote>
                    <table class="layui-hide" id="usertable" lay-filter="usertable"></table>
                    <script type="text/html" id="topToolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add_btn"><i
                                    class="layui-icon">&#xe608;</i>增加
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="edit_btn"><i
                                    class="layui-icon">&#xe642;</i>编辑
                            </button>
                            <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete_btn"><i
                                    class="layui-icon">&#xe640;</i>删除
                            </button>
                        </div>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script th:inline="javascript">

    function reloadXmselect(){

        $.ajax({
            url: ctx + "deptController/tree",
            method: "get",
            dataType: 'json',
            success: function (response) {
                dialog_deptId.update({
                    data: response.data
                })
                dialog_deptId.changeExpandedKeys(true);
            },
            error: function (res) {
            }
        });
    }

    $(function () {
        reloadXmselect();
    });
    //考生状态 下拉框初始化
    var dialog_deptId = xmSelect.render({
        el: '#deptId',
        filterable: true,
        name: 'deptId',
        tips: '请选择',
        // layVerify: 'required|uniqueDept',
        // layVerify: 'required',
        // layVerType: 'msg',
        model: {label: {type: 'block'}},
        template: function(item) {
                    // alert(JSON.stringify(item.name))
                    return '<p title="' + item.name + '">' + item.name + '</p>';
                },
        on: function (data) {
            //arr:  当前多选已选中的数据
            var arr = data.arr;
            //change, 此次选择变化的数据,数组
            var change = data.change;
            //isAdd, 此次操作是新增还是删除
            var isAdd = data.isAdd;
            // if (isAdd){
            //
            //    var value = dialog_dept.getValue();
            //     console.log(value)
            // }

            // alert('已有: ' + arr.length + ' 变化: ' + change.length + ', 状态: ' + isAdd)
        },
        // cascader: {
        //     //是否显示级联模式
        //     show: true,
        //     //间距
        //     indent: 200,
        //     //是否严格遵守父子模式
        //     strict: true,
        // },
        // showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
        // tree //开启树结构
        radio: true,//单选多选
        tree: {
            show: true,
            strict: false, //是否父子结构，父子结构父节点不会被选中
            indent: 30,//间距
            expandedKeys: true,
            clickCheck: true,
            clickExpand: true,//点击展开
        },
        clickClose: true,//点击关闭
        // autoRow: true,
        style: {
            paddingLeft: '10px',
            position: 'relative',
            width: '174px'
        },
        prop: {
            name: "deptName",
            value: "ID"
        },
        height: '200px',
        empty: '暂无数据',
        data: [],
        direction: 'auto',
    });

    layui.use(['layer', 'element', 'table', 'form', 'laydate'], function () {
        var admin = layui.admin,
            table = layui.table;
        var layer = layui.layer,
            form = layui.form,
            element = layui.element;
        var $ = layui.$;
        var laydate = layui.laydate;
        laydate.render({
            elem: '#lastLoginTime',
            range: true
        });
        laydate.render({
            elem: '#createTime',
            range: true
        });
        form.render();
        table.render({
            toolbar: '#topToolbar',
            defaultToolbar: ['filter'],
            elem: '#usertable',
            even: false,
            title: '用户列表',
            url: ctx + 'userController/userJson',
            page: true,
            cols: [
                [{type: 'checkbox'},
                    {field: 'fullName', title: '用户名', width: 120,align:'center'},
                    {field: 'userName', title: '账号', width: 120,align:'center'},
                    {
                        field: 'deptName',align:'center' ,title: '部门名称', width: 160, templet: function (data) {
                            if (data.dept==null||data.dept.deptName==null){
                                return "";
                            }
                            return data.dept.deptName;
                        }
                    },
                    {
                        field: 'idCardNumber',align:'center', title: '身份证号', width: 180, templet: function (data) {
                            if (data.userInfo==null||data.userInfo.idCardNumber==null) {
                                return "";
                            }
                            return data.userInfo.idCardNumber;
                        }
                    },
                    {field: 'phone', align:'center',title: '电话号码', width: 120},
                    {field: 'lastLoginTime',align:'center', title: '最后登录时间'},
                    {
                        field: 'isStart',align:'center', title: '状态', width: 100,
                        templet: function (data) {
                            var html = '';
                            if (!data.isStart) {
                                html += '<input type="checkbox" value=' + data.id + ' name="isStart" lay-skin="switch" lay-text="启用|停用">'
                            } else {
                                html += '<input type="checkbox" value=' + data.id + ' checked=true  name="isStart" lay-skin="switch" lay-text="启用|停用">'
                            }
                            return html;
                        }
                    }, {title:'操作',width:240,fixed: 'right',
                    templet:function(data){
                        var html = '';
                        var isys=$('#userSys').val();
                        html += '<a class="layui-btn layui-btn-xs layui-btn-primary" onclick=checkUser('+data.id+') lay-event="check">查看</a>' +
                            '<a class="layui-btn layui-btn-xs  layui-btn-normal" onclick=updataUser('+data.id+') lay-event="edit">编辑</a>' +
                            '<a class="layui-btn layui-btn-danger layui-btn-xs" onclick=deleteUser('+data.id+') lay-event="del">删除</a>' +
                            '<a class="layui-btn layui-btn-xs  layui-btn-normal" onclick=updataPassword('+data.id+') lay-event="edit">重置密码</a>'
                        return html;
                    }
                }
                ]
            ], request: {
                pageName: 'pageNum', //页码的参数名称，默认：page
                limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }, parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }

        });

        form.on('switch()', function (data) {
            var id = data.value;
            var isStart = this.checked ? 1 : 0;
            $.ajax({
                url: ctx + 'userController/updateShow?id=' + id + '&isStart=' + isStart,
                success: function (res) {
                    if (res.code == 0) {
                        layer.msg('修改成功', {icon: 1});
                    } else {
                        layer.msg('修改失败', {icon: 2});
                    }
                },
                error: function (data) {
                    layer.msg('操作失败', {icon: 2});
                }
            });
        });
        var active = {
            reload: function () {
                var checkName = $("#checkName"), checkNumber = $("#checkNumber"),
                    deptId = dialog_deptId.getValue('valueStr'), phone = $("#phone"), isStart = $("#isStart"),
                    createTime = $("#createTime"), lastLoginTime = $("#lastLoginTime"),
                    userName = $("#userName");
                table.reload('usertable', {
                    page: {
                        curr: 1
                    },
                    where: {
                        fullName: checkName.val(), idCardNumber: checkNumber.val(),
                        deptidForSelect: deptId, phone: phone.val(), isStart: isStart.val(),
                        createTime: createTime.val(), lastLoginTime: lastLoginTime.val(),
                        userName: userName.val()
                    }
                })
            }
        }
//头工具栏事件
        table.on('toolbar(usertable)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'add_btn':
                    var urlstr = ctx + 'userController/addUser';
                    var tit = "添加用户";
                    top.layui.index.openTabsPage(urlstr, tit);
                    break;
                case 'edit_btn':
                    var data = checkStatus.data;
                    if (data.length < 1) {
                        layer.msg('请选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    if (data.length > 1) {
                        layer.msg('只能选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    updataUser(checkStatus.data[0].id);
                    break;
                case 'delete_btn':
                    var data = checkStatus.data;
                    if (data.length < 1) {
                        layer.msg('请至少选中一行数据', {
                            icon: 2
                        });
                        return;
                    }
                    //获取选中的行id
                    var ids = '';
                    for (var i = 0; i < data.length - 1; i++) {
                        ids += data[i].id + ",";
                    }
                    ids += data[data.length - 1].id
                    deleteUser(ids);
                    break;
            }
            ;
        });
        $("blockquote").on('keydown','input,select',function (e) {
            if (e.keyCode === 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                $('#search_btn').trigger("click");//触发搜索按钮的点击事件
            }
        })
        $("#search_btn").click(function () {
            var type = 'reload';
            active[type] ? active[type].call(this) : '';
        })
        $("#reset_btn").click(function () {
            window.location.reload();
        })
    });

    function deleteUser(id) {
        var url = ctx + 'userController/deleteInfo?ids=' + id;
        layer.confirm('您确定要删除？', {icon: 3, btn: ['确定', '取消'], title: '提示'}, function () {
            var loading = layer.msg('正在删除', {icon: 16, shade: 0.3, time: 0});
            $.ajax({
                url: url,
                success: function (res) {
                    layer.close(loading);
                    if (res.code == 0) {
                        layer.msg('删除成功', {icon: 1});
                        location.reload();
                    } else {
                        layer.alert(res.msg, {icon: 2});
                    }

                },
                error: function (res) {
                    layer.close(loading);
                    layer.alert('操作失败', {icon: 2});
                }
            })
        })
    }

    function updataUser(id) {
        var urlstr = ctx + 'userController/userInfo?userId=' + id;
        var tit = "编辑用户";
        top.layui.index.openTabsPage(urlstr, tit);
    }
    function updataPassword(id){
        var urlstr = ctx + 'adminPwController/pwinfo?userId=' + id;
        var tit = "重置密码";
        top.layui.index.openTabsPage(urlstr,tit);
    }
    function checkUser(id) {
        var urlstr = ctx + 'userController/showUser?userId=' + id;
        var tit = "查看用户";
        top.layui.index.openTabsPage(urlstr, tit);
    }

    $(document).on("click", ".layui-table-body table.layui-table tbody tr", function () {
        var obj = event ? event.target : event.srcElement;
        var tag = obj.tagName;
        var checkbox = $(this).find("td div.laytable-cell-checkbox div.layui-form-checkbox I");
        if (checkbox.length != 0) {
            if (tag == 'DIV') {
                checkbox.click();
            }
        }

    });

    $(document).on("click", "td div.laytable-cell-checkbox div.layui-form-checkbox", function (e) {
        e.stopPropagation();
    });
</script>
</body>
</html>