<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="utf-8">
    <title>车辆频次列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <div th:replace="Importfile::html"></div>
    <script th:src="@{/plugins/formSelects/xm-select.js}"></script>
    <style>
        /*固定input宽度*/
        .layui-input, .layui-textarea {
            display: block;
            width: 180px;
            padding-left: 10px;
        }
        .color-block {
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-right: 5px;
        }
        .red-block {
            background-color: red;
        }
        .orange-block {
            background-color: orange;
        }

        .table-cell-orange {
            color: orange;
        }

        .table-cell-red {
            color: red;
        }

        /*#black-table tr {*/
        /*    height: 90px;*/
        /*    line-height: 90px;*/
        /*    vertical-align: middle;*/
        /*}*/

        /*tbody .layui-table-cell {*/
        /*    height: 90px;*/
        /*    line-height: 90px;*/
        /*    vertical-align: middle;*/
        /*    vertical-align: middle;*/
        /*}*/
    </style>
</head>

<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <blockquote class="layui-elem-quote quoteBox" id="search">

<!--                <div class="layui-inline">-->
<!--                    <input type="text" name="startDate" id="startDate" placeholder="开始日期" autocomplete="off"-->
<!--                           class="layui-input">-->
<!--                </div>-->
<!--                <div class="layui-inline">-->
<!--                    <input type="text" name="endDate" id="endDate" placeholder="结束日期" autocomplete="off"-->
<!--                           class="layui-input">-->
<!--                </div>-->

                <div class="layui-inline" style="width: 270px;">
                    <label class="layui-form-label">开始日期:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="startDate"
                               placeholder="请输入开始日期" style="width: 160px;">
                    </div>
                </div>

                <div class="layui-inline" style="width: 270px;">
                    <label class="layui-form-label">结束日期:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="endDate"
                               placeholder="请输入开始日期" style="width: 160px;">
                    </div>
                </div>
                <div class="layui-inline" style="width: 270px;">
                    <label class="layui-form-label" >车牌号:</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" id="carNum" name="carNum" placeholder="请输入车牌号" style="width: 160px;">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">地点：</label>
                    <div class="layui-input-inline">
                        <div class="xm-select-demo" id="typeSelect"></div>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label" style="width: unset;padding: 9px 5px 9px 5px;"></label>
                    <button class="layui-btn icon-btn" id="searchBtn"><i class="layui-icon">&#xe615;</i>查询</button>
                    <button class="layui-btn" id="unsetBtn"><i class="layui-icon">&#xe669;</i>重置</button>
                </div>

                <div class="layui-inline" style="float: right;">
                    <span class="layui-form-mid layui-word-aux" style="margin-left: 5px;">
                        <span class="color-block red-block"></span>：黑名单
                    </span>
                    <span class="layui-form-mid layui-word-aux" style="margin-left: 5px;">
                            <span class="color-block orange-block"></span>：疑似
                    </span>
                </div>
            </blockquote>
            <table class="layui-hide" id="black-table" lay-filter="black-table"></table>



            <script type="text/html" id="topToolbar">

                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="export_layui">
                        <i class="layui-icon">&#xe601;</i>导出
                    </button>
                </div>
            </script>

        </div>
    </div>
</div>
<script type="text/html" id="state-column">
    {{ d.stateId == '1' ? '有效' : '无效' }}
</script>
<!--<script type="text/html" id="type-text">-->
<!--    {{#  if(d.type === 0){ }}-->
<!--    普通名单-->
<!--    {{#  } else if(d.type === 1){ }}-->
<!--    疑似名单-->
<!--    {{#  } else if(d.type === 2){ }}-->
<!--    黑名单-->
<!--    {{#  } else if(d.type === -1){ }}-->
<!--    白名单-->
<!--    {{#  } else { }}-->
<!--    未定义-->
<!--    {{#  } }}-->
<!--</script>-->

<!--<script type="text/html" id="type-text-color">-->
<!--    <% if (d.type === 1) { %>-->
<!--    <span class="table-cell-orange">{{ d.type }}</span>-->
<!--    <% } else if (d.type === 2) { %>-->
<!--    <span class="table-cell-red">{{ d.type }}</span>-->
<!--    <% } else { %>-->
<!--    {{ d.type }}-->
<!--    <% } %>-->
<!--</script>-->

<script>
    //定时刷新 30s
    // setInterval(function () {
    //     window.location.reload();
    // },30000);

    var deptId = [[${deptId}]];
    console.log(deptId);

    //回车搜索
    $("blockquote").on('keyup', 'input,select', function (e) {
        if (e.keyCode == 13) {//.which属性判断按下的是哪个键,回车键的键位序号为13
            $("#searchBtn").trigger("click");//触发搜索按钮的点击事件
        }
    });

    layui.use(['laydate', 'table'], function () {
        var admin = layui.admin
            , table = layui.table;
        var laydate = layui.laydate;



        // 渲染日期选择器
        laydate.render({
            elem: '#startDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            max: new Date().toLocaleString('zh-CN', {hour12: false}).replace(/\//g, '-').replace(',', '')
        });

        laydate.render({
            elem: '#endDate',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            max: new Date().toLocaleString('zh-CN', {hour12: false}).replace(/\//g, '-').replace(',', ''),

        });

        // 获取今天的日期
        var today = new Date();
        var year = today.getFullYear();
        var month = ('0' + (today.getMonth() + 1)).slice(-2);
        var day = ('0' + today.getDate()).slice(-2);
        var startDateTime = year + '-' + month + '-' + day + ' 00:00:00';
        var endDateTime = year + '-' + month + '-' + day + ' 23:59:59';

        document.getElementById('startDate').value = startDateTime;
        document.getElementById('endDate').value = endDateTime;

        var tableIns = table.render({
            elem: '#black-table'
            , id: 'black-table'
            , toolbar: '#topToolbar'
            , defaultToolbar: [
                // {
                // title: '导出'
                // ,layEvent: 'exports1'
                // ,icon: 'layui-icon-export'
                // },
                'print','filter']
            , height: 'full-50'
            , url: ctx + 'carController/carJson'
            , title: '车辆频次列表'
            , cols: [
                [
                    {type: 'checkbox'},
                    {field: 'carNum', title: '车牌', align: 'center',
                    templet: function(d) {
                        if (d.type === 1){
                            return '<span style="color:orange">'+d.carNum+'</span>';
                        }else if (d.type === 2) {
                            return '<span style="color:red">'+d.carNum+'</span>';
                        }else {
                            return '<span >'+d.carNum+'</span>';
                        }
                    }}
                    , {field: 'count', title: '出现次数', align: 'center',sort:'true',width:'12%',
                    templet: function(d) {
                        if (d.type === 1){
                            return '<span style="color:orange">'+d.count+'</span>';
                        }else if (d.type === 2) {
                            return '<span style="color:red">'+d.count+'</span>';
                        }else {
                            return '<span >'+d.count+'</span>';
                        }
                    }}
                    , {field: 'totalCount', title: '总出现次数', align: 'center',sort:'true',width:'12%',
                    templet: function(d) {
                        if (d.type === 1){
                            return '<span style="color:orange">'+d.totalCount+'</span>';
                        }else if (d.type === 2) {
                            return '<span style="color:red">'+d.totalCount+'</span>';
                        }else {
                            return '<span >'+d.totalCount+'</span>';
                        }
                    }}
                    , {field: 'createTime', title: '最近出现时间', align: 'center',
                    templet: function(d) {
                        if (d.type === 1){
                            return '<span style="color:orange">'+d.createTime+'</span>';
                        }else if (d.type === 2) {
                            return '<span style="color:red">'+d.createTime+'</span>';
                        }else {
                            return '<span >'+d.createTime+'</span>';
                        }
                    }}
                    , {field: 'type', title: '类型', align: 'center', width: '8%',
                    templet: function(d) {
                        if (d.type === 1){
                            return '<span style="color:orange">疑似</span>';
                        }else if (d.type === 2) {
                            return '<span style="color:red">黑名单</span>';
                        }else {
                            return '<span style="color:black">普通</span>';
                        }
                    }}
                    , {field: 'type', title: '转入操作', align: 'center',
                    templet: function(d) {
                        if (d.type === 0) {
                            var s =
                                '<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="toBeSuspected">转疑似</a>' +
                                '<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="toBeBlack">转黑名单</a>' +
                                '<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="toBeWhite">转白名单</a>' ;
                            return s;
                        }else if (d.type === 1){
                            return '<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="toBeBlack">转黑名单</a>'+
                                '<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="toBeWhite">转白名单</a>' ;
                        }else if (d.type === 2) {
                            return '<span class="layui-badge layui-bg-gray">黄牛车</span> &nbsp;&nbsp;'+
                                '<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="toBeWhite">转白名单</a>' ;
                        }else {
                            return '<span class="layui-badge layui-bg-gray">内部车</span>'
                        }

                    }}
                    , {
                        // fixed: 'right', title: '详情', toolbar: '#table-toolbar-bar', align: 'center'}
                        fixed: 'right', title: '详情', align: 'center',width:'10%',
                    templet: function(d) {
                        if (d.type === 1){
                            return '<a style="color: orange" class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">查看记录</a>';
                        }else if (d.type === 2) {
                            return '<a style="color: red" class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">查看记录</a>';
                        }else {
                            return '<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">查看记录</a>';
                        }
                    }}
                ]
            ],
            where: {  // 添加默认查询参数
                startDate: startDateTime,
                endDate: endDateTime
            }
            , request: {
                pageName: 'currentPage' //页码的参数名称，默认：page
                , limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            , limit: 10
            , limits: [10, 20, 30, 50]// 可选的每页显示条目数选项
            , parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.list //解析数据列表
                }
            }
            , page: true,
            method: 'post', // 设置请求方法为 POST
            contentType: 'application/json', // 设置请求内容类型为 JSON
        });

        table.on('toolbar(black-table)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                // case 'exports1':
                //     var data = checkStatus.data;
                //     if (data.length === 0){
                //         layer.confirm('未勾选数据，是否导出当前页面数据', {
                //             title: '操作确认',  // 弹窗标题
                //             icon: 0,          // 图标类型（0-信息，1-成功，2-错误，3-问号）
                //             btn: ['确定', '取消'] // 按钮文本
                //         }, function(index){
                //             // 用户点击"确定"后的回调
                //             var currentData = table.cache['black-table'];
                //             table.exportFile('black-table', currentData, 'xls');
                //             layer.close(index); // 关闭弹窗
                //         }, function(index){
                //             // 用户点击"取消"后的回调
                //             layer.close(index); // 关闭弹窗
                //             console.log("操作取消");
                //         });
                //     }else {
                //         layer.confirm('已勾选数据，是否导出勾选数据', {
                //             title: '操作确认',  // 弹窗标题
                //             icon: 0,          // 图标类型（0-信息，1-成功，2-错误，3-问号）
                //             btn: ['确定', '取消'] // 按钮文本
                //         }, function(index){
                //             // 用户点击"确定"后的回调
                //             table.exportFile('black-table', data, 'xls');
                //             layer.close(index); // 关闭弹窗
                //         }, function(index){
                //                 // 用户点击"取消"后的回调
                //                 layer.close(index); // 关闭弹窗
                //                 console.log("操作取消");
                //         })
                //     }
                //     break;
                case 'export_layui':

                    // 获取查询条件
                    var checkStatus = table.checkStatus(obj.config.id);
                    var selectedIds = checkStatus.data.map(item => item.code).join(',');
                    var startDate = $("#startDate").val();
                    var endDate = $("#endDate").val();
                    var carNum = $("#carNum").val();

                    const carModel = []
                    typeSelect.getValue().forEach(data => {
                        carModel.push(data.ID)
                    })



                    $.ajax({
                        url: ctx + "/carController/exportExcel",
                        type: 'POST',
                        data: {
                            startDate: startDate,
                            endDate: endDate,
                            carNum: carNum,
                            selectedIds: selectedIds,
                            deptIds: carModel
                        },
                        xhrFields: {
                            responseType: 'blob' // 处理二进制文件
                        },
                        success: function (data, status, xhr) {
                            // 创建 Blob 对象并下载
                            var blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                            var url = window.URL.createObjectURL(blob);
                            var a = document.createElement('a'); // 创建一个链接元素
                            a.href = url;
                            a.download = "车辆报表信息.xlsx"; // 使用默认文件名
                            a.style.display = 'none'; // 隐藏链接
                            document.body.appendChild(a);
                            a.click(); // 自动点击链接以下载文件
                            document.body.removeChild(a); // 移除链接
                            window.URL.revokeObjectURL(url); // 释放 URL 对象
                        },
                        error: function () {
                            // alert('导出失败，请重试！');

                            var errorMsg = '导出失败，请重试！';

                            layer.alert(errorMsg, {
                                icon: 2,
                                title: '导出错误'
                            });
                        }
                    });
                    break;
            }
        });

        //监听行工具事件
        table.on('tool(black-table)', function (obj) {
            var data = obj.data;
            var carId=data.carId;
            if (!carId){
                carId=data.code
            }
            if (obj.event === 'detail') {
                var url = ctx + 'carRecordController/toRecordPage/' + carId,
                    title = '车辆辆采集记录';
                top.layui.index.openTabsPage(url, title);
            }else if (obj.event === 'toBeSuspected') {
                layer.confirm('您确定要转入疑似黄牛吗？', function (index) {
                    var loading = layer.msg('正在转入', {icon: 16, shade: 0.3, time: 0});
                    $.ajax({
                        url: ctx + 'carController/insertSuspectedDialog/' + carId,
                        method: 'post',
                        // data: {id: data.id},
                        dataType: 'JSON',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code != 0) {
                                layer.alert(res.msg, {icon: 2});
                            } else {
                                table.reload('black-table'); //只重载数据
                                layer.msg(res.msg, {icon: 1});
                            }
                        },
                        error: function (res) {
                            layer.close(loading);
                            layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                        }
                    });
                    layer.close(index);
                });
            }else if (obj.event === 'toBeBlack'){
                layer.confirm('您确定要转入黑名单吗？', function (index) {
                    var loading = layer.msg('正在转入', {icon: 16, shade: 0.3, time: 0});
                    $.ajax({
                        url: ctx + 'carController/insertBlackDialog/' + carId,
                        method: 'post',
                        // data: {id: data.id},
                        dataType: 'JSON',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code != 0) {
                                layer.alert(res.msg, {icon: 2});
                            } else {
                                table.reload('black-table'); //只重载数据
                                layer.msg(res.msg, {icon: 1});
                            }
                        },
                        error: function (res) {
                            layer.close(loading);
                            layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                        }
                    });
                    layer.close(index);
                });
            }
            if (obj.event === 'toBeWhite'){
                layer.confirm('您确定要转入白名单吗？', function (index) {
                    var loading = layer.msg('正在转入', {icon: 16, shade: 0.3, time: 0});
                    $.ajax({
                        url: ctx + 'carController/insertWhiteDialog/' + carId,
                        method: 'post',
                        // data: {id: data.id},
                        dataType: 'JSON',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code != 0) {
                                layer.alert(res.msg, {icon: 2});
                            } else {
                                table.reload('black-table'); //只重载数据
                                layer.msg(res.msg, {icon: 1});
                            }
                        },
                        error: function (res) {
                            layer.close(loading);
                            layer.alert(res.ErrorMessage, {icon: 2, anim: 6});
                        }
                    });
                    layer.close(index);
                });
            }
        });

        //搜索及重置按钮
        $("#searchBtn").click(function () {

            const carModel = []
            typeSelect.getValue().forEach(data => {
                carModel.push(data.ID)
            })

            tableIns.where = {};

            table.reload('black-table', {
                where: { //设定异步数据接口的额外参数，任意设
                    startDate: $("#startDate").val(),
                    endDate: $("#endDate").val(),
                    carNum: $("#carNum").val(),
                    deptIds: carModel
                },
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                method: 'post',
                contentType: 'application/json' ,// 设置请求的内容类型为 JSON
                done: function(res) {
                    // 数据加载完成后的回调
                    console.log('表格重载完成', res);
                    // this.where = {};
                    tableIns = this;
                }
            }); //只重载数据
        });
        //搜索及重置按钮
        $("#unsetBtn").click(function () {
            location.reload();
        })

        var typeSelect = xmSelect.render({
            el: '#typeSelect',
            radio: false,
            // clickClose: true,
            filterable: true,
            toolbar: {show: true},
            name: 'deptIds',
            layVerify: 'required',
            prop: {name: 'deptName', value: 'ID'},
            data: [],
            // 启用严格模式，确保不会有重复选中项
            strict: true,
            style: {
                paddingLeft: '0px',
                position: 'relative',
                width: '160px',
                height: '38px'
            },
            tree: {
                show: true,
                strict: false, //是否父子结构，父子结构父节点不会被选中
                indent: 30,//间距
                expandedKeys: [-1],
                clickCheck: true,
                clickExpand: true,//点击展开
            },

        });

        $.ajax({
            url: ctx + '/deptController/deptDetail',
            type: 'PUT',
            success: function (res) {

                // 1. 将ID统一转为字符串
                // var data = res.data.map(item => {
                //     return {
                //         deptName: item.deptName,
                //         ID: item.ID.toString() // 确保ID是字符串
                //     };
                // });

                console.log("data",res.data);

                // 3. 更新下拉框数据
                typeSelect.update({
                    data: res.data,
                });

                // 4. 设置初始值（确保deptId转为字符串）
                var initValue = deptId ? deptId.toString() : "";
                if (initValue) {
                    typeSelect.setValue([initValue]);
                }
            }
        });
    });
</script>



</body>
</html>