<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fwy.dic.dao.DataDetailDao">
    <select id="findAll"
            resultType="com.fwy.dic.entity.DataDetail">
        select detail.ID,
               detail.DICCODE,
               detail.DICVALUE,
               detail.DISCRIPTION,
               detail.SORTNUM,
               date_format(detail.CREATETIME, '%Y-%m-%d') as createtime,
               date_format(detail.UPDATETIME, '%Y-%m-%d') as updatetime,
               type.DICTYPE                               as dicType,
               detail.ISSTART
        from dic_datadetail detail
                     left join dic_datatype type on detail.DICCODE = type.DICCODE
        <where>
            <if test="dicValue != null and dicValue != ''">
                detail.DICVALUE like concat('%',#{dicValue},'%')
            </if>
            <if test="dicCode != null and dicCode != ''">
                and detail.DICCODE = #{dicCode}
            </if>
        </where>

        order by detail.SORTNUM asc
    </select>

    <select id="findOneById"
            resultType="com.fwy.dic.entity.DataDetail">
        select *
        from dic_datadetail
        where ID = #{id}
    </select>

    <delete id="deleteDataDetail"
            parameterType="com.fwy.dic.entity.DataDetail">
        delete
        from dic_datadetail
        where ID = #{id}
    </delete>

    <update id="changeStart"
            parameterType="com.fwy.dic.entity.DataDetail">
        update dic_datadetail
        <set>
            ISSTART = #{isStart}
        </set>
        where ID = #{id}
    </update>

    <update id="updateDataDetail"
            parameterType="com.fwy.dic.entity.DataDetail">
        update dic_datadetail
        <set>
            <if test="dicCode != null">
                DICCODE = #{dicCode},
            </if>
            <if test="dicValue != null">
                DICVALUE = #{dicValue},
            </if>
            <if test="disCription != null">
                DISCRIPTION = #{disCription},
            </if>
            <if test="isStart != null">
                ISSTART = #{isStart},
            </if>
            <if test="sortNum != null">
                SORTNUM=#{sortNum},
            </if>
            UPDATETIME = now()
        </set>
        where ID = #{id}
    </update>
    <insert id="addDataDetail"
            parameterType="com.fwy.dic.entity.DataDetail">
        insert into dic_datadetail(ID, DICCODE, DICVALUE,
                                   DISCRIPTION,
                                   SORTNUM, CREATETIME, UPDATETIME, ISSTART)
        values (#{id}, #{dicCode}, #{dicValue},
                #{disCription},
                #{id}, now(), now(), #{isStart})
    </insert>

    <select id="formSelectByCode"
            resultType="com.fwy.dic.entity.DataDetail">
        select *
        from dic_datadetail
        where DICCODE = #{dicCode}
        order by SORTNUM asc
    </select>

    <select id="formSelectByCodeStatus"
            resultType="com.fwy.dic.entity.DataDetail">
        select *
        from dic_datadetail
        where DICCODE = #{dicCode} AND ISSTART != 0
        order by SORTNUM asc
    </select>

    <select id="getDicDataByCodeAndId"
            resultType="com.fwy.dic.entity.DataDetail">
        select *
        from dic_datadetail
        where DICCODE = #{dicCode}
          and DICVALUE = cast(#{dataId} as char)
    </select>

    <select id="list" resultType="com.fwy.dic.entity.DataDetail">
        select dd.*
        from dic_datadetail dd
        where dd.DICCODE = #{doccode}
    </select>

    <select id="getObjectBy" resultType="com.fwy.dic.entity.DataDetail">
        select dd.*
        from dic_datadetail dd
        where dd.DICCODE = #{dicCode}
          and dd.DICVALUE = #{dicValue}
    </select>
    <!--	//获取上一个-->
    <select id="getUpOne" resultType="com.fwy.dic.entity.DataDetail">
        select *
        from (select dd.*
              from dic_datadetail dd
                           join
                           (select SORTNUM, DICCODE from DIC_DATADETAIL where ID = #{id}) cc
                                   on dd.SORTNUM &lt; cc.SORTNUM and dd.DICCODE = cc.DICCODE
              order by dd.SORTNUM + 0 desc) a
        limit 1
    </select>

    <select id="getDownOne" resultType="com.fwy.dic.entity.DataDetail">
        select *
        from (select dd.*
              from dic_datadetail dd
                           join
                           (select SORTNUM, DICCODE from DIC_DATADETAIL where id = #{id}) cc
                                   on dd.SORTNUM &gt; cc.SORTNUM and dd.DICCODE = cc.DICCODE
              order by dd.SORTNUM + 0 asc) a
        limit 1
    </select>
    <select id="getAutoAddId" resultType="java.lang.Long">
--         SELECT auto_increment
--         FROM information_schema.`TABLES`
--         WHERE TABLE_NAME = 'dic_datadetail'
--         order by AUTO_INCREMENT desc
--         limit 1
        select id+1 from dic_datadetail order by id desc limit 1
    </select>
</mapper>