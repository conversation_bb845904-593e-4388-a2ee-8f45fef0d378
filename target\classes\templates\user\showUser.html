<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
	  xmlns:http="http://www.w3.org/1999/xhtml"
	  xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
	<meta charset="utf-8">
	<title>用户信息</title>
	<div th:replace="Importfile::html"></div>
	<script th:src="@{/plugins/formSelects/xm-select.js}"></script>
	<style>

		.layui-form-label{
			width: 150px;
		}
		.layui-input-inline{
			width: 250px;
		}
		.layui-input{
			width: 240px;
		}
	</style>
</head>
<script th:inline="javascript" >
	$(function () {
		$("#password").val('');

		var yhyxqsStr = [[${yhyxqs}]];
		var yhyxqStr = [[${yhyxq}]];
		var mmyxqsStr = [[${mmyxqs}]];
		var mmyxqStr = [[${mmyxq}]];

		if(yhyxqsStr==null || yhyxqsStr == ''){
			// 用户有效期始默认当天
			var date=new Date();
			$("#yhyxqs").val(formatDate(date));
		}

		if(yhyxqStr==null || yhyxqStr == ''){
			// 用户有效期止默认30天
			var date=new Date();
			date.setDate(date.getDate()+30);
			$("#yhyxq").val(formatDate(date));
		}

		if(mmyxqsStr==null || mmyxqsStr == ''){
			// 密码有效期始默认当天
			var date=new Date();
			$("#mmyxqs").val(formatDate(date));
		}

		if(mmyxqStr==null || mmyxqStr == ''){
			// 密码有效期止默认15天
			var date=new Date();
			date.setDate(date.getDate()+15);
			$("#mmyxq").val(formatDate(date));
		}


	})
	function formatDate(date) {
		var y = date.getFullYear();
		var m = date.getMonth() + 1;
		m = m < 10 ? '0' + m : m;
		var d = date.getDate();
		d = d < 10 ? ('0' + d) : d;
		return y + '-' + m + '-' + d;
	};
</script>
<body>
<div class="layui-fluid">
	<div class="layui-card">
		<div class="layui-card-body">
			<fieldset class="layui-elem-field layui-field-title"
					  style="margin-top: 20px;">
				<legend>用户信息</legend>
			</fieldset>

			<form class="layui-form" action="">
				<input type="hidden" name="id" id="id" th:value="${user?.id}">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>用户名：</label>
						<div class="layui-input-inline">
							<input type="text" name="userName" lay-verify="required|unique" th:if="${user!=null}"
								   placeholder="请输入用户名" autocomplete="off" th:value="${user?.userName}" class="layui-input" readonly >
						</div>
					</div>
					<div class="layui-inline" th:if="${user==null}">
						<label class="layui-form-label"><span style="color:red">*</span>密码：</label>
						<div class="layui-input-inline">
							<input type="password" name="password" lay-verify="required|passwordLength"
								   class="layui-input">
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>民警姓名：</label>
						<div class="layui-input-inline">
							<input type="text" name="fullName" lay-verify="required|fullnameLength"
								   placeholder="请输入民警姓名" autocomplete="off" th:value="${user?.fullName}" class="layui-input" readonly>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>身份证号：</label>
						<div class="layui-input-inline">
							<input type="text" name="idCardNumber" lay-verify="required|identity|uniqueCard"
								   placeholder="请输入身份证号" autocomplete="off" th:value="${userInfo?.idCardNumber}" class="layui-input" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">警员编号(员工编号)：</label>
						<div class="layui-input-inline">
							<input type="text" name="jybh" lay-verify="required"
								   placeholder="请输入警员编号(员工编号)" autocomplete="off" th:value="${userInfo?.jybh}" class="layui-input" readonly>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>角色权限：</label>
						<div class="layui-input-inline" style="width: 240px">
							<input type="hidden"  th:value="${roleIds}" id="roleIds">
							<div class="xm-select" id="roleIdSelect">
							</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item">

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>联系电话：</label>
						<div class="layui-input-inline">
							<input type="tel" name="phone" lay-verify="required|phone" placeholder="请输入电话号码"
								   th:value="${user?.phone}" class="layui-input" readonly>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="margin-left: 0px"><span style="color:red">*</span>所属部门：</label>
						<div class="layui-input-inline" style="width: 240px">
							<input type="text" name="deptName" lay-verify="required"
								   autocomplete="off" th:value="${user?.dept?.deptName}" class="layui-input" disabled="disabled">
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>最后登录时间：</label>
						<div class="layui-input-inline">
							<input type="text" name="fullName" lay-verify="required|fullnameLength"
								   placeholder="" autocomplete="off" th:value="${lasttime}" class="layui-input" readonly>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>最后登录IP：</label>
						<div class="layui-input-inline">
							<input type="text" name="idCardNumber" lay-verify="required|identity|uniqueCard"
								   placeholder="" autocomplete="off" th:value="${user?.lastLoginIp}" class="layui-input" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>用户有效期始：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="yhyxqsStr" id="yhyxqs" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(userInfo?.yhyxqs, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>用户有效期止：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="yhyxqStr" id="yhyxq" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(userInfo?.yhyxq, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>密码有效期始：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="mmyxqsStr" id="mmyxqs" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(userInfo?.mmyxqs, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>密码有效期止：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="text" name="mmyxqStr" id="mmyxq" lay-verify="date"
									   autocomplete="off" th:value="${#dates.format(userInfo?.mmyxq, 'yyyy-MM-dd')}" class="layui-input" readonly>
							</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">职位：</label>
						<div class="layui-input-inline" style="width: 240px">
							<input type="hidden"  th:value="${userInfo?.position}" id="position">
							<div class="xm-select" id="positionSelect">
							</div>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="width: 100px">学历：</label>
						<div class="layui-input-inline" style="width: 240px">
							<input type="hidden"  th:value="${userInfo?.degree}" id="education">
							<div class="xm-select" id="educationSelect">
							</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">政治面貌：</label>
						<div class="layui-input-inline" style="width: 240px">
							<input type="hidden"  th:value="${userInfo?.politicalStatus}" id="politicalStatus">
							<div class="xm-select" id="politicalStatusSelect">
							</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>客户端IP：</label>
						<div class="layui-input-inline">
							<input type="text" name="allowIp" lay-verify="validip"
								   placeholder="请输入客户端IP" autocomplete="off" th:value="${userInfo?.allowIp}" class="layui-input" readonly>
						</div>
					</div>

					<div class="layui-inline">
						<label class="layui-form-label"><span style="color:red">*</span>创建用户：</label>
						<div class="layui-input-inline">
							<input type="text" placeholder="" autocomplete="off" th:value="${userInfo?.createBy}" class="layui-input" readonly>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">是否警员:</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="1" th:attr=" checked=${userInfo?.sfjy == 1 ? true : false}" name="sfjy" id="sfjy" lay-skin="switch" lay-text="是|否" disabled>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">启用状态:</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="true" th:attr="checked=${user?.isStart== 1 ? true : false}" name="isStart" id="isStart" lay-skin="switch" lay-text="启用|停用" disabled>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline" th:if="${session.user != null &&session.user.isSys==1}">
						<label class="layui-form-label">是否系统级:</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="true" th:attr="checked=${user?.isSys == 1 ? true : false}" name="isSys" id="isSys" lay-skin="switch" lay-text="是|否" disabled>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">是否超级管理员:</label>
						<div class="layui-input-inline">
							<input type="checkbox" value="1" th:attr="checked=${userInfo?.isAdmin == 1 ? true : false}" name="isSys" id="isAdmin" lay-skin="switch" lay-text="是|否" disabled>
						</div>
					</div>
				</div>

				<div class="layui-form-item" style="text-align: center;">

					<button class="layui-btn layui-btn-normal"  id="cancelBtn" >返回</button>

				</div>
			</form>
		</div>
	</div>
</div>

<script>
	var formSelects = layui.formSelects;


	layui.use([ 'form', 'layedit', 'laydate', 'jquery'  ],
			function() {
				var form = layui.form,
						layer = layui.layer,
						layedit = layui.layedit,
						laydate = layui.laydate,
						$ = jQuery = layui.$;
				// 回到列表页面
				$('#cancelBtn').click(function(){
					window.parent.changTabs( ctx +'userController/userList','','用户列表');
				})




				//政治面貌

				form.render();


			});

	$(function () {
		reloadXmselect();
	});

	function reloadXmselect() {
		//角色权限下拉框
		$.ajax({
			url : ctx + 'roleController/roleDialogJsonX',
			data: {"roleIds": $("#roleIds").val()},
			method: "get",
			dataType: 'json',
			success: function (response) {
				dialog_role.update({
					data:response.data,
				});
			},
			error: function (res) {
			}
		});
		//职位下拉框
		$.get(ctx + '/dataDetailController/formSelectByCode?dicCode=position&id='+$("#position").val(),function (res) {
			dialog_position.update({
				data:res.data,
			});
		})
		//教育下拉框
		$.get( ctx + '/dataDetailController/formSelectByCode?dicCode=education&id='+ $("#education").val(),function (res) {
			dialog_edu.update({
				data:res.data,
			});
		})
		//政治面貌
		$.get( ctx + '/dataDetailController/formSelectByCode?dicCode=politicalStatus&id='+ $("#politicalStatus").val(),function (res) {
			dialog_politicalStatus.update({
				data:res.data,
			});
		})
	}
	//政治面貌
	var dialog_politicalStatus = xmSelectRender("#politicalStatusSelect",[],'politicalStatus',true,'disCription','dicValue');
	//教育
	var dialog_edu = xmSelectRender("#educationSelect",[],'education',true,'disCription','dicValue');
	//职位
	var dialog_position = xmSelectRender("#positionSelect",[],'position',true,'disCription','dicValue');
	//角色
	var dialog_role = xmSelectRender("#roleIdSelect",[],'roleIds',false,'roleName','id');
	//id：初始化 id ，
	//data: 喧嚷数据。
	//name: 提交时传递的名字
	//radio ：单选还是多选
	//dataName:自定义数据名称
	//dataValue：自定义数据的值
	function xmSelectRender(id,data,name,radio,dataName,dataValue) {
		//核验类型
		return  xmSelect.render({
			el: id,
			// filterable: true,
			name: name,
			tips: '请选择',
			disabled: true,
			// layVerify: 'required|uniqueDept',
			layVerify: 'required',
			layVerType: 'msg',
			model: {label: {type: 'block'}},
			template:function(item) {
				return '<p title="' + item.name + '">' + item.name + '</p>';
			},
			on: function (data) {


			},
			// cascader: {
			//     //是否显示级联模式
			//     show: true,
			//     //间距
			//     indent: 200,
			//     //是否严格遵守父子模式
			//     strict: true,
			// },
			// showCount: 5,这里是下拉款中显示的数量，配合搜索框使用
			// tree //开启树结构
			radio: radio,//单选多选
			tree: {
				show: true,
				strict: false, //是否父子结构，父子结构父节点不会被选中
				indent: 30,//间距
				expandedKeys: [-1],
				clickCheck: true,
				clickExpand: true,//点击展开
			},
			// clickClose: true,//点击关闭
			autoRow: true,
			style: {
				paddingLeft: '10px',
				position: 'relative',
				width: '228px'
			},
			prop: {
				name: dataName,
				value: dataValue
			},
			height: '200px',
			empty: '暂无数据',
			data: data,
			direction: 'auto',
		});
	}
</script>
</body>
</html>