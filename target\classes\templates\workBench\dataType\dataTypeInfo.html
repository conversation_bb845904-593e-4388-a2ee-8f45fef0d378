<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:http="http://www.w3.org/1999/xhtml"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
    <meta charset="UTF-8">
    <title>字典数据管理</title>
    <script th:replace="Importfile::html"></script>
    <script th:src="@{/scripts/security/main/openWay.js}"></script>
</head>

<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                <legend>字典类型</legend>
            </fieldset>
            <form id="modelAuthForm" lay-filter="modelAuthForm" class="layui-form model-form">
                <div class="layui-form-item">
                    <input name="id" id="id" type="hidden" th:value="${dataType?.id}">
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>字典类型</label>
                        <div class="layui-input-inline">
                            <input name="dicType" id="dicType"
                                   oninput="del(this,'blank|char')"
                                   th:value="${dataType?.dicType}" placeholder="请输入字典类型" type="text" class="layui-input" lay-verify="required|uniqueDicType|dicType">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><span style="color:red">*</span>字典代码</label>
                        <div class="layui-input-inline">
                            <input name="dicCode"
                                   oninput="del(this,'blank|char')"
                                   list="chanceCode" id="dicCode" th:value="${dataType?.dicCode}" placeholder="请输入字典代码" type="text" class="layui-input"  lay-verify="required|unique|dicCode">

                            <datalist id="chanceCode">
                            </datalist>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label label-width">备注:</label>
                        <div class="layui-input-inline">
                             <textarea name="remarks"
                                       oninput="del(this,'blank|char')"
                                       placeholder="请输入备注内容" lay-verify="remarks" id="remarks" th:text="${dataType?.remarks}" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">是否启用</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" th:attr="checked=${dataType?.isStart == 1 ? true : false}" value=1 name="isStart" id="isStart" lay-skin="switch" lay-filter="isStart" lay-text="启用|停用">
                        </div>
                    </div>
                </div>
                <button style="display:none" lay-submit lay-filter="submitBut" id="submitBut"></button>
            </form>
        </div>
    </div>
</div>
</body>
<script type="text/javascript">
    layui.use(['form', 'layer'], function () {
        var form = layui.form,
            layer = layui.layer,
            $ = layui.$;
        $.ajax({
            url:ctx + "dataTypeController/findAllDicCode",
            dataType:'json',
            success:function(data){
                var html = '';
                for(var i = 0;i < data.length;i++){
                    html += '<option value='+data[i]+'></option>'
                }
                $("#chanceCode").html(html);
            },
            error:function(data){
                console.log('查询错误!');
            }
        });
        form.render();
        form.verify({
            dicType:function(value){
                if (value.length > 50) {
                    return '字典类型不能超过50个字符';
                }
            },
            dicCode:function(value){
                if (value.length > 50) {
                    return '字典代码不能超过50个字符';
                }
            },
            unique:function(value,item){
                var id = $("#id").val();
                var checkMsg = '';
                var url = ctx +  "dataTypeController/uniqueData?dicCode="+value + "&id="+id;
                $.ajax({
                    url : url,
                    datatype : 'json',
                    async: false,
                    success : function(result) {
                        if (result) {
                            checkMsg += '字典代码重复';
                            return checkMsg;
                        }
                    },error : function() {
                        layer.msg("字典代码验证失败");
                    }
                });
                if(checkMsg != ''){
                    return checkMsg;
                }
            },
            uniqueDicType:function(value,item){
                debugger
                var id = $("#id").val();
                var checkMsg = '';
                var url = ctx +  "dataTypeController/uniqueDicType?dicType="+value + "&id="+id;
                $.ajax({
                    url : url,
                    datatype : 'json',
                    async: false,
                    success : function(result) {
                        if (result) {
                            checkMsg += '字典类型重复';
                            return checkMsg;
                        }
                    },error : function() {
                        layer.msg("字典类型验证失败");
                    }
                });
                if(checkMsg != ''){
                    return checkMsg;
                }
            },
            remarks:function(value){
                if (value.length > 200) {
                    return '备注不能超过200个字符';
                }
            },
        });
        form.on('submit(submitBut)', function(data){
            var url = ctx + "dataTypeController/saveDataType";
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'JSON',
                async: false,
                data: data.field,
                success: function(res) {
                    if(res.code == 0){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        parent.layer.msg(res.msg, { icon: 1});
                        parent.layui.table.reload("dateType_table");
                    }else{
                        layer.msg(res.msg,{icon: 2});
                    }
                },
                error:function(){
                    layer.close(loading);
                    layer.msg('操作失败',{icon: 1});
                }
            });
            return false;
        });
    });
</script>
</html>